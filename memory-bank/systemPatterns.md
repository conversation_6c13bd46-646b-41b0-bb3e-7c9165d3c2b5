# 系统架构与模式

- 后端：SpringMVC分层架构（controller/service/dao/mapper/model），MyBatis做ORM，war包部署，Maven多模块。
- 控制器层（controller）处理所有HTTP请求，REST风格为主，支持多端适配。
- 前端：JSP+静态HTML（enyan-web）、纯静态HTML（enyan-static），JS/CSS增强交互。
- 支付与内容分发高度自定义，依赖aaron系列自有模块。
- 前后端资源部分解耦，静态资源独立部署。
- 移动端适配：专有controller（如MUserController），静态H5页面（app*.html等）。

## 架构分层图

```text
[前端]
  ├── JSP/HTML/JS/CSS（enyan-web/webapp）
  ├── 纯HTML/JS/CSS（enyan-static）
[后端]
  ├── Controller（SpringMVC）
  ├── Service
  ├── DAO/Mapper（MyBatis）
  ├── Model
  └── 公共工具/安全/标签/任务

## 目录结构（细致版）
```text
enyan-web
│
├── src/main/webapp
│   ├── WEB-INF/         # JSP模板、配置
│   ├── css/ fonts/ img/ # 样式与资源
│   ├── js/              # 脚本
│   ├── *.jsp            # 动态页面
│   └── statics/         # 静态资源
│
enyan-static
│
├── *.html               # 各类静态页面（含移动端、支付、阅读等）
├── css/ fonts/ img/ js/ # 静态资源

## 包结构图（业务分层）
```text
com.aaron.spring
│
├── controller
│   ├── BookController
│   ├── ShopController
│   ├── UserController
│   └── ...（其他业务控制器）
├── controller\BookController.java 为后台管理端web接口
├── api\vxxx\controller\Rest* 为移动端restful接口
├── service
│   └── ...（业务服务）
├── dao/mapper
│   └── ...（数据访问）
├── dao/mapper/custom
│   └── ...（自定义数据访问，即无法通过MyBatis的CRUD方法实现的）
├── model
│   └── ...（数据模型）
├── common
├── interceptor
├── security
├── tags
└── task
