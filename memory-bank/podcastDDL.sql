create table pod_episode
(
    episode_id                  bigint auto_increment comment '单集ID'
        primary key,
    podcast_id                  bigint       default 0                            null comment '所属栏目ID (定义单集归属)',
    topic_id                    bigint       default 0                            null comment '所属专题ID',
    title                       varchar(255) default ''                           null comment '单集标题',
    description                 longtext                                          null comment '单集描述/文稿',
    audio_file_url              varchar(255) default ''                           null comment '音频文件URL',
    duration_seconds            int          default 0                            null comment '时长 (秒)',
    is_published                tinyint      default 0                            null comment '是否已发布',
    publication_date            timestamp                                         null comment '发布日期，转发布时自动设置，一般以此日期给用户作为识别的最新时间',
    episode_number              int          default 0                            null comment '集数, 相对整个栏目。用来栏目里展示排序顺序',
    episode_count               int          default 0                            null comment '总集数（进行冗余）',
    listen_count                int          default 0                            null comment '播放次数。有效触发播放按钮的次数，即触发播放后持续超过2秒。仅放在单集上累计',
    like_count                  int          default 0                            null comment '点赞次数。匿名点赞，可以不限用户，不限次数点击、累加',
    is_deleted                  tinyint      default 0                            null comment '是否已经删除',
    created_at                  datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间',
    cover_image_url             varchar(255)                                      null comment '封面图片URL，与所属播客的cover_image_url保持一致',
    cover_image_url2 varchar(255) NULL COMMENT '详情页面长条图片URL，与所属播客的cover_image_url2保持一致',
    cumulative_playback_seconds int          default 0                            null comment '累计播放时长 (秒) 。pod_user_episode_interaction表会记录用户播放开始时间，然后前端触发结束播放请求：有三种：暂停，播完，杀掉app后重新启动后。这三种情况要向服务更新播放时长信息。仅放在单集上累计'
)
    comment '栏目单集' collate = utf8mb4_unicode_ci;



create table pod_favorite
(
favorite_id  bigint auto_increment comment '用对栏目的收藏记录ID'
primary key,
user_id      bigint      default 0                 null comment '读者ID',
user_email   varchar(50) default ''                null comment '用户email',
podcast_id   bigint      default 0                 null comment '所属栏目ID',
favorited_at timestamp   default CURRENT_TIMESTAMP null comment '收藏时间'
)
comment '用户播客（栏目）收藏' collate = utf8mb4_unicode_ci;

create table pod_podcast
(
podcast_id       bigint auto_increment comment '播客(栏目)ID'
primary key,
title            varchar(255) default ''                           null comment '栏目标题',
author_name      varchar(255)                                      null comment '主持人/作者名',
description      longtext                                          null comment '栏目描述',
cover_image_url  varchar(255)                                      null comment '封面图片URL',
cover_image_url2 varchar(255) NULL COMMENT '详情页面长条图片URL',
display_order    int          default 0                            null comment '首页展示顺序',
episode_count    int          default 0                            null comment '总集数',
is_published     tinyint      default 0                            null comment '是否已发布',
publication_date timestamp                                         null comment '发布日期',
is_deleted       tinyint      default 0                            null comment '是否已经删除',
created_at       datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
)
comment '播客(栏目)信息' collate = utf8mb4_unicode_ci;

create table pod_topic
(
topic_id      bigint auto_increment comment '专题ID'
primary key,
podcast_id    bigint       default 0                            null comment '所属栏目ID',
title         varchar(255) default ''                           null comment '专题标题',
description   longtext                                          null comment '专题描述 (可选)',
display_order int          default 0                            null comment '显示序号，专题在播客内的展示顺序',
episode_count int          default 0                            null comment '总集数（进行冗余）',
is_deleted    tinyint      default 0                            null comment '是否已经删除',
created_at    datetime(6)  default '2020-01-01 00:00:00.000000' null comment '创建时间'
)
comment '栏目内专题' collate = utf8mb4_unicode_ci;

create table pod_user_episode_interaction
(
    interaction_id              bigint auto_increment comment '互动记录ID'
        primary key,
    user_id                     bigint      default 0                            null comment '读者ID',
    user_email                  varchar(50) default ''                           null comment '用户email',
    episode_id                  bigint      default 0                            null comment '单集ID',
    is_liked                    tinyint     default 0                            null comment '是否点赞(当前不用了，需要变为匿名点赞)',
    playback_progress_seconds   int         default 0                            null comment '播放进度 (秒) (当前不用了，只在前端记录播放进度)',
    cumulative_playback_seconds int         default 0                            null comment '累计播放时长 (秒) （这里不用了，数据要累积在单集上面）',
    last_played_at              timestamp                                        null comment '最近播放时间（日期）。用户最后一次有效播放动作的时间戳',
    is_completed                tinyint     default 0                            null comment '是否已播完。如果用户播放了最后一秒，即算是播放完成。播完后再次播放也还算是播完，这个字段再不变。',
    downloaded_at               timestamp                                        null comment '标记为下载完成的时间（当前不使用）',
    created_at                  datetime(6) default '2020-01-01 00:00:00.000000' null comment '创建时间'
)
    comment '用户单集互动记录' collate = utf8mb4_unicode_ci;






