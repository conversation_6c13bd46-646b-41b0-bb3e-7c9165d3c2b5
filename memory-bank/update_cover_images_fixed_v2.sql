-- 先创建一个临时表存储随机图片组
CREATE TEMPORARY TABLE temp_podcast_images (
    podcast_id BIGINT PRIMARY KEY,
    img_group INT
);

-- 为每个播客生成随机图片组(1-4)
INSERT INTO temp_podcast_images (podcast_id, img_group)
SELECT podcast_id, FLOOR(1 + RAND() * 4) as img_group
FROM pod_podcast
WHERE is_deleted = 0;

-- 更新pod_podcast表的封面图片
UPDATE pod_podcast p
JOIN temp_podcast_images t ON p.podcast_id = t.podcast_id
SET 
    p.cover_image_url = CONCAT('https://dl.edhub.cc/root/images/bk/bk', t.img_group, '_l.jpg'),
    p.cover_image_url2 = CONCAT('https://dl.edhub.cc/root/images/bk/bk', t.img_group, '_s.jpg')
WHERE p.is_deleted = 0;

-- 更新pod_episode表的封面图片
UPDATE pod_episode e
JOIN pod_podcast p ON e.podcast_id = p.podcast_id
SET 
    e.cover_image_url = p.cover_image_url,
    e.cover_image_url2 = p.cover_image_url2
WHERE e.is_deleted = 0 AND p.is_deleted = 0;

-- 清理临时表
DROP TEMPORARY TABLE IF EXISTS temp_podcast_images;










-- 查看播客的图片分布
SELECT
    RIGHT(cover_image_url, 7) as image_group,
    COUNT(*) as count
FROM pod_podcast
WHERE is_deleted = 0
GROUP BY image_group;

-- 检查单集图片是否与播客一致
SELECT
    e.episode_id,
    e.title,
    e.cover_image_url as episode_cover,
    e.cover_image_url2 as episode_cover2,
    p.cover_image_url as podcast_cover,
    p.cover_image_url2 as podcast_cover2
FROM pod_episode e
         JOIN pod_podcast p ON e.podcast_id = p.podcast_id
WHERE e.is_deleted = 0 AND p.is_deleted = 0
LIMIT 10;
