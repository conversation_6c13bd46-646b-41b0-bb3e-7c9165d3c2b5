-- 更新pod_podcast表的封面图片
-- 为每个播客随机分配1-4的图片组，确保cover_image_url和cover_image_url2使用相同的图片组编号
UPDATE pod_podcast 
SET 
    cover_image_url = CONCAT('https://dl.edhub.cc/root/images/bk/bk', @img_group := FLOOR(1 + RAND() * 4), '_l.jpg'),
    cover_image_url2 = CONCAT('https://dl.edhub.cc/root/images/bk/bk', @img_group, '_s.jpg')
WHERE is_deleted = 0;

-- 更新pod_episode表的封面图片
-- 确保每个单集的封面图片与其所属播客的图片组一致
UPDATE pod_episode e
JOIN pod_podcast p ON e.podcast_id = p.podcast_id
SET 
    e.cover_image_url = p.cover_image_url,
    e.cover_image_url2 = p.cover_image_url2
WHERE e.is_deleted = 0 AND p.is_deleted = 0;



-- 你可以运行以下查询来验证更新是否成功：

sql
CopyInsert
-- 验证播客图片更新
SELECT
    podcast_id,
    title,
    cover_image_url,
    cover_image_url2
FROM pod_podcast
WHERE is_deleted = 0
LIMIT 10;

-- 验证单集图片是否与所属播客一致
SELECT
    e.episode_id,
    e.title,
    e.cover_image_url as episode_cover,
    e.cover_image_url2 as episode_cover2,
    p.cover_image_url as podcast_cover,
    p.cover_image_url2 as podcast_cover2
FROM pod_episode e
         JOIN pod_podcast p ON e.podcast_id = p.podcast_id
WHERE e.is_deleted = 0 AND p.is_deleted = 0
LIMIT 10;