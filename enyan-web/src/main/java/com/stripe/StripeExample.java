package com.stripe;

import com.stripe.exception.*;
import com.stripe.model.Charge;
import com.stripe.model.Token;
import com.stripe.net.RequestOptions;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/28
 * @Modified By:
 */
public class StripeExample {
    public static void main(String[] args) {
        RequestOptions requestOptions = (new RequestOptions.RequestOptionsBuilder()).setApiKey("YOUR-SECRET-KEY").build();
        Map<String, Object> chargeMap = new HashMap<>();
        chargeMap.put("amount", 100);
        chargeMap.put("currency", "usd");//cny
        chargeMap.put("source", "tok_1234"); // obtained via Stripe.js
        try {
            Charge charge = Charge.create(chargeMap, requestOptions);
            System.out.println(charge);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }
    public void test(){
        Stripe.apiKey = "sk_test_BQokikJOvBiI2HlWgH4olfQ2";

        Map<String, Object> tokenParams = new HashMap<String, Object>();
        Map<String, Object> cardParams = new HashMap<String, Object>();
        cardParams.put("number", "****************");
        cardParams.put("exp_month", 12);
        cardParams.put("exp_year", 2018);
        cardParams.put("cvc", "314");
        tokenParams.put("card", cardParams);

        try {
            Token token = Token.create(tokenParams);

        } catch (StripeException e) {
            e.printStackTrace();
        }
    }
}
