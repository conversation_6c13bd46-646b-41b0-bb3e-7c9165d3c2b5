package com.aaron.drm.util;

import com.aaron.drm.model.Encryption;
import com.aaron.drm.model.Licenses;
import com.aaron.drm.model.User;
import com.aaron.util.FilePropertiesUtil;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-04
 * @Modified By:
 */
public class DRMUtil {
    public static final String AUTH_LCP_NAME = FilePropertiesUtil.props.getProperty("lcp.auth.lcp.name");//
    public static final String AUTH_LCP_PASSWD = FilePropertiesUtil.props.getProperty("lcp.auth.lcp.passwd");//
    public static final String AUTH_LSD_NAME = FilePropertiesUtil.props.getProperty("lcp.auth.lsd.name");//
    public static final String AUTH_LSD_PASSWD = FilePropertiesUtil.props.getProperty("lcp.auth.lsd.passwd");//

    public static final String EPUB_MASTER_DIR = FilePropertiesUtil.props.getProperty("lcp.files.master");//
    public static final String URL_FRONTEND = FilePropertiesUtil.props.getProperty("lcp.url.fontend");//
    public static final String URL_LCP = FilePropertiesUtil.props.getProperty("lcp.url.lcp");//
    public static final String URL_LSD = FilePropertiesUtil.props.getProperty("lcp.url.lsd");//

    private static final String API_GET_LICENSE_BY_PURCHASE_ID = URL_FRONTEND + "api/v1/purchases/${purchaseId}/license";
    private static final String API_POST_PUBLICATION = URL_FRONTEND + "api/v1/publications";
    private static final String API_DOWNLOAD_EPUB_BY_LICENSE = URL_LCP + "licenses/${licenseId}/publication";
    private static final String API_GET_LICENSE_BY_LICENSE = URL_LCP + "licenses/${licenseId}";
    public static final String DEFAULT_HINT = "您的email？";

    public static final String getDefaultHintPasswd(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }
        return DigestUtils.sha256Hex(input);
    }

    public static final String getRandomUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * @param purchaseId
     *
     * 根据 {purchaseId} 获取相应的license信息
     * curl http://127.0.0.1:8991/api/v1/purchases/708/license
     *
     * @Date: 2019-06-10
     */
    public static String getApiLicenseByPurchaseId(int purchaseId) {
        Map valuesMap = new HashMap();
        valuesMap.put("purchaseId", purchaseId);
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        return sub.replace(API_GET_LICENSE_BY_PURCHASE_ID);
    }

    public static String getApiLicenseByLicenseId(String licenseId) {
        Map valuesMap = new HashMap();
        valuesMap.put("licenseId", licenseId);
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        return sub.replace(API_GET_LICENSE_BY_LICENSE);
    }

    /**
     *
     * 根据{licenseId} 获取下载epub的API
     * @param licenseId
     * @Date: 2019-06-10
     */
    public static String getApiDownloadEpubByLicense(String licenseId) {
        Map valuesMap = new HashMap();
        valuesMap.put("licenseId", licenseId);
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        return sub.replace(API_DOWNLOAD_EPUB_BY_LICENSE);
    }
    /**
     *
     * 生成电子书时的API
     * @param
     * @Date: 2019-06-11
     */
    public static String getApiPostPublication() {
        return API_POST_PUBLICATION;
    }

    /**
     * @param user
     * @param encryption
     *
     *  用于下载publication时的JSON
     * @Date: 2019-06-10
     */
    public static String getJsonForPublicationByLicenseID(User user, Encryption encryption) {

        /*
        Map valuesMap = new HashMap();
        valuesMap.put("animal", "quick brown fox");
        valuesMap.put("target", "lazy dog");
        valuesMap.put("age",12);
        String templateString = "The ${animal} jumped over the ${target} ${age}.";
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        String resolvedString = sub.replace(templateString);

        System.out.println("resolvedString:"+resolvedString);*/

        Licenses licenses = new Licenses();
        licenses.setUser(user);
        licenses.setEncryption(encryption);

        return JSONObject.toJSONString(licenses);
    }

    public static void main(String[] args) {
        //37ebeda2ec15c4071fc2aa64e8770341dc46209a67d3836febb2446aebaa3529
        /*
            email wrong:<EMAIL>
            email wrong:<EMAIL>
        * */
        System.out.println(DRMUtil.getDefaultHintPasswd("<EMAIL>"));
    }
}
