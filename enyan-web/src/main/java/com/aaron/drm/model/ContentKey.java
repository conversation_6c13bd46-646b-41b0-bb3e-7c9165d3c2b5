package com.aaron.drm.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-06-04
 * @Modified By:
 */
public class ContentKey {
    private String algorithm;
    private String encryptedValue;

    @JsonProperty("algorithm")
    public String getAlgorithm() {
        return algorithm;
    }

    @JsonProperty("algorithm")
    public void setAlgorithm(String value) {
        this.algorithm = value;
    }

    @JsonProperty("encrypted_value")
    public String getEncryptedValue() {
        return encryptedValue;
    }

    @JsonProperty("encrypted_value")
    public void setEncryptedValue(String value) {
        this.encryptedValue = value;
    }
}
