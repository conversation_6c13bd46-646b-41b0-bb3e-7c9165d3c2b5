package com.aaron.excel.util;

import com.aaron.excel.ExcelRowReaderBatch;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.RedeemCodeNoteInfo;
import com.aaron.spring.service.EnyanRedeemCodeService;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * 从教务导入数据
 * @Author: <PERSON>
 * @Date: Created in  2019-11-12
 * @Modified By:
 */
public class ExcelReaderMutiFromJW extends ExcelRowReaderBatch {
    private String currentDate = DateFormatUtils.format(new Date(),"yyyyMMddHHmmss");
    private EnyanRedeemCodeService enyanRedeemCodeService;
    private List<EnyanBook> bookList;
    @Override
    public void getRows(int sheetIndex, int curRow, List<String> rowlist) {
        /*
        if (curRow<this.getBatchReadBeginRow()){
            System.out.println("--------------getRows--------------");
            System.out.println(rowlist);
        }
        if (curRow == 0){
            //
            //canInsert = false;
        }*/
    }
    @Override
    public void batchRead(List<List<String>> rowlists) {
        /*---------需要copy start-----------*/
        if (this.isInterrupted()){
            return;
        }
        super.batchRead(rowlists);
        /*---------需要copy end-----------*/
        //System.out.println("--------------batchRead--------------");
        /*
        for (List<String> rowlist : rowlists){
            if (this.getTotalRows() == 2){

                this.interruptImport();//实现中断

                this.saveRow(rowlist);//保存特定行
            }
            System.out.println(rowlist);
        }*/
        for (List<String> rowList : rowlists){
            if (rowList.size() >= 5){
                String uuid = rowList.get(0);
                String name = rowList.get(1);
                String email = rowList.get(2);
                String course = rowList.get(3);
                String courseTime = rowList.get(4);

                StringBuffer buffer = new StringBuffer();
                buffer.append("学员姓名：");
                buffer.append(name);
                buffer.append(";email：");
                buffer.append(email);
                buffer.append(";课程：");
                buffer.append(course);
                buffer.append(";时间：");
                buffer.append(courseTime);

                List<EnyanBook> newList = new ArrayList<>();
                for (EnyanBook enyanBook : this.getBookList()){
                    EnyanBook newBook = new EnyanBook();
                    newBook.setBookId(enyanBook.getBookId());
                    newBook.setBookTitle(enyanBook.getBookTitle());

                    newList.add(newBook);
                }

                RedeemCodeNoteInfo redeemCodeNoteInfo = new RedeemCodeNoteInfo();
                redeemCodeNoteInfo.setSource(EBookConstant.RedeemSource.EDUCATIONAL_DESCRIPTION);
                redeemCodeNoteInfo.setType(EBookConstant.RedeemType.SPECIAL);
                redeemCodeNoteInfo.setOtherThings(buffer.toString());
                redeemCodeNoteInfo.setBooksToRedeemList(newList);

                EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
                enyanRedeemCode.setCode(uuid);
                enyanRedeemCode.setCreateTime(currentDate);
                enyanRedeemCode.setStatus(EBookConstant.RedeemStatus.DEFAULT);
                enyanRedeemCode.setType(EBookConstant.RedeemType.SPECIAL);
                enyanRedeemCode.setNote(JSON.toJSONString(redeemCodeNoteInfo));
                enyanRedeemCode.setUserEmail(Constant.DEFAULT_EXCEL_IMPORT);

                this.enyanRedeemCodeService.addRecord(enyanRedeemCode);
            }
        }
    }

    @Override
    public void readEnd() {
        /*---------需要copy start-----------*/
        super.readEnd();
        /*---------需要copy end-----------*/
        System.out.println("ExcelReaderMutiFromJW read end, 总共有"+this.getTotalRows()+"条数据。");
        System.out.println(""+this.getSaveRowLists());//获取保存的行信息
    }

    public EnyanRedeemCodeService getEnyanRedeemCodeService() {
        return enyanRedeemCodeService;
    }

    public void setEnyanRedeemCodeService(EnyanRedeemCodeService enyanRedeemCodeService) {
        this.enyanRedeemCodeService = enyanRedeemCodeService;
    }

    public List<EnyanBook> getBookList() {
        return bookList;
    }

    public void setBookList(List<EnyanBook> bookList) {
        this.bookList = bookList;
    }
}
