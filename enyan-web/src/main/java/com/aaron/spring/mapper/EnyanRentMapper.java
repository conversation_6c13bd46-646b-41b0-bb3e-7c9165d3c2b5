package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanRent;
import com.aaron.spring.model.EnyanRentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanRentMapper {
    long countByExample(EnyanRentExample example);

    int deleteByExample(EnyanRentExample example);

    int deleteByPrimaryKey(Long rentId);

    int insert(EnyanRent record);

    int insertSelective(EnyanRent record);

    List<EnyanRent> selectByExampleWithBLOBs(EnyanRentExample example);

    List<EnyanRent> selectByExample(EnyanRentExample example);

    EnyanRent selectByPrimaryKey(Long rentId);

    int updateByExampleSelective(@Param("record") EnyanRent record, @Param("example") EnyanRentExample example);

    int updateByExampleWithBLOBs(@Param("record") EnyanRent record, @Param("example") EnyanRentExample example);

    int updateByExample(@Param("record") EnyanRent record, @Param("example") EnyanRentExample example);

    int updateByPrimaryKeySelective(EnyanRent record);

    int updateByPrimaryKeyWithBLOBs(EnyanRent record);

    int updateByPrimaryKey(EnyanRent record);
}