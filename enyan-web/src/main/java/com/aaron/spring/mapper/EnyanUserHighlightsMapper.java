package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanUserHighlights;
import com.aaron.spring.model.EnyanUserHighlightsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanUserHighlightsMapper {
    long countByExample(EnyanUserHighlightsExample example);

    int deleteByExample(EnyanUserHighlightsExample example);

    int deleteByPrimaryKey(String highlightId);

    int insert(EnyanUserHighlights record);

    int insertSelective(EnyanUserHighlights record);

    List<EnyanUserHighlights> selectByExample(EnyanUserHighlightsExample example);

    EnyanUserHighlights selectByPrimaryKey(String highlightId);

    int updateByExampleSelective(@Param("record") EnyanUserHighlights record, @Param("example") EnyanUserHighlightsExample example);

    int updateByExample(@Param("record") EnyanUserHighlights record, @Param("example") EnyanUserHighlightsExample example);

    int updateByPrimaryKeySelective(EnyanUserHighlights record);

    int updateByPrimaryKey(EnyanUserHighlights record);
}