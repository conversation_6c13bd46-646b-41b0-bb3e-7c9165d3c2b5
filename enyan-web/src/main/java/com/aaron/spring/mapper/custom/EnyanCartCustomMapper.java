package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanCartCustomMapper {

    long searchCountByExample(@Param("record") EnyanCart record, @Param("example") EnyanCartExample example);
    List<EnyanBook> searchByExample(@Param("record") EnyanCart record, @Param("example") EnyanCartExample example);

    List<EnyanBook> searchAllByEmail(@Param("email") String email);

    @Delete({"delete from enyan_cart " +
                     " where user_email = #{email} and book_id = #{bookId}"})
    int deleteByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Delete({"<script>delete from enyan_cart " +
                     "where user_email = #{email} " +
                     "and book_id in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
                     "</script>"})
    int deleteCarts(@Param("email")String email, @Param("ids")Long[] ids);

    @Select("select COUNT(1) from enyan_cart where user_email = #{email}")
    long countOfCartByEmail(@Param("email")String email);

    @Select("select COUNT(1) from enyan_cart where user_email = #{email} and book_id = #{bookId}")
    long countOfCartByEmailAndBookId(@Param("email")String email, @Param("bookId")Long bookId);

    @Delete({"delete from enyan_cart where user_email = #{email}"})
    int deleteCartsAllByEmail(@Param("email")String email);

    @Insert("insert into enyan_cart (cart_id, user_email, book_id, quantity, add_at) values (#{record.cartId}, #{record.userEmail}, #{record.bookId}, #{record.quantity}, #{record.addAt})")
    int addCart(@Param("record")EnyanCart record);

    @Update({"update enyan_cart set user_email = #{revokedEmail} where user_email = #{email}"})
    int revokeUser(@Param("email")String email, @Param("revokedEmail")String revokedEmail);
}