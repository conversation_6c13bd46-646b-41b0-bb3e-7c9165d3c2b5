package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanBlog;
import com.aaron.spring.model.EnyanBlogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanBlogMapper {
    long countByExample(EnyanBlogExample example);

    int deleteByExample(EnyanBlogExample example);

    int deleteByPrimaryKey(Long blogId);

    int insert(EnyanBlog record);

    int insertSelective(EnyanBlog record);

    List<EnyanBlog> selectByExampleWithBLOBs(EnyanBlogExample example);

    List<EnyanBlog> selectByExample(EnyanBlogExample example);

    EnyanBlog selectByPrimaryKey(Long blogId);

    int updateByExampleSelective(@Param("record") EnyanBlog record, @Param("example") EnyanBlogExample example);

    int updateByExampleWithBLOBs(@Param("record") EnyanBlog record, @Param("example") EnyanBlogExample example);

    int updateByExample(@Param("record") EnyanBlog record, @Param("example") EnyanBlogExample example);

    int updateByPrimaryKeySelective(EnyanBlog record);

    int updateByPrimaryKeyWithBLOBs(EnyanBlog record);

    int updateByPrimaryKey(EnyanBlog record);
}