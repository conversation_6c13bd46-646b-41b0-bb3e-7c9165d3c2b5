package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanRentDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/*
CREATE TABLE `enyan_rent_detail`
(
    detail_id           BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_num                 VARCHAR(40)  DEFAULT '' NULL COMMENT '订单号',
    user_email     varchar(96) DEFAULT '' NULL COMMENT '用户email',
    rent_type     INT DEFAULT '0'   NULL COMMENT '套餐类型，1：新旧约；2：旧约；3：新约；',
    rent_lang     INT DEFAULT '0'   NULL COMMENT '语言类型，1：简体；2：繁体；3：英语；',
    is_auto       INT    DEFAULT '0' NULL COMMENT '续订状态，0：手动订阅；1：自动订阅-仅支持信用卡；',
    publisher_id              BIGINT           DEFAULT '0' NULL COMMENT '版权商ID',
    rate_value                VARCHAR(20) DEFAULT '' NULL COMMENT '汇率值',
    #fee                     DECIMAL(10, 2) DEFAULT '0.00' NULL COMMENT '价格',
    rent_months                  INT   DEFAULT '1' NULL COMMENT '本次订阅月份数',
    vendor_percent            INT          DEFAULT '0' NULL COMMENT 'Vendor比例（版税税率）new',
    income_vendor             DECIMAL(10, 2) DEFAULT '0.00' NULL COMMENT 'Vendor收益（版税金额）',
    income_plat               DECIMAL(10, 2) DEFAULT '0.00' NULL COMMENT '平台收益',
    income_total              DECIMAL(10, 2) DEFAULT '0.00' NULL COMMENT '总收益（销售额）',
    income_real               DECIMAL(10, 2) DEFAULT '0.00' NULL COMMENT '真实本币（人民币或美元）总收益',
    pay_fee                   DECIMAL(10, 2) DEFAULT '0.00' NULL COMMENT '支付手续费new',
    net_sales                 DECIMAL(10, 2) DEFAULT '0.00' NULL COMMENT '净收益new',
    order_currency            INT    DEFAULT '0' NULL COMMENT '订单币种，0：人民币；1：美元;2:港币',
    pay_type                  INT DEFAULT '0'   NULL COMMENT '支付类型，1：alipay；2：信用卡',
    pay_country                   VARCHAR(10)   DEFAULT '' NULL COMMENT '付费的国家',
    order_from          INT DEFAULT '0'   NULL COMMENT '订单来源，0：网站；1：App',
    purchased_at   DATETIME(6)   DEFAULT '2022-01-01 00:00:00' NULL COMMENT '购买时间',
    from_at   DATETIME(6)   DEFAULT '2022-01-01 00:00:00' NULL COMMENT '上次开始时间',
    expired_at     DATETIME(6)   DEFAULT '2022-01-01 00:00:00' NULL COMMENT '过期时间',
    create_at      DATETIME(6)   DEFAULT '2022-01-01 00:00:00' NULL COMMENT '创建时间'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
* */
@Repository
public interface EnyanRentDetailCustomMapper {

    @Select("select sum(rent_months) as rent_months, sum(income_total) as income_total from enyan_rent_detail" +
                    " where order_num = #{orderNum} and user_email = #{email}")
    List<EnyanRentDetail> findSumMonthsByRent(@Param("orderNum") String orderNum, @Param("email") String email);

    @Select("select count(1) from enyan_rent_detail where order_num = #{orderNum} and TO_DAYS(NOW()) = TO_DAYS(purchased_at)")
    long countOfTodayOrder(@Param("orderNum") String orderNum);
}