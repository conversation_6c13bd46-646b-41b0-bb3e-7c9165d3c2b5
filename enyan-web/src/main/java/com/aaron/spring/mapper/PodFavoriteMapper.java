package com.aaron.spring.mapper;

import com.aaron.spring.model.PodFavorite;
import com.aaron.spring.model.PodFavoriteExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PodFavoriteMapper {
    long countByExample(PodFavoriteExample example);

    int deleteByExample(PodFavoriteExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PodFavorite record);

    int insertSelective(PodFavorite record);

    List<PodFavorite> selectByExample(PodFavoriteExample example);

    PodFavorite selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") PodFavorite record, 
                               @Param("example") PodFavoriteExample example);

    int updateByExample(@Param("record") PodFavorite record, 
                      @Param("example") PodFavoriteExample example);

    int updateByPrimaryKeySelective(PodFavorite record);

    int updateByPrimaryKey(PodFavorite record);

    List<PodFavorite> selectByUserIdAndType(@Param("userId") Long userId,
                                          @Param("favoriteType") Integer type);
                                          
    int batchUpsert(@Param("list") List<PodFavorite> favorites);

    int countByUserId(@Param("userId") Long userId);
}
