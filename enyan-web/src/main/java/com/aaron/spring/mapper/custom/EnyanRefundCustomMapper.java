package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanRefund;
import com.aaron.spring.model.EnyanRefundExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanRefundCustomMapper {
    long countByExample(EnyanRefundExample example);

    int deleteByExample(EnyanRefundExample example);

    int deleteByPrimaryKey(Long refundId);

    int insert(EnyanRefund record);

    int insertSelective(EnyanRefund record);

    List<EnyanRefund> selectByExample(EnyanRefundExample example);

    EnyanRefund selectByPrimaryKey(Long refundId);

    int updateByExampleSelective(@Param("record") EnyanRefund record, @Param("example") EnyanRefundExample example);

    int updateByExample(@Param("record") EnyanRefund record, @Param("example") EnyanRefundExample example);

    int updateByPrimaryKeySelective(EnyanRefund record);

    int updateByPrimaryKey(EnyanRefund record);
}