package com.aaron.spring.mapper;

import com.aaron.spring.model.EnyanCategory;
import com.aaron.spring.model.EnyanCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnyanCategoryMapper {
    long countByExample(EnyanCategoryExample example);

    int deleteByExample(EnyanCategoryExample example);

    int deleteByPrimaryKey(Long categoryId);

    int insert(EnyanCategory record);

    int insertSelective(EnyanCategory record);

    List<EnyanCategory> selectByExample(EnyanCategoryExample example);

    EnyanCategory selectByPrimaryKey(Long categoryId);

    int updateByExampleSelective(@Param("record") EnyanCategory record, @Param("example") EnyanCategoryExample example);

    int updateByExample(@Param("record") EnyanCategory record, @Param("example") EnyanCategoryExample example);

    int updateByPrimaryKeySelective(EnyanCategory record);

    int updateByPrimaryKey(EnyanCategory record);
}