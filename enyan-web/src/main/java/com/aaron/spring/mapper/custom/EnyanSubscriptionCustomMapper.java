package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanCoupon;
import com.aaron.spring.model.EnyanSubscription;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanSubscriptionCustomMapper {
    @Select("select * from enyan_subscription")
    List<EnyanSubscription> findAllRecores();
}