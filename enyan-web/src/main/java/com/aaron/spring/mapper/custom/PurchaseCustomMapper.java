package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.Purchase;
import com.aaron.spring.model.PurchaseExample;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PurchaseCustomMapper {
    /**
     *
     * 因为更换了DR吗，更新购买记录的DRM匹配
     * @param newPublicationId
     * @param oldPublicationId
     * @Date: 2020-06-22
     */
    int updatePurchaseToNewPublicationId(@Param("newPublicationId") Integer newPublicationId, @Param("oldPublicationId") Integer oldPublicationId);

    @Delete({"delete from purchase where user_email = #{email}"})
    int deleteAllByEmail(@Param("email")String email);

    @Select("select * from purchase where publication_id = #{publicationId} and user_id = #{userId}")
    List<Purchase> findRecordsByUserIDAndPublicationId(@Param("userId")Integer userId, @Param("publicationId")Integer publicationId);
}