package com.aaron.spring.mapper;

import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.model.PodPodcastExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PodPodcastMapper {
    long countByExample(PodPodcastExample example);

    int deleteByExample(PodPodcastExample example);

    int deleteByPrimaryKey(Long podcastId);

    int insert(PodPodcast record);

    int insertSelective(PodPodcast record);

    List<PodPodcast> selectByExample(PodPodcastExample example);

    PodPodcast selectByPrimaryKey(Long podcastId);

    int updateByExampleSelective(@Param("record") PodPodcast record, @Param("example") PodPodcastExample example);

    int updateByExample(@Param("record") PodPodcast record, @Param("example") PodPodcastExample example);

    int updateByPrimaryKeySelective(PodPodcast record);

    int updateByPrimaryKey(PodPodcast record);
}
