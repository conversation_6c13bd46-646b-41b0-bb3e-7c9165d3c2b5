package com.aaron.spring.mapper.custom;

import com.aaron.spring.model.EnyanBalance;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnyanBalanceCustomMapper {
    List<EnyanBalance> findBalanceByIds(@Param("balanceIdList") List<Long> balanceIdList, @Param("publisherId") Long publisherId);

    int updateBalanceStatusByIds(@Param("balanceIdList") List<Long> balanceIdList, @Param("publisherId") Long publisherId, @Param("isCounted") Byte isCounted);
}