package com.aaron.spring.security;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.security.function.domain.IAntMatcher;
import org.springframework.stereotype.Component;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/11/30
 * @Modified By:
 */
@Component
public class AntMatcher implements IAntMatcher{
    private static final String[] ADMIN = {"/test/**"};
    private static final String[] USER = {"/", "/home","/**/myCenter","/**/myCart","/**/myOrders","/**/myRent","/**/orderDetail-**",
            "/**/myWishes","/**/myDevices","/**/cart-**","/**/preDownload-**","/**/myGiftHistory"};
    private static final String[] VENDOR = {"/","/vendor/**","/refund/**"};
    private static final String[] OPERATION = {"/admin/**","/book/**","/category/**","/publisher/**","/lcp/**","/config/**",
            "/adminImg/**", "/adminRedeemCode/**","/discount/**","/test/**","/spirit/**",
            "/coupon/**", "/blog/**", "/banner/**", "/reading/**","/dailyWords/**","/bookSetAdmin/**","/bookListAdmin/**",
            "/feedbackAdmin/**", "/highlightsAdmin/**", "/commentAdmin/**"};
    private static final String[] FINANCE = {"/order/**","/rentAdmin/**", "/dataStat/**", "/dataRentStat/**"};
    private static final String[] STAFF = {"/", "/home"};
    //private static final List<String> STAFF = Arrays.asList("/", "/home");
    private static final String[] DBA = {"/db/**"};
    private static final String[] API = {"/api/**","/front/**"};
    private static final String[] CSRF_IGNORING_URL = {"/shop/enyanMyNotify","/enyanMyNotify","/checkin/importExcelAction"};//
    private static final String[] WEB_IGNORING_URL = {"/statics/**","/book_image/**","/ckeditor/**","/css/**",
            "/images/**","/js/**","/fonts/**","/img/**"};
    @Override
    public String[] getAntMatchers(String roleName) {
        switch (roleName){
            case InterfaceContant.RoleName.ROLE_ADMIN:
                return ADMIN;
            case InterfaceContant.RoleName.ROLE_DBA:
                return DBA;
            case InterfaceContant.RoleName.ROLE_STAFF:
                return STAFF;
            case InterfaceContant.RoleName.ROLE_USER:
                return USER;
            case InterfaceContant.RoleName.ROLE_VENDOR:
                return VENDOR;
            case InterfaceContant.RoleName.ROLE_OPERATION:
                return OPERATION;
            case InterfaceContant.RoleName.ROLE_FINANCE:
                return FINANCE;
            case InterfaceContant.RoleName.ROLE_API:
                return API;
        }
        return USER;
    }

    @Override
    public String[] getCsrfIgnoringURL() {
        return CSRF_IGNORING_URL;
    }

    @Override
    public String[] getWebSecurityIgnoringURL() {
        return WEB_IGNORING_URL;
    }

    @Override
    public boolean useSecurity() {
        return true;
    }

    @Override
    public boolean createPersistentTokenTableOnStartup() {
        return false;
    }

    @Override
    public boolean useCommonPasswordEncoder() {
        return false;
    }
}
