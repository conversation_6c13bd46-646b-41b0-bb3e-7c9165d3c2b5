package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanRent;
import com.aaron.spring.service.EnyanRentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/1/21
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/rentAdmin")
public class RentController extends BaseController{
	@Resource
	private EnyanRentService enyanRentService;

	@RequestMapping(value = "/list")
	public String list(HttpServletRequest req, EnyanRent record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		if (null == record){
			record = new EnyanRent();
		}
		if (null == record.getPage()){
			record.setPage(new Page());
		}
		Map<String, Object> queryParams = new HashMap<>();

		// 获取分页参数
		String total = req.getParameter("total");
		String currentPage = req.getParameter("pageNo");

		if (StringUtils.hasLength(total)) {
			record.getPage().setTotalRecord(Integer.parseInt(total));
		}
		if (StringUtils.hasLength(currentPage)) {
			record.getPage().setCurrentPage(Integer.parseInt(currentPage));
		}


		// 高级查询条件：
		String searchText = req.getParameter("searchText");
		String searchType = req.getParameter("searchType");
		if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
			switch (searchType){
				case "0":
					record.setUserEmail(searchText);
					break;
				case "1":
					break;

			}
			queryParams.put("searchText",searchText);
			queryParams.put("searchType",searchType);
		}

		record.addOrder(new OrderObj("create_at", InterfaceContant.OrderBy.DESC));
//        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
		Page<EnyanRent> page = enyanRentService.queryRecords(record.getPage(),record);
		record.setPage(page);
		record.excutePageLand(queryParams);

		modelMap.addAttribute("list",page.getRecords());
		modelMap.addAttribute("pageLand",record.getPageLand());
		modelMap.addAttribute("dto",record);
		modelMap.addAttribute("explan","文章管理");

		return "admin/rents";
	}

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		EnyanRent record = enyanRentService.queryRecordByPrimaryKey(id).getResult();

		modelMap.addAttribute("record",record);
		modelMap.addAttribute("publisherList", Constant.publishersList);
		return "admin/blogAdd";
	}

	@RequestMapping("/del-{id}")
	public String delById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("method delById");
		enyanRentService.deleteRecordByPrimaryKey(id);
		return "redirect:/blog/list";
	}

	@RequestMapping("/addUI")
	public String addCompanyUI(EnyanRent record, ModelMap modelMap){
		log.debug("method addCompanyUI");
		record.setIsDeleted(0);
		modelMap.addAttribute("publisherList",Constant.publishersList);
		modelMap.addAttribute("record",record);
		return "admin/blogAdd";
	}

}
