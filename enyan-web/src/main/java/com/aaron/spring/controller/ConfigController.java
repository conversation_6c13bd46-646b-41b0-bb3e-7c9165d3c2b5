package com.aaron.spring.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanCategory;
import com.aaron.spring.model.EnyanConfig;
import com.aaron.spring.service.EnyanConfigService;
import com.aaron.util.ExecuteResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/12/6
 * @Modified By:
 */
@Controller
@RequestMapping("/config")
public class ConfigController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(ConfigController.class);
    public static final int CONF_ID_SECRIT_CODE = 251; //安全验证码
    public static final int CONF_ID_SPIRIT_TIME = 252; //灵修材料更新时间
    public static final int CONF_ID_LATEST_NOTICE_TIME = 253; //最新消息更新时间


    @Resource
    private EnyanConfigService enyanConfigService;

    @RequestMapping(value = "/configs")
    public String booksPage(HttpServletRequest req, EnyanConfig enyanConfig, ModelMap modelMap){
        //logger.info("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
        if (null == enyanConfig){
            enyanConfig = new EnyanConfig();
        }
        if (null == enyanConfig.getPage()){
            enyanConfig.setPage(new Page());
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            enyanConfig.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            enyanConfig.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件：
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType){
                case "0":
                    enyanConfig.setConfigDescription(searchText);
                    break;
            }
            queryParams.put("searchText",searchText);
            queryParams.put("searchType",searchType);
        }

        enyanConfig.setIsShow(Constant.BYTE_VALUE_1);
        /*
        Page<EnyanBook> page = enyanConfigService.queryRecords(enyanConfig.getPage(),enyanConfig);
        enyanConfig.setPage(page);
        enyanConfig.excutePageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",enyanConfig.getPageLand());
        modelMap.addAttribute("enyanConfig",enyanConfig);
        modelMap.addAttribute("explan","配置管理");*/

        List<EnyanConfig> list = enyanConfigService.findConfigs(enyanConfig);
        List<EnyanConfig> moneyList = new ArrayList<>();
        List<EnyanConfig> refreshList = new ArrayList<>(); //可以刷新的数据
        List<EnyanConfig> adList = new ArrayList<>(); //广告数据
        List<EnyanConfig> couponList = new ArrayList<>(); //优惠码数据
        List<EnyanConfig> spiritList = new ArrayList<>(); //优惠码数据
        for (EnyanConfig tmp: list){
            if (tmp.getConfigId() >= 401){// 401-450 广告的信息
                spiritList.add(tmp);
                continue;
            }
            if (tmp.getConfigId() >= 351){// 351-353 广告的信息
                couponList.add(tmp);
                continue;
            }
            if (tmp.getConfigId() >= 301){// 301-309 广告的信息
                adList.add(tmp);
                continue;
            }
            if (tmp.getConfigId() >= 251){//可以刷新的数据
                refreshList.add(tmp);
                continue;
            }
            if (tmp.getConfigId() >= 200){ // 200-250 货币
                moneyList.add(tmp);
                continue;
            }
        }
        modelMap.addAttribute("moneyList",moneyList);
        modelMap.addAttribute("refreshList",refreshList);
        modelMap.addAttribute("adList",adList);
        modelMap.addAttribute("couponList",couponList);
        modelMap.addAttribute("enyanConfig",enyanConfig);
        modelMap.addAttribute("spiritList",spiritList);
        modelMap.addAttribute("explan","配置管理");

        return "admin/configs";
    }
    @ResponseBody
    @RequestMapping(value = "/saveConfig", method = RequestMethod.POST)
    public ExecuteResult<EnyanConfig> saveConfig(@RequestBody EnyanConfig enyanConfig){
        logger.debug("method saveConfig："+enyanConfig);

        ExecuteResult<EnyanConfig> result = new ExecuteResult<>();

        if (null == enyanConfig.getConfigId() || StringUtils.hasLength(enyanConfig.getConfigValue()) == false){
            result.addErrorMessage("请选择配置属性值");
            return result;
        }
        enyanConfig.setConfigValue(enyanConfig.getConfigValue());
        result = enyanConfigService.updateRecord(enyanConfig);

        return result;
    }

    @ResponseBody
    @RequestMapping(value = "/saveRefreshConfig", method = RequestMethod.POST)
    public ExecuteResult<EnyanConfig> saveRefreshConfig(@RequestBody EnyanConfig enyanConfig){
        logger.debug("method saveRefreshConfig："+enyanConfig);

        ExecuteResult<EnyanConfig> result = new ExecuteResult<>();

        if (null == enyanConfig.getConfigId()){
            result.addErrorMessage("请选择配置属性值");
            return result;
        }

        switch (enyanConfig.getConfigId().intValue()){
            case CONF_ID_SECRIT_CODE:
                enyanConfig.setConfigValue(UUID.randomUUID().toString().toUpperCase());
                break;
            case CONF_ID_SPIRIT_TIME:
                enyanConfig.setConfigValue(String.valueOf(System.currentTimeMillis()));
                break;
            case CONF_ID_LATEST_NOTICE_TIME:
                enyanConfig.setConfigValue(String.valueOf(System.currentTimeMillis()));
                break;
        }

        result = enyanConfigService.updateRecord(enyanConfig);

        return result;
    }
}
