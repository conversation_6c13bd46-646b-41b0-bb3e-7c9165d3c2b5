package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.NameAndValue;
import com.aaron.common.OrderObj;
import com.aaron.excel.ExcelImportUtil;
import com.aaron.excel.util.ExcelReaderMutiFromDailyWords;
import com.aaron.excel.util.ExcelReaderMutiFromJW;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.SystemUtil;
import com.aaron.spring.common.ZipUtil;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanDailyWords;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.service.EnyanDailyWordsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.text.ParseException;
import java.util.*;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2022/6/23
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/dailyWords")
public class DailyWordsController extends BaseController{
	@Resource
	private EnyanDailyWordsService enyanDailyWordsService;

	@RequestMapping(value = "/list")
	public String list(HttpServletRequest req, EnyanDailyWords record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		if (null == record){
			record = new EnyanDailyWords();
		}
		if (null == record.getPage()){
			record.setPage(new Page());
		}
		Map<String, Object> queryParams = new HashMap<>();

		// 获取分页参数
		String total = req.getParameter("total");
		String currentPage = req.getParameter("pageNo");

		if (StringUtils.hasLength(total)) {
			record.getPage().setTotalRecord(Integer.parseInt(total));
		}
		if (StringUtils.hasLength(currentPage)) {
			record.getPage().setCurrentPage(Integer.parseInt(currentPage));
		}

		// 高级查询条件：
		String searchText = req.getParameter("searchText");
		String searchType = req.getParameter("searchType");
		if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
			//record.setDataName(searchText);
			switch (searchType){
				case "0":
					record.setDataContent(searchText);
					break;
				case "1":
					//record.setDataIndexShow(1);
					break;
				case "2":
					//record.setDataReadShow(1);
					break;
			}
			queryParams.put("searchText",searchText);
			queryParams.put("searchType",searchType);
		}

        record.addOrder(new OrderObj("data_at", InterfaceContant.OrderBy.DESC));
//        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
		Page<EnyanDailyWords> page = enyanDailyWordsService.queryRecords(record.getPage(),record);
		record.setPage(page);
		record.excutePageLand(queryParams);

		modelMap.addAttribute("list",page.getRecords());
		modelMap.addAttribute("pageLand",record.getPageLand());
		modelMap.addAttribute("dto",record);
		modelMap.addAttribute("explan","banner管理");

		return "admin/dailyWordsList";
	}

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		EnyanDailyWords record = enyanDailyWordsService.queryRecordByPrimaryKey(id).getResult();
//		String rangeDate = DateFormatUtils.format(record.getBeginAt(),"yyyy/MM/dd HH:mm:ss")+" - "
//				                   + DateFormatUtils.format(record.getEndAt(),"yyyy/MM/dd HH:mm:ss");
//		record.setRangeDate(rangeDate);

		/*
		List<NameAndValue> selectedList;
		if (enyanDiscount.getDiscountType().equals(new Byte("2"))){//单个折扣
			selectedList = enyanBookService.findBookNameAndValueByDiscountSingleId(id);
		}else {
			selectedList = enyanBookService.findBookNameAndValueByDiscountId(id);
		}
		String[] ids = new String[selectedList.size()];
		for (int i = 0; i < selectedList.size(); i++) {
			ids[i] = selectedList.get(i).getValue();
		}*/
		String[] ids = {record.getBookId()+""};
		record.setBookIDs(ids);

		modelMap.addAttribute("record",record);
		modelMap.addAttribute("bookIDsList", Constant.booksList);
		return "admin/dailyWordsAdd";
	}

	@RequestMapping("/del-{id}")
	public String delById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("method delById");
		//enyanDailyWordsService.deleteRecordByPrimaryKey(id);
		return "redirect:/dailyWords/list";
	}

	@RequestMapping("/reset")
	public String reset(ModelMap modelMap){
		log.debug("method reset");
		enyanDailyWordsService.initDailywords();
		return "redirect:/dailyWords/list";
	}

	@RequestMapping("/nextBatch")
	public String nextBatch(ModelMap modelMap){
		log.debug("method reset");
		enyanDailyWordsService.createDailyWordsNextBatch();
		return "redirect:/dailyWords/list";
	}

	@RequestMapping("/addUI")
	public String addUI(EnyanDailyWords record, ModelMap modelMap){
		log.debug("method addUI");
		modelMap.addAttribute("record",record);
		modelMap.addAttribute("bookIDsList", Constant.booksList);
		return "admin/dailyWordsAdd";
	}

	@RequestMapping("/excelUI")
	public String excelUI(EnyanDailyWords record, ModelMap modelMap){
		log.debug("method excelUI");
		modelMap.addAttribute("record",record);
		return "admin/dailyWordsExcel";
	}

	@RequestMapping("/zipUI")
	public String zipUI(EnyanDailyWords record, ModelMap modelMap){
		log.debug("method imageUI");
		modelMap.addAttribute("record",record);
		return "admin/dailyWordsZip";
	}

	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public String save(HttpServletRequest request, EnyanDailyWords record, ModelMap modelMap){
		log.debug("method save："+record);
		modelMap.addAttribute("bookIDsList", Constant.booksList);

		if (StringUtils.hasLength(record.getDataContent()) == false){
			this.setErrorMsg(modelMap,"请填写金句内容");
			modelMap.addAttribute("record",record);
			return "admin/dailyWordsAdd";
		}
		if (StringUtils.hasLength(record.getDataImgUrl()) == false){
			this.setErrorMsg(modelMap,"请填写图片的Url");
			modelMap.addAttribute("record",record);
			return "admin/dailyWordsAdd";
		}
		String[] ids = record.getBookIDs();
		if (null == ids || ids.length == 0){
			this.setErrorMsg(modelMap,"请填写转向的书籍");
			modelMap.addAttribute("record",record);
			return "admin/dailyWordsAdd";
		}
		record.setBookId(Long.parseLong(ids[0]));
		if (null == record.getDataId()){
			if (null == record.getDataAt()){
				this.setErrorMsg(modelMap,"请选择时间信息");
				modelMap.addAttribute("record",record);
				return "admin/dailyWordsAdd";
			}
			List<EnyanDailyWords> list = enyanDailyWordsService.findRecordsByDateAt(record.getDataAt());
			if (list.isEmpty() == false){
				this.setErrorMsg(modelMap,record.getDataAt() + "的日历已经存在");
				modelMap.addAttribute("record",record);
				return "admin/dailyWordsAdd";
			}

			Date currentDate = new Date();
			this.setSuccessMsg(modelMap,"添加金句成功");
			record.setCreateAt(currentDate);
			record.setLikeCount(0);
			enyanDailyWordsService.addRecord(record);
		}else {
			this.setSuccessMsg(modelMap,"修改金句成功");
			enyanDailyWordsService.updateRecord(record);
		}
		return "redirect:/dailyWords/list";
	}

	@RequestMapping(value = "/excel", method = RequestMethod.POST)
	public String excel(@RequestParam(value = "uploadExcel", required = false) MultipartFile filedataPic,
	                         ModelMap modelMap) {
		if (filedataPic == null || filedataPic.isEmpty()) {
			this.setErrorMsg(modelMap, "请选择Excel！");
			return "admin/dailyWordsExcel";
		}
		File picFile ;
		try {
			// 获取图片的文件名
			String fileName = filedataPic.getOriginalFilename();
			// 获取图片的扩展名
			String extensionName = fileName.substring(fileName.lastIndexOf(".") + 1);

			// 新的图片文件名 = 获取时间戳+"."图片扩展名
			String newFileName = fileName+"_"+ System.currentTimeMillis() + "." + extensionName;

			//enyanImgService.addRecord(enyanImg);

			String filePath = SystemUtil.getExcelDir()+newFileName;
			picFile = new File(filePath);
			filedataPic.transferTo(picFile);
			//picFile.setWritable(true);//设置可写权限
			//picFile.setExecutable(true);//设置可执行权限
			//picFile.setReadable(true);//设置可读权限

			Runtime.getRuntime().exec("chmod 777 -R " + filePath);

			ExcelReaderMutiFromDailyWords mutiFrom = new ExcelReaderMutiFromDailyWords();
			mutiFrom.setBatchReadBeginRow(1);//从1开始
			mutiFrom.setEnyanDailyWordsService(enyanDailyWordsService);
			ExcelImportUtil.readExcel(filePath, mutiFrom, mutiFrom, mutiFrom.getBatchReadBeginRow());

			log.error("game over...");

		} catch (Exception e) {
			log.error("上传图片失败.", e);
			modelMap.addAttribute("msg", "上传Excel失败！");
			this.setErrorMsg(modelMap, "上传Excel失败！");

			return "admin/dailyWordsExcel";
		}
		this.setSuccessMsg(modelMap, "添加兑换码成功！");
		return "redirect:/dailyWords/list";
	}

	@RequestMapping(value = "/zip", method = RequestMethod.POST)
	public String zip(@RequestParam(value = "uploadExcel", required = false) MultipartFile filedataPic,
	                         ModelMap modelMap) {
		if (filedataPic == null || filedataPic.isEmpty()) {
			this.setErrorMsg(modelMap, "请选择Zip！");
			return "admin/dailyWordsZip";
		}
		File picFile ;
		try {
			// 获取图片的文件名
			String fileName = filedataPic.getOriginalFilename();
			// 获取图片的扩展名
			String extensionName = fileName.substring(fileName.lastIndexOf(".") + 1);

			// 新的图片文件名 = 获取时间戳+"."图片扩展名
			String newFileName = fileName+"_"+ System.currentTimeMillis() + "." + extensionName;

			//enyanImgService.addRecord(enyanImg);

			String filePath = SystemUtil.getExcelDir()+fileName;
			picFile = new File(filePath);
			filedataPic.transferTo(picFile);
			//picFile.setWritable(true);//设置可写权限
			//picFile.setExecutable(true);//设置可执行权限
			//picFile.setReadable(true);//设置可读权限

			String destPath = SystemUtil.getDailyWordsImageDir();
			String tmpPath = SystemUtil.getTmpDir();

			List<File> fileList = ZipUtil.upzipFile(filePath, tmpPath);
			for (File file:fileList){
				FileUtils.copyFileToDirectory(file, new File(destPath));
			}

			Runtime.getRuntime().exec("chmod 777 -R " + destPath);

		} catch (Exception e) {
			log.error("上传图片失败.", e);
			modelMap.addAttribute("msg", "上传Excel失败！");
			this.setErrorMsg(modelMap, "上传Excel失败！");

			return "admin/dailyWordsExcel";
		}
		this.setSuccessMsg(modelMap, "添加兑换码成功！");
		return "redirect:/dailyWords/list";
	}
}
