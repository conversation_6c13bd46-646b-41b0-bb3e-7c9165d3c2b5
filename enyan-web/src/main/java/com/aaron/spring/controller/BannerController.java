package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestFeedback;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBanner;
import com.aaron.spring.model.EnyanFeedback;
import com.aaron.spring.service.EnyanBannerService;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2022/6/23
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/banner")
public class BannerController extends BaseController{
	@Resource
	private EnyanBannerService enyanBannerService;

	@Resource
	private EnyanBookService enyanBookService;

	@RequestMapping(value = "/list")
	public String list(HttpServletRequest req, EnyanBanner record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		if (null == record){
			record = new EnyanBanner();
		}
		if (null == record.getPage()){
			record.setPage(new Page());
		}
		Map<String, Object> queryParams = new HashMap<>();

		// 获取分页参数
		String total = req.getParameter("total");
		String currentPage = req.getParameter("pageNo");

		if (StringUtils.hasLength(total)) {
			record.getPage().setTotalRecord(Integer.parseInt(total));
		}
		if (StringUtils.hasLength(currentPage)) {
			record.getPage().setCurrentPage(Integer.parseInt(currentPage));
		}

		// 高级查询条件：
		String searchText = req.getParameter("searchText");
		String searchType = req.getParameter("searchType");
		if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
			record.setDataName(searchText);
			switch (searchType){
				case "0":

					break;
				case "1":
					record.setDataIndexShow(1);
					break;
				case "2":
					record.setDataReadShow(1);
					break;
			}
			queryParams.put("searchText",searchText);
			queryParams.put("searchType",searchType);
		}

        record.addOrder(new OrderObj("data_priority", InterfaceContant.OrderBy.DESC));
//        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
		Page<EnyanBanner> page = enyanBannerService.queryRecords(record.getPage(),record);
		record.setPage(page);
		record.excutePageLand(queryParams);

		modelMap.addAttribute("list",page.getRecords());
		modelMap.addAttribute("pageLand",record.getPageLand());
		modelMap.addAttribute("dto",record);
		modelMap.addAttribute("explan","banner管理");

		return "admin/bannerList";
	}

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		EnyanBanner record = enyanBannerService.queryRecordByPrimaryKey(id).getResult();
		String rangeDate = DateFormatUtils.format(record.getBeginAt(),"yyyy/MM/dd HH:mm:ss")+" - "
				                   + DateFormatUtils.format(record.getEndAt(),"yyyy/MM/dd HH:mm:ss");
		record.setRangeDate(rangeDate);

		modelMap.addAttribute("record",record);
		return "admin/bannerAdd";
	}

	@RequestMapping("/del-{id}")
	public String delById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("method delById");
		enyanBannerService.deleteRecordByPrimaryKey(id);
		return "redirect:/banner/list";
	}

	@ResponseBody
	@RequestMapping(value = "/solve")
	public ExecuteResult<String> solve(@RequestBody EnyanBanner restObj, ModelMap modelMap,
	                                   HttpServletRequest request, HttpServletResponse response){
		ExecuteResult<String> result = new ExecuteResult<>();
		log.debug("dataId:{}",restObj.getDataId());
		if (null == restObj.getDataId()){
			result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		enyanBannerService.deleteRecordByPrimaryKey(restObj.getDataId());
		return result;
	}

	@RequestMapping("/reset")
	public String reset(ModelMap modelMap){
		log.debug("method reset");
		enyanBookService.initIndexAllInfo();
		return "redirect:/closePage";
	}

	@RequestMapping("/addUI")
	public String addUI(EnyanBanner record, ModelMap modelMap){
		log.debug("method addUI");
		record.setDataIndexShow(0);
		record.setDataReadShow(0);
		record.setDataMiddleShow(0);
		record.setDataStatus(1);
		modelMap.addAttribute("record",record);
		return "admin/bannerAdd";
	}

	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public String save(HttpServletRequest request, EnyanBanner record, ModelMap modelMap){
		log.debug("method save："+record);
		if (StringUtils.hasLength(record.getDataName()) == false){
			this.setErrorMsg(modelMap,"请填写banner名称");
			modelMap.addAttribute("record",record);
			return "admin/bannerAdd";
		}
		if (StringUtils.hasLength(record.getDataImgUrl()) == false){
			this.setErrorMsg(modelMap,"请填写图片的Url");
			modelMap.addAttribute("record",record);
			return "admin/bannerAdd";
		}
		if (StringUtils.hasLength(record.getDataToUrl()) == false){
			this.setErrorMsg(modelMap,"请填写转向的页面");
			modelMap.addAttribute("record",record);
			return "admin/bannerAdd";
		}
		if (null == record.getDataPriority()){
			this.setErrorMsg(modelMap,"请填写优先级");
			modelMap.addAttribute("record",record);
			return "admin/bannerAdd";
		}

		// 高级查询条件：
		String startDate, endDate ;
		String rangeDate = request.getParameter("rangeDate");
		if (StringUtils.hasLength(rangeDate) == false){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			modelMap.addAttribute("record",record);
			return "admin/bannerAdd";
		}
		String[] rangeDateArray = rangeDate.split("-");
		if (rangeDateArray.length != 2){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			modelMap.addAttribute("record",record);
			return "admin/bannerAdd";
		}
		startDate = rangeDateArray[0].trim();
		endDate = rangeDateArray[1].trim();
		if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
			this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
			modelMap.addAttribute("record",record);
			return "admin/bannerAdd";
		}
		try {
			record.setBeginAt(DateUtils.parseDate(startDate,"yyyy/MM/dd HH:mm:ss"));
			record.setEndAt(DateUtils.parseDate(endDate,"yyyy/MM/dd HH:mm:ss"));
		} catch (ParseException e) {
			e.printStackTrace();
		}

		if (null == record.getDataId()){
			Date currentDate = new Date();
			this.setSuccessMsg(modelMap,"添加banner成功");
			record.setIsDeleted(0);
			record.setCreateAt(currentDate);
			enyanBannerService.addRecord(record);
		}else {
			this.setSuccessMsg(modelMap,"修改banner成功");
			enyanBannerService.updateRecord(record);
		}
		return "redirect:/banner/list";
	}
}
