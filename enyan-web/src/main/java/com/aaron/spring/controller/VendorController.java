package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.NameAndValue;
import com.aaron.common.OrderObj;
import com.aaron.excel.ExcelExportUtil;
import com.aaron.excel.ExcelTemplateUtil;
import com.aaron.excel.FileUtil;
import com.aaron.excel.GroupCode;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.security.function.domain.IUser;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.*;
import com.aaron.spring.service.*;
import com.aaron.util.DateUtil;
import com.aaron.util.UserUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2018/4/25
 * @Modified By:
 */
@Controller
@RequestMapping("/vendor")
public class VendorController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String SESSION_BALANCE_DETAIL = "balanceDetail";

    @Resource
    private EnyanOrderDetailService enyanOrderDetailService;

    @Resource
    private EnyanPublisherService enyanPublisherService;

    @Resource
    private EnyanBalanceService enyanBalanceService;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanBalanceHistoryService enyanBalanceHistoryService;

    @Resource
    private EnyanRedeemCodeService enyanRedeemCodeService;

    @Resource
    private EnyanRefundService enyanRefundService;

    @Resource
    private EnyanRentDetailService enyanRentDetailService;

    @RequestMapping(value = "/books")
    public String booksPage(HttpServletRequest req, EnyanBook book, ModelMap modelMap,HttpServletRequest request){
        //logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
        if (UserUtils.isAnonymous()){
            this.setErrorMsg(modelMap, this.getMessage("error.nonlogin",request));
            return "/500";
        }
        if (null == book){
            book = new EnyanBook();
        }
        if (null == book.getPage()){
            book.setPage(new Page());
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            book.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            book.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件：
        String searchText = req.getParameter("searchText");
        String searchOption = req.getParameter("searchOption");
        if (StringUtils.hasLength(searchText)) {
            book.setBookTitle(searchText);
            queryParams.put("searchText",searchText);
        }

        if (StringUtils.hasLength(searchOption)){
            switch (searchOption){
                case "0":
//                    book.setShelfStatus(Constant.BYTE_VALUE_1);
                    break;
                case "1":
                    book.setShelfStatus(Constant.BYTE_VALUE_1);
                    queryParams.put("searchOption",searchOption);
                    break;
                case "2":
                    book.setShelfStatus(Constant.BYTE_VALUE_0);
                    queryParams.put("searchOption",searchOption);
                    break;
            }
        }

        IUser user = UserUtils.getCurrentLoginUser();
        CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
        book.setPublisherId(customUserDetail.getPublisherId());

//        book.addOrder(new OrderObj("is_recommended", InterfaceContant.OrderBy.DESC));
        book.addOrder(new OrderObj("shelf_status", InterfaceContant.OrderBy.ASC));
        Page<EnyanBook> page = enyanBookService.queryRecords(book.getPage(),book);
        book.setPage(page);
        book.excutePageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",book.getPageLand());
        modelMap.addAttribute("book",book);
        modelMap.addAttribute("explan","书籍管理");

        return "vendor/books";
    }

    @RequestMapping(value = "/myBalanceAccount", method = RequestMethod.GET)
    public String myBalanceAccount(AuthUser authUser, ModelMap modelMap,HttpServletRequest request){
        //logger.info("method regAction："+authUser);
        if (UserUtils.isAnonymous()){
            this.setErrorMsg(modelMap, this.getMessage("error.nonlogin",request));
            return "/vendor/myBalanceAccount";
        }
        //modelMap.addAttribute("authUser",authUser);
        IUser user = UserUtils.getCurrentLoginUser();
        CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
        EnyanPublisher publisher = enyanPublisherService.queryRecordByPrimaryKey(customUserDetail.getPublisherId()).getResult();

        modelMap.addAttribute("dto",publisher);
        return "/vendor/myBalanceAccount";
    }

    @RequestMapping(value = "/orderDetailsUI")
    public String ordersUI(HttpServletRequest request, EnyanOrderDetail orderDetail, ModelMap modelMap){
        if (null == orderDetail){
            orderDetail = new EnyanOrderDetail();
        }
        modelMap.addAttribute("dto",orderDetail);
        return "vendor/orderDetails";
    }

    @RequestMapping(value = "/orderDetailsUIOrder")
    public String orderDetailsUIOrder(HttpServletRequest request, EnyanOrderDetail orderDetail, ModelMap modelMap){
        if (null == orderDetail){
            orderDetail = new EnyanOrderDetail();
        }
        modelMap.addAttribute("dto",orderDetail);

        List<NameAndValue> publisherList = this.getNameAndValueList();
        modelMap.addAttribute("publisherList", publisherList);

        return "vendor/orderDetailsUI-order";
    }

    @RequestMapping(value = "/orderDetailsUIBook")
    public String orderDetailsUIBook(HttpServletRequest request, EnyanOrderDetail orderDetail, ModelMap modelMap){
        if (null == orderDetail){
            orderDetail = new EnyanOrderDetail();
        }
        modelMap.addAttribute("dto",orderDetail);

        List<NameAndValue> publisherList = this.getNameAndValueList();
        modelMap.addAttribute("publisherList", publisherList);
        return "vendor/orderDetailsUI-book";
    }

    @RequestMapping(value = "/orderDetails")
    public String orderDetailsPage(HttpServletRequest request, EnyanOrderDetail orderDetail, ModelMap modelMap){
        logger.debug("orderDetails");
        if (null == orderDetail){
            orderDetail = new EnyanOrderDetail();
        }
        if (null == orderDetail.getPage()){
            orderDetail.setPage(new Page());
        }

        String searchType = request.getParameter("searchType");
        String searchOption = request.getParameter("searchOption");
        searchOption = StringUtils.hasLength(searchOption) == false ? "-1":searchOption;
        String toPage = "vendor/orderDetails-order";

        if ("1".equals(searchType)){
            toPage = "vendor/orderDetails-book";
        }else if ("0".equals(searchType)){

        }else{
            //toPage = "vendor/orderDetails-vendor";
        }

        List<NameAndValue> publisherList = this.getNameAndValueList();
        modelMap.addAttribute("publisherList", publisherList);

        Map<String, Object> queryParams = new HashMap<>();

        // 高级查询条件：
        String startDate, endDate ;

        String rangeDate = request.getParameter("rangeDate");
        if (StringUtils.hasLength(rangeDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",orderDetail);
            return toPage;
        }
        String[] rangeDateArray = rangeDate.split("-");
        if (rangeDateArray.length != 2){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",orderDetail);
            return toPage;
        }
        startDate = rangeDateArray[0].trim();
        endDate = rangeDateArray[1].trim();
        if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",orderDetail);
            return toPage;
        }
        String searchSelect = request.getParameter("searchSelect");
        Long publisherId = 0L;
        if (StringUtils.hasLength(searchSelect)){
            searchSelect = StringUtils.hasLength(searchSelect) == false ? "-1":searchSelect;
            publisherId = Long.parseLong(searchSelect);
            if (publisherId != -1){
                orderDetail.setPublisherId(publisherId);
            }
            queryParams.put("searchSelect",searchSelect);
        }

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            if (this.isPublisherValid(publisherId) == false){//如果是非法id，则直接归0
                publisherId = 0L;
            }
            orderDetail.setPublisherId(publisherId);
        }

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            orderDetail.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            orderDetail.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        queryParams.put("rangeDate",rangeDate);

        orderDetail.setStartDate(startDate);
        orderDetail.setEndDate(endDate);

        String searchText = request.getParameter("searchText");
        if (StringUtils.hasLength(searchText)){
            orderDetail.setBookTitle(searchText);
            queryParams.put("searchText",searchText);
        }


        Page<EnyanOrderDetail> page ;
        switch (searchOption){
            case "0":
                orderDetail.addOrder(new OrderObj("purchased_at","desc"));
                break;
            case "1":
                orderDetail.addOrder(new OrderObj("quantity","desc"));
                break;
            case "2":
                orderDetail.addOrder(new OrderObj("quantity","asc"));
                break;
            case "3":
                orderDetail.addOrder(new OrderObj("income_total","desc"));
                break;
            case "4":
                orderDetail.addOrder(new OrderObj("income_total","asc"));
                break;
            case "5":
                orderDetail.addOrder(new OrderObj("book_title","desc"));
                break;
            case "6":
                orderDetail.addOrder(new OrderObj("purchased_at","desc"));
                break;
        }
        if ("1".equals(searchType)){//book 销售分析
            queryParams.put("searchOption",searchOption);
            page = enyanOrderDetailService.queryRecordsByBook(orderDetail.getPage(),orderDetail);

            queryParams.put("searchType","1");
            orderDetail.setDownloadType(EBookConstant.ExcelDownload.SELL_INFO_BOOK);
        }else if ("0".equals(searchType)){//order 销售明细
            queryParams.put("searchOption",searchOption);
            page = enyanOrderDetailService.findRecordsByOrder(orderDetail.getPage(),orderDetail);

            queryParams.put("searchType","0");
            orderDetail.setDownloadType(EBookConstant.ExcelDownload.SELL_INFO_ORDER);
        }else {//Vendor
            queryParams.put("searchOption",searchOption);
            page = enyanOrderDetailService.queryRecordsByVendor(orderDetail.getPage(),orderDetail);

            queryParams.put("searchType","2");
            orderDetail.setDownloadType(EBookConstant.ExcelDownload.SELL_INFO_VENDOR);
        }
        //page.setTotalRecord(page.getRecords().size());
        orderDetail.setPage(page);
        orderDetail.excutePageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",orderDetail.getPageLand());
        modelMap.addAttribute("dto",orderDetail);
        //modelMap.addAttribute("explan",this.getMessage("error.invalid.order",request));


        return toPage;
    }

    @RequestMapping(value = "/rentDetails")
    public String rentDetailsPage(HttpServletRequest request, EnyanRentDetail detail, ModelMap modelMap){
        logger.debug("rentDetails");
        if (null == detail){
            detail = new EnyanRentDetail();
        }
        if (null == detail.getPage()){
            detail.setPage(new Page());
        }

        String searchType = request.getParameter("searchType");
        String searchOption = request.getParameter("searchOption");
        searchOption = StringUtils.hasLength(searchOption) == false ? "-1":searchOption;
        String toPage = "vendor/rentDetails";
        /*
        if ("1".equals(searchType)){
            toPage = "vendor/orderDetails-book";
        }else if ("0".equals(searchType)){

        }else{
            //toPage = "vendor/orderDetails-vendor";
        }


        List<NameAndValue> publisherList = new ArrayList<>();
        NameAndValue nameAndValue = new NameAndValue();
        nameAndValue.setName("--全部--");
        nameAndValue.setValue("-1");

        publisherList.add(nameAndValue);
        publisherList.addAll(Constant.publishersList);
        modelMap.addAttribute("publisherList", publisherList);
        */
        Map<String, Object> queryParams = new HashMap<>();

        // 高级查询条件：
        String startDate, endDate ;

        String rangeDate = request.getParameter("rangeDate");
        if (StringUtils.hasLength(rangeDate) == false){
            //this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",detail);
            return toPage;
        }
        String[] rangeDateArray = rangeDate.split("-");
        if (rangeDateArray.length != 2){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",detail);
            return toPage;
        }
        startDate = rangeDateArray[0].trim();
        endDate = rangeDateArray[1].trim();
        if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",detail);
            return toPage;
        }
        /*
        String searchSelect = request.getParameter("searchSelect");
        if (StringUtils.hasLength(searchSelect)){
            searchSelect = StringUtils.hasLength(searchSelect) == false ? "-1":searchSelect;
            Long publisherId = Long.parseLong(searchSelect);
            if (publisherId != -1){
                orderDetail.setPublisherId(publisherId);
            }
            queryParams.put("searchSelect",searchSelect);
        }*/

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            IUser user = UserUtils.getCurrentLoginUser();
            CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
            //if (InterfaceContant.RoleName.ROLE_VENDOR.equals(user.getAuthorities().))
            //orderDetail.setPublisherId(user.getCustomDetail().);
            detail.setPublisherId(customUserDetail.getPublisherId());
        }

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            detail.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            detail.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        queryParams.put("rangeDate",rangeDate);

        detail.setStartDate(startDate+"000000");
        detail.setEndDate(endDate+"235959");

        /*
        String searchText = request.getParameter("searchText");
        if (StringUtils.hasLength(searchText)){
            orderDetail.setBookTitle(searchText);
            queryParams.put("searchText",searchText);
        }*/


        Page<EnyanRentDetail> page ;
        //detail.addOrder(new OrderObj("purchased_at","desc"));

        page = enyanRentDetailService.queryRecords(detail.getPage(),detail);
        //page.setTotalRecord(page.getRecords().size());
        detail.setPage(page);
        detail.excutePageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",detail.getPageLand());
        modelMap.addAttribute("dto",detail);

        return toPage;
    }

    @RequestMapping(value = "/rentDetailsExcel-{startDate}-{endDate}", method = RequestMethod.GET)
    public ResponseEntity<byte[]> rentDetailsExcel(HttpServletRequest request, @PathVariable("startDate")String startDate,
                                                @PathVariable("endDate")String endDate, ModelMap modelMap){
        logger.debug("rentDetailsExcel");
        EnyanRentDetail detail = new EnyanRentDetail();

        if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
            return excelDownloadAction(request,null,null,modelMap);
        }
        detail.setStartDate(startDate);
        detail.setEndDate(endDate);

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            IUser user = UserUtils.getCurrentLoginUser();
            CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
            //if (InterfaceContant.RoleName.ROLE_VENDOR.equals(user.getAuthorities().))
            //orderDetail.setPublisherId(user.getCustomDetail().);
            detail.setPublisherId(customUserDetail.getPublisherId());
        }

        List<EnyanRentDetail> list = enyanRentDetailService.findRecords(detail);
        List<List<String>> dataList = new ArrayList<>();

        List<String> excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.order.no",request,"订单编号"),
                this.getMessage("header.date.order",request,"订单时间"),this.getMessage("header.email",request,"用户email"),"产品编码","订购类型",
                this.getMessage("header.pay.method",request,"支付方式"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.fee.pay",request,"支付手续费"),
                this.getMessage("header.revenue.net",request,"净收益"),this.getMessage("header.amount.royalty",request,"版税金额"));

        int i = 1;
        int dataSize = excelHeaders.size();
        //"序号","订单编号","订单时间","用户email","产品编码","订购类型","支付方式","销售额","支付手续费",this.getMessage("header.revenue.net",request,"净收益"),"版税金额"
        for (EnyanRentDetail obj:list) {
            List<String> data = this.getDefaultData(dataSize);
            data.set(0, i++ + "");
            if (StringUtils.hasLength(obj.getOrderNum())) {//订单编号
                data.set(1, obj.getOrderNum());
            }
            if (null != obj.getPurchasedAt()) {//订单时间
                data.set(2, DateFormatUtils.format(obj.getPurchasedAt(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (StringUtils.hasLength(obj.getUserEmail()) == true){//用户email
                data.set(3, obj.getUserEmail());//用户email
            }
            data.set(4, BookUtil.getRentCodeInRentType(obj.getRentType(),obj.getRentLang()));//产品编码
            data.set(5, BookUtil.getBookNameInRentType(obj.getRentType(),obj.getRentLang()));//订购类型
            data.set(6, BookUtil.getPayTypeString(obj.getPayType()));//支付方式

            if (null != obj.getIncomeTotal()){//"销售额"
                data.set(7,String.valueOf(obj.getIncomeTotal()));
            }
            if (null != obj.getPayFee()){//"支付手续费"
                data.set(8,String.valueOf(obj.getPayFee()));
            }
            if (null != obj.getNetSales()){//"净收益"
                data.set(9,String.valueOf(obj.getNetSales()));
            }
            if (null != obj.getIncomeVendor()){//"版税金额"
                data.set(10,String.valueOf(obj.getIncomeVendor()));
            }
            dataList.add(data);
        }
        return this.excelDownloadAction(request,dataList,excelHeaders,modelMap);
    }

    @RequestMapping(value = "/orderCountUI")
    public String orderCountUI(HttpServletRequest request, EnyanOrderDetail orderDetail, ModelMap modelMap){
        if (null == orderDetail){
            orderDetail = new EnyanOrderDetail();
        }
        List<NameAndValue> publisherList = this.getNameAndValueList();
        modelMap.addAttribute("publisherList", publisherList);
        modelMap.addAttribute("dto",orderDetail);
        return "vendor/orderCountUI";
    }
    @RequestMapping(value = "/orderCounts")
    public String orderCounts(HttpServletRequest request, EnyanOrderDetail orderDetail, ModelMap modelMap){
        logger.debug("orderCounts");
        List<NameAndValue> publisherList = this.getNameAndValueList();
        modelMap.addAttribute("publisherList", publisherList);

        if (null == orderDetail){
            orderDetail = new EnyanOrderDetail();
        }
        if (null == orderDetail.getPage()){
            orderDetail.setPage(new Page());
        }
        Map<String, Object> queryParams = new HashMap<>();
        //orderDetail.getPage().setPageSize(1);
        // 高级查询条件：
        String startDate, endDate ;

        String rangeDate = request.getParameter("rangeDate");
        if (StringUtils.hasLength(rangeDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",orderDetail);
            return "vendor/orderCountUI";
        }
        String[] rangeDateArray = rangeDate.split("-");
        if (rangeDateArray.length != 2){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",orderDetail);
            return "vendor/orderCountUI";
        }
        startDate = rangeDateArray[0].trim();
        endDate = rangeDateArray[1].trim();

        if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",orderDetail);
            return "vendor/orderCountUI";
        }

        String searchSelect = request.getParameter("searchSelect");
        searchSelect = StringUtils.hasLength(searchSelect) == false ? "-1":searchSelect;
        Long publisherId = Long.parseLong(searchSelect);
        if (publisherId != -1){
            orderDetail.setPublisherId(publisherId);
        }
        queryParams.put("searchSelect",searchSelect);

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            if (this.isPublisherValid(publisherId) == false){//如果是非法id，则直接归0
                publisherId = 0L;
            }
            orderDetail.setPublisherId(publisherId);
        }

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            orderDetail.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            orderDetail.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        queryParams.put("startDate",startDate);
        queryParams.put("endDate",endDate);

        String searchType = request.getParameter("searchType");

        /*if (StringUtils.hasLength(searchType)) {
            switch (searchType){
                case "0":
                    order.setIsPaid(new Byte("0"));
                    break;
                case "1":
                    order.setIsPaid(new Byte("1"));
                    break;
                case "2":
                    order.setIsValid(new Byte("0"));
                    break;
            }
            queryParams.put("searchText",searchText);
            queryParams.put("searchType",searchType);
        }*/
        String toPage = "vendor/orderCount-detail";
        searchType = StringUtils.hasLength(searchType) == false?"0":searchType;


        orderDetail.setStartDate(startDate);
        orderDetail.setEndDate(endDate);

        Page<EnyanOrderDetail> page=null ;

        switch (searchType){
            case "0":
                page = enyanOrderDetailService.queryOrderCountsByDay(orderDetail.getPage(),orderDetail);
                orderDetail.setDownloadType(EBookConstant.ExcelDownload.SELL_COUNT_DAY);
                toPage = "vendor/orderCount-day";
                break;
            case "1":
                page = enyanOrderDetailService.queryOrderCountsByWeek(orderDetail.getPage(),orderDetail);
                orderDetail.setDownloadType(EBookConstant.ExcelDownload.SELL_COUNT_WEEK);
                toPage = "vendor/orderCount-week";
                break;
            case "2":
                orderDetail.setStartDate(startDate+"01");
                orderDetail.setEndDate(endDate+"31");
                page = enyanOrderDetailService.queryOrderCountsByMonth(orderDetail.getPage(),orderDetail);
                orderDetail.setDownloadType(EBookConstant.ExcelDownload.SELL_COUNT_MONTH);
                toPage = "vendor/orderCount-month";
                break;
            case "3":
                orderDetail.setStartDate(startDate+"0101");
                orderDetail.setEndDate(endDate+"1231");
                page = enyanOrderDetailService.queryOrderCountsByYear(orderDetail.getPage(),orderDetail);
                orderDetail.setDownloadType(EBookConstant.ExcelDownload.SELL_COUNT_YEAR);
                toPage = "vendor/orderCount-year";
                break;
        }

        queryParams.put("searchType",searchType);
        page.setTotalRecord(page.getRecords().size());
        orderDetail.setPage(page);
        orderDetail.excutePageLand(queryParams);

        /*
        EnyanOrderDetail orderDetailBottom = new EnyanOrderDetail();
        orderDetailBottom.setQuantity(0);
        orderDetailBottom.setIncomeReal(new BigDecimal("0"));
        orderDetailBottom.setIncomeTotal(new BigDecimal("0"));
        orderDetailBottom.setPriceSelling(new BigDecimal("0"));
        orderDetailBottom.setPriceFixed(new BigDecimal("0"));
        orderDetailBottom.setIncomePlat(new BigDecimal("0"));
        orderDetailBottom.setIncomeVendor(new BigDecimal("0"));
        for (EnyanOrderDetail tmp:page.getRecords()){
            orderDetailBottom.setQuantity(orderDetailBottom.getQuantity()+tmp.getQuantity());
            if (null!=tmp.getIncomeReal()){
                orderDetailBottom.setIncomeReal(orderDetailBottom.getIncomeReal().add(tmp.getIncomeReal()));
            }
            if (null != tmp.getIncomeTotal()){
                orderDetailBottom.setIncomeTotal(orderDetailBottom.getIncomeTotal().add(tmp.getIncomeTotal()));
            }
            if (null != tmp.getPriceSelling()){
                orderDetailBottom.setPriceSelling(orderDetailBottom.getPriceSelling().add(tmp.getPriceSelling()));
            }
            if (null != tmp.getPriceFixed()){
                orderDetailBottom.setPriceFixed(orderDetailBottom.getPriceFixed().add(tmp.getPriceFixed()));
            }
            if (null != tmp.getIncomePlat()){
                orderDetailBottom.setIncomePlat(orderDetailBottom.getIncomePlat().add(tmp.getIncomePlat()));
            }
            if (null != tmp.getIncomeVendor()){
                orderDetailBottom.setIncomeVendor(orderDetailBottom.getIncomeVendor().add(tmp.getIncomeVendor()));
            }
        }*/
        orderDetail.setStartDate(startDate);
        orderDetail.setEndDate(endDate);
        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",orderDetail.getPageLand());
        modelMap.addAttribute("dto",orderDetail);
        //modelMap.addAttribute("orderDetailBottom",orderDetailBottom);
        //modelMap.addAttribute("explan",this.getMessage("error.invalid.order",request));


        return toPage;
    }
    @RequestMapping(value = "/balanceUI")
    public String balanceUI(HttpServletRequest request, EnyanBalance balance, ModelMap modelMap){
        if (null == balance){
            balance = new EnyanBalance();
        }
        modelMap.addAttribute("dto",balance);
        return "vendor/balance";
    }

    @RequestMapping(value = "/cancelBalance")
    public String cancelBalance(HttpServletRequest request, EnyanBalance balance, ModelMap modelMap){
        request.getSession().removeAttribute(SESSION_BALANCE_DETAIL);
        return "redirect:/vendor/balanceUI";
    }

    @RequestMapping(value = "/balances")
    public String balances(HttpServletRequest request, EnyanBalance balance, ModelMap modelMap){
        logger.debug("orderCounts");
        if (null == balance){
            balance = new EnyanBalance();
        }
        if (null == balance.getPage()){
            balance.setPage(new Page());
        }

        //orderDetail.getPage().setPageSize(1);
        // 高级查询条件：
        String startDate, endDate ;

        String rangeDate = request.getParameter("rangeDate");
        if (StringUtils.hasLength(rangeDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",balance);
            return "vendor/orderCountUI";
        }
        String[] rangeDateArray = rangeDate.split("-");
        if (rangeDateArray.length != 2){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",balance);
            return "vendor/orderCountUI";
        }
        startDate = rangeDateArray[0].trim();
        endDate = rangeDateArray[1].trim();

        if (StringUtils.hasLength(startDate) == false || StringUtils.hasLength(endDate) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",balance);
            return "vendor/balance-detail";
        }

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            IUser user = UserUtils.getCurrentLoginUser();
            CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
            //if (InterfaceContant.RoleName.ROLE_VENDOR.equals(user.getAuthorities().))
            //orderDetail.setPublisherId(user.getCustomDetail().);
            balance.setPublisherId(customUserDetail.getPublisherId());
        }

        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            balance.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            balance.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        queryParams.put("startDate",startDate);
        queryParams.put("endDate",endDate);


        balance.setStartDate(startDate);
        balance.setEndDate(endDate);

        Page<EnyanBalance> page= enyanBalanceService.queryRecords(balance.getPage(),balance);


        page.setTotalRecord(page.getRecords().size());
        balance.setPage(page);
        balance.excutePageLand(queryParams);


        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",balance.getPageLand());
        modelMap.addAttribute("dto",balance);
        //modelMap.addAttribute("explan",this.getMessage("error.invalid.order",request));

        return "vendor/balance-detail";
    }

    @RequestMapping(value = "/orderDetailsBook-{yearMonth}")
    public String orderDetailsBookPage(HttpServletRequest request, @PathVariable("yearMonth")String yearMonth, ModelMap modelMap){
        logger.debug("orderDetailsBookPage");
        EnyanOrderDetail orderDetail = new EnyanOrderDetail();
        orderDetail.setPage(new Page());
        orderDetail.setPageFileName("orderDetails");

        String searchOption = request.getParameter("searchOption");
        searchOption = StringUtils.hasLength(searchOption) == false ? "-1":searchOption;
        Long publisherId = Long.parseLong(searchOption);
        if (publisherId != -1){
            orderDetail.setPublisherId(publisherId);
        }
        //queryParams.put("searchOption",searchOption);

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            if (this.isPublisherValid(publisherId) == false){//如果是非法id，则直接归0
                publisherId = 0L;
            }
            orderDetail.setPublisherId(publisherId);
        }
        if (StringUtils.hasLength(yearMonth) == false){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",orderDetail);
            return "vendor/orderCount-detail";
        }

        try {
            orderDetail.setStartDate(yearMonth);
            Date date = DateUtils.parseDate(orderDetail.getStartDate()+"01","yyyyMMdd");
            orderDetail.setEndDate(DateUtil.getLastDayThisMonth(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            orderDetail.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            orderDetail.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        queryParams.put("startDate",yearMonth+"01");
        queryParams.put("endDate",orderDetail.getEndDate());

        Page<EnyanOrderDetail> page = enyanOrderDetailService.queryOrderDetailBySpecialMonth(orderDetail.getPage(),orderDetail);
        queryParams.put("searchType","0");
        orderDetail.setPage(page);
        orderDetail.excutePageLand(queryParams);

        orderDetail.setStartDate(yearMonth+"01");
        orderDetail.setSearchType(0);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",orderDetail.getPageLand());
        modelMap.addAttribute("dto",orderDetail);
        //modelMap.addAttribute("explan",this.getMessage("error.invalid.order",request));


        return "vendor/orderDetails-order";
    }

    @RequestMapping(value = "/balanceConfirm")
    public String balanceConfirm(HttpServletRequest request, EnyanBalance balance, ModelMap modelMap){
        logger.debug("balanceConfirm");

        String[] products = request.getParameterValues("productId");
        if (products == null ||products.length == 0){
            modelMap.addAttribute(Error_Msg,this.getMessage("error.balance.select",request));
            return "vendor/balance-detail";
        }
        BalanceDetail balanceDetail = new BalanceDetail();
        List<Long> productList = new ArrayList<>(products.length);
        for (String p:products){
            productList.add(Long.parseLong(p));
        }
        if (null == balance){
            balance = new EnyanBalance();
        }

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            IUser user = UserUtils.getCurrentLoginUser();
            CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
            //if (InterfaceContant.RoleName.ROLE_VENDOR.equals(user.getAuthorities().))
            //orderDetail.setPublisherId(user.getCustomDetail().);
            balance.setPublisherId(customUserDetail.getPublisherId());
        }

        List<EnyanBalance> list = enyanBalanceService.findBalanceByIds(productList,balance.getPublisherId());
        balanceDetail.setBalanceList(list);

        EnyanBalance balanceBottom = new EnyanBalance();
        balanceBottom.setQuantity(0);
        balanceBottom.setIncomeVendor(new BigDecimal("0"));
        balanceBottom.setIncomeTotal(new BigDecimal("0"));
        balanceBottom.setIncomeReal(new BigDecimal("0"));
        balanceBottom.setIncomePlat(new BigDecimal("0"));
        for (EnyanBalance tmp:list){
            if (null != tmp.getIncomeVendor()){
                balanceBottom.setIncomeVendor(balanceBottom.getIncomeVendor().add(tmp.getIncomeVendor()));
            }
            if (null != tmp.getIncomePlat()){
                balanceBottom.setIncomePlat(balanceBottom.getIncomePlat().add(tmp.getIncomePlat()));
            }
            if (null != tmp.getIncomeReal()){
                balanceBottom.setIncomeReal(balanceBottom.getIncomeReal().add(tmp.getIncomeReal()));
            }
            if (null != tmp.getIncomeTotal()){
                balanceBottom.setIncomeTotal(balanceBottom.getIncomeTotal().add(tmp.getIncomeTotal()));
            }
            balanceBottom.setQuantity(balanceBottom.getQuantity()+tmp.getQuantity());
        }
        balanceDetail.setBalanceBottom(balanceBottom);

        List<EnyanBook> bookList = enyanBookService.findBookNotCost(balance.getPublisherId());
        balanceDetail.setBookCostList(bookList);

        balanceDetail.setBookCostTotal(0);
        for (EnyanBook tmp:bookList){
            if (null != tmp.getBookCost()){
                balanceDetail.setBookCostTotal(balanceDetail.getBookCostTotal()+tmp.getBookCost());
            }
        }

        balanceDetail.setTransferFee(new BigDecimal("120"));

        BigDecimal totalFee = balanceDetail.getBalanceBottom().getIncomeVendor().subtract(new BigDecimal(balanceDetail.getBookCostTotal())).subtract(balanceDetail.getTransferFee());
        balanceDetail.setTotalFee(totalFee);

        //modelMap.addAttribute("list",list);
        //modelMap.addAttribute("dto",balance);
        //modelMap.addAttribute("balanceDetail",balanceDetail);
        //modelMap.addAttribute("explan",this.getMessage("error.invalid.order",request));

        request.getSession().setAttribute(SESSION_BALANCE_DETAIL,balanceDetail);
        return "vendor/balanceConfirm";
    }

    @RequestMapping(value = "/balanceBank")
    public String balanceBank(HttpServletRequest request, EnyanBalance balance, ModelMap modelMap){
        logger.debug("balanceConfirm");
        BalanceDetail balanceDetail = (BalanceDetail) request.getSession().getAttribute(SESSION_BALANCE_DETAIL);
        if (null == balanceDetail){
            return "500";
        }
        if (UserUtils.isAnonymous()){
            this.setErrorMsg(modelMap, this.getMessage("error.nonlogin",request));
            return "/vendor/balanceConfirm";
        }
        if (balanceDetail.getTotalFee()==null || balanceDetail.getTotalFee().compareTo(new BigDecimal("0"))<=0){
            this.setErrorMsg(modelMap, this.getMessage("error.balance.less",request));
            return "/vendor/balanceConfirm";
        }
        //modelMap.addAttribute("authUser",authUser);
        IUser user = UserUtils.getCurrentLoginUser();
        CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
        EnyanPublisher publisher = enyanPublisherService.queryRecordByPrimaryKey(customUserDetail.getPublisherId()).getResult();

        Date date = new Date();

        if (publisher.getStartTime() == null || date.compareTo(publisher.getStartTime())==-1){//当前时间小于预定开始结算时间
            this.setErrorMsg(modelMap, this.getMessage("error.balance.date.less",request));
            return "/vendor/balanceConfirm";
        }

        EnyanPublisher newPublisher = new EnyanPublisher();
        newPublisher.setPublisherId(publisher.getPublisherId());

        newPublisher.setBankNational(publisher.getBankNational());
        newPublisher.setBankAddress(publisher.getBankAddress());
        newPublisher.setBankName(publisher.getBankName());
        newPublisher.setBankCode(publisher.getBankCode());
        newPublisher.setBankNum(publisher.getBankNum());
        newPublisher.setBankTitle(publisher.getBankTitle());

        balanceDetail.setPublisher(newPublisher);

        request.getSession().setAttribute(SESSION_BALANCE_DETAIL,balanceDetail);
        return "vendor/balanceBank";
    }

    @RequestMapping(value = "/balanceConfirmCreate")
    public String balanceConfirmCreate(HttpServletRequest request, EnyanBalance balance, ModelMap modelMap){
        logger.debug("balanceConfirmCreate");
        BalanceDetail balanceDetail = (BalanceDetail) request.getSession().getAttribute(SESSION_BALANCE_DETAIL);
        if (null == balanceDetail){
            return "500";
        }
        if (UserUtils.isAnonymous()){
            this.setErrorMsg(modelMap, this.getMessage("error.nonlogin",request));
            return "/vendor/balanceConfirm";
        }
        this.setSuccessMsg(modelMap,this.getMessage("success.balance.confirm",request));

        String json = JSON.toJSONString(balanceDetail);
        logger.debug(json);

        Date date = new Date();
        EnyanBalanceHistory balanceHistory = new EnyanBalanceHistory();
        balanceHistory.setPublisherId(balanceDetail.getPublisher().getPublisherId());
        balanceHistory.setBalanceAt(date);
        balanceHistory.setIncomeVendorTotal(balanceDetail.getTotalFee());
        balanceHistory.setIsCounted(Constant.BYTE_VALUE_0);
        balanceHistory.setBalanceDetail(json);
        balanceHistory.setBalanceDay(Integer.parseInt(DateFormatUtils.format(date,"yyyyMMdd")));

        enyanBalanceHistoryService.saveBalanceHistory(balanceHistory,balanceDetail);

        request.getSession().removeAttribute(SESSION_BALANCE_DETAIL);
        return "vendor/balanceSuccess";
    }
    @RequestMapping(value = "/balanceHistoryUI")
    public String balanceHistoryUI(HttpServletRequest request, EnyanBalanceHistory balance, ModelMap modelMap){
        if (null == balance){
            balance = new EnyanBalanceHistory();
        }
        modelMap.addAttribute("dto",balance);
        return "vendor/balanceHistoryUI";
    }

    @RequestMapping(value = "/balanceHistory")
    public String balanceHistory(HttpServletRequest request, EnyanBalanceHistory balanceHistory, ModelMap modelMap){
        logger.debug("balanceHistory");
        if (null == balanceHistory){
            balanceHistory = new EnyanBalanceHistory();
        }
        if (null == balanceHistory.getPage()){
            balanceHistory.setPage(new Page());
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 高级查询条件：
        String startDate = "", endDate ="";

        String rangeDate = request.getParameter("rangeDate");
        if (StringUtils.hasLength(rangeDate)){
            String[] rangeDateArray = rangeDate.split("-");
            if (rangeDateArray.length != 2){
                this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
                modelMap.addAttribute("dto",balanceHistory);
                return "vendor/balanceHistorys";
            }
            startDate = rangeDateArray[0].trim();
            endDate = rangeDateArray[1].trim();
            queryParams.put("rangeDate",rangeDate);
        }

        /*
        if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)){
            this.setErrorMsg(modelMap, this.getMessage("error.time.startOrEnd.null",request));
            modelMap.addAttribute("dto",balanceHistory);
            return "vendor/balanceHistoryUI";
        }*/

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            IUser user = UserUtils.getCurrentLoginUser();
            CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
            //if (InterfaceContant.RoleName.ROLE_VENDOR.equals(user.getAuthorities().))
            //orderDetail.setPublisherId(user.getCustomDetail().);
            balanceHistory.setPublisherId(customUserDetail.getPublisherId());
        }

        // 获取分页参数
        String total = request.getParameter("total");
        String currentPage = request.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            balanceHistory.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            balanceHistory.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        if (StringUtils.hasLength(startDate)){
            balanceHistory.setStartDate(startDate);
        }
        if (StringUtils.hasLength(endDate)){
            balanceHistory.setEndDate(endDate);
        }

        Page<EnyanBalanceHistory> page= enyanBalanceHistoryService.queryRecords(balanceHistory.getPage(),balanceHistory);

        balanceHistory.setPage(page);
        balanceHistory.excutePageLand(queryParams);


        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand",balanceHistory.getPageLand());
        modelMap.addAttribute("dto",balanceHistory);
        //modelMap.addAttribute("explan",this.getMessage("error.invalid.order",request));

        return "vendor/balanceHistorys";
    }

    @RequestMapping(value = "/balanceHistoryView-{id}", method = RequestMethod.GET)
    public String balanceHistoryView(@PathVariable("id")Long id , ModelMap modelMap){
        logger.info("getOrderById");
        EnyanBalanceHistory enyanBalanceHistory = enyanBalanceHistoryService.queryRecordByPrimaryKey(id).getResult();

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
            IUser user = UserUtils.getCurrentLoginUser();
            CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
            //if (InterfaceContant.RoleName.ROLE_VENDOR.equals(user.getAuthorities().))
            //orderDetail.setPublisherId(user.getCustomDetail().);
            //balanceHistory.setPublisherId(customUserDetail.getPublisherId());

            if (enyanBalanceHistory.getPublisherId() != customUserDetail.getPublisherId()){
                modelMap.addAttribute("balanceDetail",new BalanceDetail());
                return "vendor/balanceHistoryDetail";
            }
        }
        if (StringUtils.hasLength(enyanBalanceHistory.getBalanceDetail())){
            BalanceDetail balanceDetail = JSONObject.parseObject(enyanBalanceHistory.getBalanceDetail(),BalanceDetail.class);
            modelMap.addAttribute("balanceDetail",balanceDetail);
        }else {
            modelMap.addAttribute("balanceDetail",new BalanceDetail());
        }
        return "vendor/balanceHistoryDetail";
    }

    @RequestMapping(value = "/balanceHistoryConfirm-{id}", method = RequestMethod.GET)
    public String balanceHistoryConfirm(@PathVariable("id")Long id , ModelMap modelMap){
        logger.info("balanceHistoryConfirm");

        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_ADMIN) || UserUtils.hasRole(InterfaceContant.RoleName.ROLE_FINANCE)){
            enyanBalanceHistoryService.confirmBalanceHistory(id, EBookConstant.BalanceStatus.DONE);
        }
        return "redirect:/vendor/balanceHistory";
    }

    @RequestMapping(value = "/excelDownload-{startDate}-{endDate}-{downloadType}", method = RequestMethod.GET)
    public ResponseEntity<byte[]> excelDownload(HttpServletRequest request, @PathVariable("startDate")String startDate,
                                                @PathVariable("endDate")String endDate,
                                                     @PathVariable("downloadType")Integer downloadType, ModelMap modelMap){
        logger.debug("excelDownload");
        ResponseEntity<byte[]> entity = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型

            Object userIdObj = UserUtils.getCurrentLoginUser().getUserId();

            if (UserUtils.isAnonymous() || null == userIdObj || "-1".equals(userIdObj)
                    ||(UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)&&downloadType == EBookConstant.ExcelDownload.SELL_INFO_VENDOR)){
                headers.setContentDispositionFormData("attachment", "excel.txt");//告知浏览器以下载方式打开
                entity = new ResponseEntity<>("下载失败001".getBytes(), headers, HttpStatus.OK);
                return entity;
            }

            List<EnyanOrderDetail> list = null;
            List<EnyanRedeemCode> redeemCodeList = null;
            List<EnyanRefund> refundList = null;
            List<String> excelHeaders = this.getExcelHeaders(downloadType,request);
            List<List<String>> dataList = null;

            EnyanOrderDetail orderDetail = new EnyanOrderDetail();
            orderDetail.setStartDate(startDate);
            orderDetail.setEndDate(endDate);

            String searchSelect = request.getParameter("searchSelect");
            searchSelect = StringUtils.hasLength(searchSelect) == false ? "-1":searchSelect;
            Long publisherId = Long.parseLong(searchSelect);
            if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR)){
                if (this.isPublisherValid(publisherId) == false){//如果是非法id，则直接归0
                    publisherId = 0L;
                }
                orderDetail.setPublisherId(publisherId);
            }else if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_ADMIN) || UserUtils.hasRole(InterfaceContant.RoleName.ROLE_FINANCE)){//管理员
                if (StringUtils.hasLength(searchSelect) == true){
                    publisherId = Long.parseLong(searchSelect);
                }
            }

            String searchText = request.getParameter("searchText");
            orderDetail.setBookTitle(searchText);

            String searchOption = request.getParameter("searchOption");
            searchOption = StringUtils.hasLength(searchOption) == false ? "-1":searchOption;
            switch (searchOption){
                case "0":
                    break;
                case "1":
                    orderDetail.addOrder(new OrderObj("quantity","desc"));
                    break;
                case "2":
                    orderDetail.addOrder(new OrderObj("quantity","asc"));
                    break;
                case "3":
                    orderDetail.addOrder(new OrderObj("income_total","desc"));
                    break;
                case "4":
                    orderDetail.addOrder(new OrderObj("income_total","asc"));
                    break;
                case "5":
                    orderDetail.addOrder(new OrderObj("book_title","desc"));
                    break;
                case "6":
                    orderDetail.addOrder(new OrderObj("purchased_at","desc"));
                    break;
            }
            switch (downloadType){
                case EBookConstant.ExcelDownload.SELL_INFO_BOOK:
                    if (publisherId != -1){
                        orderDetail.setPublisherId(publisherId);
                    }
                    list = enyanOrderDetailService.queryRecordsByBook(orderDetail);
                    break;
                case EBookConstant.ExcelDownload.SELL_INFO_VENDOR:
                    list = enyanOrderDetailService.queryRecordsByVendor(orderDetail);
                    break;
                case EBookConstant.ExcelDownload.SELL_INFO_ORDER:
                    orderDetail.setBookTitle(searchText);
                    if (publisherId != -1){
                        orderDetail.setPublisherId(publisherId);
                    }
                    list = enyanOrderDetailService.findRecordsByOrder(orderDetail);
                    break;
                case EBookConstant.ExcelDownload.SELL_COUNT_DAY:
                    if (publisherId != -1){
                        orderDetail.setPublisherId(publisherId);
                    }
                    list = enyanOrderDetailService.queryOrderCountsByDay(orderDetail);
                    break;
                case EBookConstant.ExcelDownload.SELL_COUNT_WEEK:
                    if (publisherId != -1){
                        orderDetail.setPublisherId(publisherId);
                    }
                    list = enyanOrderDetailService.queryOrderCountsByWeek(orderDetail);
                    break;
                case EBookConstant.ExcelDownload.SELL_COUNT_MONTH:
                    orderDetail.setStartDate(startDate+"01");
                    orderDetail.setEndDate(endDate+"31");
                    if (publisherId != -1){
                        orderDetail.setPublisherId(publisherId);
                    }
                    list = enyanOrderDetailService.queryOrderCountsByMonth(orderDetail);
                    break;
                case EBookConstant.ExcelDownload.SELL_COUNT_YEAR:
                    orderDetail.setStartDate(startDate+"0101");
                    orderDetail.setEndDate(endDate+"1231");
                    if (publisherId != -1){
                        orderDetail.setPublisherId(publisherId);
                    }
                    list = enyanOrderDetailService.queryOrderCountsByYear(orderDetail);
                    break;
                case EBookConstant.ExcelDownload.SELL_INFO_REDEEM_CODE:
                    EnyanRedeemCode redeemCode = new EnyanRedeemCode();
                    redeemCode.setStartDate(startDate+"000000");
                    redeemCode.setEndDate(endDate+"235959");
                    redeemCodeList = enyanRedeemCodeService.findRecordsByRedeemCode(redeemCode);
                    break;
                case EBookConstant.ExcelDownload.REFUND_INFO:
                    EnyanRefund refund = new EnyanRefund();
                    refund.setBookTitle(searchText);
                    refund.setStartDate(startDate+"000000");
                    refund.setEndDate(endDate+"235959");
                    if (publisherId != -1){
                        refund.setPublisherId(publisherId);
                    }
                    refundList = enyanRefundService.findRecordByRefund(refund);
                    break;
            }
            dataList = this.getExcelData(list,redeemCodeList,refundList,downloadType,excelHeaders.size());
            if (null == dataList){
                //logger.info("downloadAcsm 下载失败");
                headers.setContentDispositionFormData("attachment", "excel.txt");//告知浏览器以下载方式打开
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
                entity = new ResponseEntity<>("无数据002".getBytes(), headers, HttpStatus.OK);
                return entity;
            }
            XSSFWorkbook book = ExcelTemplateUtil.createHeaders(excelHeaders);
            if (book.getNumberOfSheets() > 0) {
                Row rowDel = book.getSheetAt(0).getRow(2);
                if (rowDel != null) {
                    book.getSheetAt(0).removeRow(rowDel);
                }
            }

            SXSSFWorkbook workbook = new SXSSFWorkbook(book);
            String filePath = FileUtil.getExportPath(GroupCode.DETAIL, DateFormatUtils.format(new Date(),"yyyyMMddHHmmss"))+".xlsx";
            File file = new File(filePath);
            ExcelExportUtil.createExcel(dataList, filePath, 2, workbook);
            String fileName = "excel.xlsx";
            String downloadFileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");;

            headers.setContentDispositionFormData("attachment", downloadFileName);//告知浏览器以下载方式打开
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            entity = new ResponseEntity<>(FileUtils.readFileToByteArray(file), headers, HttpStatus.OK);
            return entity;
            //ExcelExportUtil.createExcel();
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage());
        }
        return entity;
    }

    public ResponseEntity<byte[]> excelDownloadAction(HttpServletRequest request,List<List<String>> dataList, List<String> excelHeaders, ModelMap modelMap){
        logger.debug("excelDownloadAction");
        ResponseEntity<byte[]> entity = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型

            if (null == dataList || dataList.isEmpty() == true){
                //logger.info("downloadAcsm 下载失败");
                headers.setContentDispositionFormData("attachment", "excel.txt");//告知浏览器以下载方式打开
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
                entity = new ResponseEntity<>("无数据002".getBytes(), headers, HttpStatus.OK);
                return entity;
            }
            XSSFWorkbook book = ExcelTemplateUtil.createHeaders(excelHeaders);
            if (book.getNumberOfSheets() > 0) {
                Row rowDel = book.getSheetAt(0).getRow(2);
                if (rowDel != null) {
                    book.getSheetAt(0).removeRow(rowDel);
                }
            }

            SXSSFWorkbook workbook = new SXSSFWorkbook(book);
            String filePath = FileUtil.getExportPath(GroupCode.DETAIL, DateFormatUtils.format(new Date(),"yyyyMMddHHmmss"))+".xlsx";
            File file = new File(filePath);
            ExcelExportUtil.createExcel(dataList, filePath, 2, workbook);
            String fileName = "excel.xlsx";
            String downloadFileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");;

            headers.setContentDispositionFormData("attachment", downloadFileName);//告知浏览器以下载方式打开
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);//设置MIME类型
            entity = new ResponseEntity<>(FileUtils.readFileToByteArray(file), headers, HttpStatus.OK);
            return entity;
            //ExcelExportUtil.createExcel();
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage());
        }
        return entity;
    }

    /**
     *
     *  获取Excel headers
     * @param downloadType
     * @Date: 2018/6/29
     */
    private List<String> getExcelHeaders(int downloadType,HttpServletRequest request){
        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_ADMIN) || UserUtils.hasRole(InterfaceContant.RoleName.ROLE_FINANCE)){
            return this.getExcelHeadersAdmin(downloadType,request);
        }else {
            return this.getExcelHeadersVendor(downloadType,request);
        }
    }
    private List<String> getExcelHeadersAdmin(int downloadType,HttpServletRequest request){
        List<String> excelHeaders = null;
        switch (downloadType){
            case EBookConstant.ExcelDownload.SELL_INFO_BOOK:
                //书名,ISBN,出版编码,出版商,销量,销售额,支付手续费,净收益,版税金额,毛利,毛利率
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.book.title",request,"书名"),"ESIN","出版编码",this.getMessage("header.publisher",request,"出版商"),this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.revenue.net",request,"净收益"),this.getMessage("header.amount.royalty",request,"版税金额"),"毛利","毛利率");
                break;
            case EBookConstant.ExcelDownload.SELL_INFO_VENDOR:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),"Vendor",this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_INFO_ORDER:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.order.no",request,"订单编号"),this.getMessage("header.date.order",request,"订单时间"),this.getMessage("header.email",request,"用户email"),this.getMessage("header.book.title",request,"书名"),"ESIN","出版编码",this.getMessage("header.publisher",request,"出版商"),this.getMessage("header.price.list",request,"定价"),this.getMessage("header.price.sell",request,"售价"),this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.pay.method",request,"支付方式"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.revenue.net",request,"净收益"),"版税税率",this.getMessage("header.amount.royalty",request,"版税金额"),"销售","支付国籍","订单来源");
                break;
            case EBookConstant.ExcelDownload.SELL_COUNT_DAY:
            case EBookConstant.ExcelDownload.SELL_COUNT_WEEK:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.date.start",request,"开始日期"),this.getMessage("header.date.end",request,"结束日期"),this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.revenue.net",request,"净收益"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_COUNT_MONTH:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),"月份",this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.revenue.net",request,"净收益"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_COUNT_YEAR:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),"年份",this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.revenue.net",request,"净收益"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_INFO_REDEEM_CODE:
//                excelHeaders = Arrays.asList("兑换码",this.getMessage("header.book.title",request,"书名"),"出版编码","原付款email","原付款订单号","原付款日期","销售额","兑换订单号","兑换时间","兑换账户","兑换码来源");
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),"兑换码",this.getMessage("header.book.title",request,"书名"),"ESIN","出版编码","原付款email","原付款订单号","原付款日期",this.getMessage("header.price.list",request,"定价"),this.getMessage("header.price.sell",request,"售价"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.pay.method",request,"支付方式"),this.getMessage("header.fee.pay",request,"支付手续费"),"支付国籍","兑换订单号","兑换时间","兑换账户","兑换码来源");
                break;
            case EBookConstant.ExcelDownload.REFUND_INFO:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.order.no",request,"订单编号"),this.getMessage("header.date.order",request,"订单时间"),this.getMessage("header.book.title",request,"书名"),this.getMessage("header.publisher",request,"出版商"),this.getMessage("header.change.volume",request,"销量变动"),this.getMessage("header.change.sales",request,"销售额变动HKD"),this.getMessage("header.refund.reason",request,"退款原因及处理结果"));
                break;
        }
        return excelHeaders;
    }
    private List<String> getExcelHeadersVendor(int downloadType,HttpServletRequest request){
        List<String> excelHeaders = null;
        switch (downloadType){
            case EBookConstant.ExcelDownload.SELL_INFO_BOOK:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.book.title",request,"书名"),this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.revenue.net",request,"净收益"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_INFO_VENDOR:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),"Vendor",this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_INFO_ORDER:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.date.order",request,"订单时间"),this.getMessage("header.order.no",request,"订单编号"),this.getMessage("header.book.title",request,"书名"),this.getMessage("header.price.list",request,"定价"),this.getMessage("header.price.sell",request,this.getMessage("header.price.sell",request,"售价")),this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.pay.method",request,"支付方式"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.revenue.net",request,"净收益"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_COUNT_DAY:
            case EBookConstant.ExcelDownload.SELL_COUNT_WEEK:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.date.start",request,"开始日期"),this.getMessage("header.date.end",request,"结束日期"),this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_COUNT_MONTH:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),"月份",this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.SELL_COUNT_YEAR:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),"年份",this.getMessage("header.volume.sales",request,"销量"),this.getMessage("header.revenue.sales",request,"销售额"),this.getMessage("header.fee.pay",request,"支付手续费"),this.getMessage("header.amount.royalty",request,"版税金额"));
                break;
            case EBookConstant.ExcelDownload.REFUND_INFO:
                excelHeaders = Arrays.asList(this.getMessage("header.no",request,"序号"),this.getMessage("header.order.no",request,"订单编号"),this.getMessage("header.date.order",request,"订单时间"),this.getMessage("header.book.title",request,"书名"),this.getMessage("header.change.volume",request,this.getMessage("header.change.volume",request,"销量变动")),this.getMessage("header.change.sales",request,"销售额变动HKD"),this.getMessage("header.refund.reason",request,"退款原因及处理结果"));
                break;
        }
        return excelHeaders;
    }

    private List<List<String>> getExcelData(List<EnyanOrderDetail> list,List<EnyanRedeemCode> redeemCodeList,List<EnyanRefund> refundList,
                                            int downloadType,int dataSize){
        if ((null == list || list.isEmpty()) && (null == redeemCodeList || redeemCodeList.isEmpty())
                && (null == refundList || refundList.isEmpty())){
            return null;
        }
        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_ADMIN) || UserUtils.hasRole(InterfaceContant.RoleName.ROLE_FINANCE)){
            return this.getExcelDataAdmin(list,redeemCodeList,refundList,downloadType,dataSize);
        }else {
            return this.getExcelDataVendor(list,refundList,downloadType,dataSize);
        }
    }
    private List<List<String>> getExcelDataAdmin(List<EnyanOrderDetail> list,List<EnyanRedeemCode> redeemCodeList,List<EnyanRefund> refundList,
                                                 int downloadType,int dataSize){
        List<List<String>> dataList = new ArrayList<>();
        int i = 1;
        if (downloadType == EBookConstant.ExcelDownload.REFUND_INFO){
            //"序号","订单编号","订单时间","书名","出版商","销量变动","销售额变动HKD","退款原因及处理结果"
            for (EnyanRefund refund:refundList){
                List<String> data = this.getDefaultData(dataSize);
                data.set(0, i++ +"");
                if (StringUtils.hasLength(refund.getOrderNum())){//订单编号
                    data.set(1,refund.getOrderNum());
                }
                if (null != refund.getPurchasedAt()){//订单时间
                    data.set(2,DateFormatUtils.format(refund.getPurchasedAt(),"yyyy-MM-dd HH:mm:ss"));
                }
                if (StringUtils.hasLength(refund.getBookTitle())){//书名
                    data.set(3,refund.getBookTitle());
                }
                if (StringUtils.hasLength(refund.getPublisherName())){//出版商
                    data.set(4,refund.getPublisherName());
                }
                if (null != refund.getSalesVolumeDecrease()){//销量变动
                    data.set(5,refund.getSalesVolumeDecrease().toString());
                }
                if (null != refund.getIncomeTotalDecrease()){//销售额变动HKD
                    data.set(6,refund.getIncomeTotalDecrease().toString());
                }
                if (StringUtils.hasLength(refund.getReasonContent())){//退款原因及处理结果
                    data.set(7,refund.getReasonContent());
                }
                dataList.add(data);
            }
            return dataList;
        }
        if (downloadType == EBookConstant.ExcelDownload.SELL_INFO_REDEEM_CODE){
            //"序号","兑换码","书名","ESIN","出版编码","原付款email","原付款订单号","原付款日期","定价","售价","销售额","支付方式","支付手续费","支付国籍","兑换订单号","兑换时间","兑换账户","兑换码来源"
            for (EnyanRedeemCode redeemCode:redeemCodeList){
                List<String> data = this.getDefaultData(dataSize);
                EnyanOrderDetail orderDetail = redeemCode.getRedeemCodeNoteInfo().getEnyanOrderDetail();
                if (null == orderDetail){
                    orderDetail = new EnyanOrderDetail();
                }
                data.set(0, i++ +"");
                if (StringUtils.hasLength(redeemCode.getCode())){//"兑换码"
                    data.set(1,redeemCode.getCode());
                }
                if (StringUtils.hasLength(redeemCode.getRedeemCodeNoteInfo().getBookNameDescription())){//"书名"
                    data.set(2,redeemCode.getRedeemCodeNoteInfo().getBookNameDescription());
                }
                if (StringUtils.hasLength(orderDetail.getBookEsin())){//"ESIN"
                    data.set(3,orderDetail.getBookEsin());
                }
                if (StringUtils.hasLength(orderDetail.getBookEsin())){//"出版编码"
                    data.set(4,orderDetail.getBookPubCode());
                }
                if (StringUtils.hasLength(redeemCode.getUserEmail())){//"原付款email"
                    data.set(5,redeemCode.getUserEmail());
                }
                if (StringUtils.hasLength(orderDetail.getOrderNum())){//"原付款订单号"
                    data.set(6,orderDetail.getOrderNum());
                }
                if (null != orderDetail.getPurchasedDay()){//"原付款日期"
                    data.set(7,String.valueOf(orderDetail.getPurchasedDay()));
                }
                if (null != orderDetail.getPriceFixed()){//"定价"
                    data.set(8,String.valueOf(orderDetail.getPriceFixed()));
                }
                if (null != orderDetail.getPriceSelling()){//"售价"
                    data.set(9,String.valueOf(orderDetail.getPriceSelling()));
                }
                if (null != orderDetail.getIncomeTotal()){//销售额
                    data.set(10,String.valueOf(orderDetail.getIncomeTotal()));
                }
                if (null != orderDetail.getPayType()){//支付方式
                    data.set(11,PayDetail.payTypeDescription(orderDetail.getPayType()));
                }
                if (null != orderDetail.getPayFee()){//支付手续费
                    data.set(12,String.valueOf(orderDetail.getPayFee()));
                }
                if (StringUtils.hasLength(orderDetail.getPayCountry())){//支付国籍
                    data.set(13,orderDetail.getPayCountry());
                }
                if (EBookConstant.RedeemStatus.USE == redeemCode.getStatus()){
                    if (StringUtils.hasLength(redeemCode.getRedeemCodeNoteInfo().getOrderNumToRedeem())){//兑换订单号
                        data.set(14,redeemCode.getRedeemCodeNoteInfo().getOrderNumToRedeem());
                    }
                    if (StringUtils.hasLength(redeemCode.getRedeemCodeNoteInfo().getDateToRedeem())){//兑换时间
                        data.set(15,redeemCode.getRedeemCodeNoteInfo().getDateToRedeem());
                    }
                    if (StringUtils.hasLength(redeemCode.getRedeemCodeNoteInfo().getEmailToRedeem())){//兑换账户
                        data.set(16,redeemCode.getRedeemCodeNoteInfo().getEmailToRedeem());
                    }
                }
                if (StringUtils.hasLength(redeemCode.getRedeemCodeNoteInfo().getSource())){
                    if (StringUtils.hasLength(redeemCode.getRedeemCodeNoteInfo().getSource())){//兑换时间
                        data.set(17,redeemCode.getRedeemCodeNoteInfo().getSource());
                    }
                }
                dataList.add(data);
            }
            return dataList;
        }
        for (EnyanOrderDetail orderDetail:list){
            List<String> data = this.getDefaultData(dataSize);

            switch (downloadType){
                case EBookConstant.ExcelDownload.SELL_INFO_BOOK:
                    //"序号","书名","ESIN","出版编码","出版商","销量","销售额","支付手续费","净收益",this.getMessage("header.amount.royalty",request,"版税金额"),"毛利","毛利率"
                    data.set(0, i++ +"");
                    if (StringUtils.hasLength(orderDetail.getBookTitle())){//"书名"
                        data.set(1,orderDetail.getBookTitle());
                    }
                    if (StringUtils.hasLength(orderDetail.getBookEsin())){//"ESIN"
                        data.set(2,orderDetail.getBookEsin());
                    }
                    if (StringUtils.hasLength(orderDetail.getBookPubCode())){//"出版编码"
                        data.set(3,orderDetail.getBookPubCode());
                    }
                    if (null != orderDetail.getPublisherId()){//"出版商"
                        data.set(4,enyanPublisherService.getPublisherNameByID(orderDetail.getPublisherId()));
                    }
                    if (null != orderDetail.getQuantity()){//"销量"
                        data.set(5,String.valueOf(orderDetail.getQuantity()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//"销售额"
                        data.set(6,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getPayFee()){//"支付手续费"
                        data.set(7,String.valueOf(orderDetail.getPayFee()));
                    }
                    if (null != orderDetail.getNetSales()){//"净收益"
                        data.set(8,String.valueOf(orderDetail.getNetSales()));
                    }
                    if (null != orderDetail.getIncomeVendor()){//"版税金额"
                        data.set(9,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    if (null != orderDetail.getGrossProfit()){//"毛利"
                        data.set(10,String.valueOf(orderDetail.getGrossProfit()));
                    }
                    if (null != orderDetail.getGrossProfitMargin()){//"毛利率"
                        data.set(11,String.valueOf(orderDetail.getGrossProfitMargin()));
                    }
                    break;
                case EBookConstant.ExcelDownload.SELL_INFO_VENDOR:
                    //"序号","Vendor","销量","销售额","版税金额"
                    data.set(0, i++ +"");
                    if (null != orderDetail.getPublisherId()){
                        data.set(1,enyanPublisherService.getPublisherNameByID(orderDetail.getPublisherId()));
                    }
                    if (null != orderDetail.getQuantity()){
                        data.set(2,String.valueOf(orderDetail.getQuantity()));
                    }
                    if (null != orderDetail.getIncomeTotal()){
                        data.set(3,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getIncomeTotal()){
                        data.set(4,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    break;
                case EBookConstant.ExcelDownload.SELL_INFO_ORDER:
                    //"序号","订单编号","订单时间","用户email","书名","ESIN","出版编码","出版商","定价","售价","销量","销售额","支付方式","支付手续费","净收益","版税税率","版税金额","销售","支付国籍","订单来源"
                    data.set(0, i++ +"");
                    if (StringUtils.hasLength(orderDetail.getOrderNum())){//订单编号
                        data.set(1,orderDetail.getOrderNum());
                    }
                    if (null != orderDetail.getPurchasedAt()){//订单时间
                        data.set(2,DateFormatUtils.format(orderDetail.getPurchasedAt(),"yyyy-MM-dd HH:mm:ss"));
                    }
                    if (StringUtils.hasLength(orderDetail.getUserEmail())){//用户email
                        data.set(3,orderDetail.getUserEmail());
                    }
                    if (StringUtils.hasLength(orderDetail.getBookTitle())){//书名
                        data.set(4,orderDetail.getBookTitle());
                    }
                    if (StringUtils.hasLength(orderDetail.getBookEsin())){//ESIN
                        data.set(5,orderDetail.getBookEsin());
                    }
                    if (StringUtils.hasLength(orderDetail.getBookPubCode())){//出版编码
                        data.set(6,orderDetail.getBookPubCode());
                    }
                    if (null != orderDetail.getPublisherId()){//出版商
                        data.set(7,enyanPublisherService.getPublisherNameByID(orderDetail.getPublisherId()));
                    }
                    if (null != orderDetail.getPriceFixed()){//定价
                        data.set(8,String.valueOf(orderDetail.getPriceFixed()));
                    }
                    if (null != orderDetail.getPriceSelling()){//售价
                        data.set(9,String.valueOf(orderDetail.getPriceSelling()));
                    }
                    if (null != orderDetail.getQuantity()){//销量
                        data.set(10,String.valueOf(orderDetail.getQuantity()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//销售额
                        data.set(11,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getPayType()){//支付方式
                        data.set(12,PayDetail.payTypeDescription(orderDetail.getPayType()));
                    }
                    if (null != orderDetail.getPayFee()){//支付手续费
                        data.set(13,String.valueOf(orderDetail.getPayFee()));
                    }
                    if (null != orderDetail.getNetSales()){//净收益
                        data.set(14,String.valueOf(orderDetail.getNetSales()));
                    }
                    if (null != orderDetail.getVendorPercent()){//版税税率
                        data.set(15,String.valueOf(orderDetail.getVendorPercent()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//版税金额
                        data.set(16,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    if (null != orderDetail.getSalesModel()){//销售
                        if (0 == orderDetail.getSalesModel()){
                            data.set(17,"正常");
                        }else{
                            data.set(17,"预售");
                        }
                    }
                    if (StringUtils.hasLength(orderDetail.getPayCountry())){//支付国籍
                        data.set(18,orderDetail.getPayCountry());
                    }
                    if (null != orderDetail.getOrderFrom()){//订单来源
                        if (1 == orderDetail.getOrderFrom()){
                            data.set(19,"App");
                        }else {
                            data.set(19,"Web");
                        }
                    }
                    break;
                case EBookConstant.ExcelDownload.SELL_COUNT_DAY:

                case EBookConstant.ExcelDownload.SELL_COUNT_WEEK:
                    //"序号","开始日期","结束日期","销量","销售额","支付手续费","净收益","版税金额"
                    data.set(0, i++ +"");
                    if (null != orderDetail.getPurchasedDayStart()){//开始日期
                        data.set(1,String.valueOf(orderDetail.getPurchasedDayStart()));
                    }
                    if (null != orderDetail.getPurchasedDayEnd()){//结束日期
                        data.set(2,String.valueOf(orderDetail.getPurchasedDayEnd()));
                    }
                    if (null != orderDetail.getQuantity()){//销量
                        data.set(3,String.valueOf(orderDetail.getQuantity()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//销售额
                        data.set(4,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getPayFee()){//支付手续费
                        data.set(5,String.valueOf(orderDetail.getPayFee()));
                    }
                    if (null != orderDetail.getNetSales()){//净收益
                        data.set(6,String.valueOf(orderDetail.getNetSales()));
                    }
                    if (null != orderDetail.getIncomeVendor()){//版税金额
                        data.set(7,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    break;
                case EBookConstant.ExcelDownload.SELL_COUNT_MONTH:

                case EBookConstant.ExcelDownload.SELL_COUNT_YEAR:
                    //"序号","年份","销量","销售额","支付手续费","净收益","版税金额"
                    data.set(0, i++ +"");
                    if (null != orderDetail.getPurchasedDayStart()){//年份
                        data.set(1,String.valueOf(orderDetail.getPurchasedDayStart()));
                    }
                    if (null != orderDetail.getQuantity()){//销量
                        data.set(2,String.valueOf(orderDetail.getQuantity()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//销售额
                        data.set(3,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getPayFee()){//支付手续费
                        data.set(4,String.valueOf(orderDetail.getPayFee()));
                    }
                    if (null != orderDetail.getNetSales()){//净收益
                        data.set(5,String.valueOf(orderDetail.getNetSales()));
                    }
                    if (null != orderDetail.getIncomeVendor()){//版税金额
                        data.set(6,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    break;
                case EBookConstant.ExcelDownload.SELL_INFO_REDEEM_CODE:
                    //"序号","兑换码","书名","ESIN","原付款email","原付款订单号","原付款日期","售价","兑换时间"
                    break;
            }

            dataList.add(data);
        }

        return dataList;
    }
    private List<List<String>> getExcelDataVendor(List<EnyanOrderDetail> list,List<EnyanRefund> refundList,
                                                  int downloadType,int dataSize){
        int i = 1;
        List<List<String>> dataList = new ArrayList<>();
        if (downloadType == EBookConstant.ExcelDownload.REFUND_INFO){
            //"订单编号","订单时间","书名","销量变动","销售额变动HKD","退款原因及处理结果"
            for (EnyanRefund refund:refundList){
                List<String> data = this.getDefaultData(dataSize);
                data.set(0, i++ +"");
                if (StringUtils.hasLength(refund.getOrderNum())){//订单编号
                    data.set(1,refund.getOrderNum());
                }
                if (null != refund.getPurchasedAt()){//订单时间
                    data.set(2,DateFormatUtils.format(refund.getPurchasedAt(),"yyyy-MM-dd HH:mm:ss"));
                }
                if (StringUtils.hasLength(refund.getBookTitle())){//书名
                    data.set(3,refund.getBookTitle());
                }
                if (null != refund.getSalesVolumeDecrease()){//销量变动
                    data.set(4,refund.getSalesVolumeDecrease().toString());
                }
                if (null != refund.getIncomeTotalDecrease()){//销售额变动HKD
                    data.set(5,refund.getIncomeTotalDecrease().toString());
                }
                if (StringUtils.hasLength(refund.getReasonContent())){//退款原因及处理结果
                    data.set(6,refund.getReasonContent());
                }
                dataList.add(data);
            }
            return dataList;
        }
        for (EnyanOrderDetail orderDetail:list){
            List<String> data = this.getDefaultData(dataSize);
            switch (downloadType){
                case EBookConstant.ExcelDownload.SELL_INFO_BOOK:
                    //"序号","书名","销量","销售额","净收益","版税金额"
                    data.set(0, i++ +"");//序号
                    if (StringUtils.hasLength(orderDetail.getBookTitle())){//书名
                        data.set(1,orderDetail.getBookTitle());
                    }
                    if (null != orderDetail.getQuantity()){//销量
                        data.set(2,String.valueOf(orderDetail.getQuantity()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//"销售额"
                        data.set(3,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getNetSales()){//"净收益"
                        data.set(4,String.valueOf(orderDetail.getNetSales()));
                    }
                    if (null != orderDetail.getIncomeVendor()){//版税金额
                        data.set(5,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    break;
                case EBookConstant.ExcelDownload.SELL_INFO_ORDER:
                    //"序号","订单时间","订单编号","书名","定价","售价","销量","销售额","支付方式","支付手续费","净收益","版税金额"
                    data.set(0, i++ +"");
                    if (null != orderDetail.getPurchasedAt()){//订单时间
                        data.set(1,DateFormatUtils.format(orderDetail.getPurchasedAt(),"yyyy-MM-dd HH:mm:ss"));
                    }
                    if (StringUtils.hasLength(orderDetail.getOrderNum())){//订单编号
                        data.set(2,orderDetail.getOrderNum());
                    }
                    if (StringUtils.hasLength(orderDetail.getBookTitle())){//书名
                        data.set(3,orderDetail.getBookTitle());
                    }
                    if (null != orderDetail.getPriceFixed()){//定价
                        data.set(4,String.valueOf(orderDetail.getPriceFixed()));
                    }
                    if (null != orderDetail.getPriceSelling()){//售价
                        data.set(5,String.valueOf(orderDetail.getPriceSelling()));
                    }
                    if (null != orderDetail.getPriceSelling()){//销量
                        data.set(6,String.valueOf(orderDetail.getPriceSelling()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//"销售额"
                        data.set(7,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getPayType()){//支付方式
                        data.set(8,PayDetail.payTypeDescription(orderDetail.getPayType()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//支付手续费
                        data.set(9,String.valueOf(orderDetail.getPayFee()));
                    }
                    if (null != orderDetail.getNetSales()){//"净收益"
                        data.set(10,String.valueOf(orderDetail.getNetSales()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//版税金额
                        data.set(11,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    break;
                case EBookConstant.ExcelDownload.SELL_COUNT_DAY:

                case EBookConstant.ExcelDownload.SELL_COUNT_WEEK:
                    //"开始日期","结束日期","销量","销售额","支付手续费","版税金额"
                    data.set(0, i++ +"");
                    if (null != orderDetail.getPurchasedDayStart()){//开始日期
                        data.set(1,String.valueOf(orderDetail.getPurchasedDayStart()));
                    }
                    if (null != orderDetail.getPurchasedDayEnd()){//结束日期
                        data.set(2,String.valueOf(orderDetail.getPurchasedDayEnd()));
                    }
                    if (null != orderDetail.getQuantity()){//销量
                        data.set(3,String.valueOf(orderDetail.getQuantity()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//销售额
                        data.set(4,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getPayFee()){//支付手续费
                        data.set(5,String.valueOf(orderDetail.getPayFee()));
                    }
                    if (null != orderDetail.getIncomeVendor()){//版税金额
                        data.set(6,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    break;
                case EBookConstant.ExcelDownload.SELL_COUNT_MONTH:

                case EBookConstant.ExcelDownload.SELL_COUNT_YEAR:
                    //"年份","销量","销售额","支付手续费","版税金额"
                    data.set(0, i++ +"");
                    if (null != orderDetail.getPurchasedDayStart()){//年份
                        data.set(1,String.valueOf(orderDetail.getPurchasedDayStart()));
                    }
                    if (null != orderDetail.getQuantity()){//销量
                        data.set(2,String.valueOf(orderDetail.getQuantity()));
                    }
                    if (null != orderDetail.getIncomeTotal()){//销售额
                        data.set(3,String.valueOf(orderDetail.getIncomeTotal()));
                    }
                    if (null != orderDetail.getPayFee()){//支付手续费
                        data.set(4,String.valueOf(orderDetail.getPayFee()));
                    }
                    if (null != orderDetail.getIncomeVendor()){//版税金额
                        data.set(5,String.valueOf(orderDetail.getIncomeVendor()));
                    }
                    break;
            }

            dataList.add(data);
        }
        return dataList;
    }

    private List<String> getDefaultData(int dataSize){
        List<String> data = new ArrayList<>(dataSize);
        for (int i = 0; i < dataSize; i++) {
            data.add("");
        }
        return data;
    }
    /**
     * <p>获取自己管理的vendor列表</p>
     * @param
     * @return java.util.List<com.aaron.common.NameAndValue>
     * @since : 2024-11-04
     **/
    private List<NameAndValue> getNameAndValueList(){
        List<NameAndValue> publisherList = new ArrayList<>();
        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_ADMIN) || UserUtils.hasRole(InterfaceContant.RoleName.ROLE_FINANCE)){
            NameAndValue nameAndValue = new NameAndValue();
            nameAndValue.setName("--全部--");
            nameAndValue.setValue("-1");

            publisherList.add(nameAndValue);
            publisherList.addAll(Constant.publishersList);
            return publisherList;
        }
        IUser user = UserUtils.getCurrentLoginUser();
        CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
        //book.setPublisherId(customUserDetail.getPublisherId());
        List<String> publisherIdStrList = customUserDetail.getPublisherIdList();
        for (NameAndValue nameAndValue : Constant.publishersList) {
            if (publisherIdStrList.contains(nameAndValue.getValue())){
                publisherList.add(nameAndValue);
            }
        }
        return publisherList;
    }

    /**
     * <p>publisherId 是否合法</p>
     * @param publisherId
     * @return boolean
     * @since : 2024-11-05
     **/
    private boolean isPublisherValid(Long publisherId){
        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_ADMIN) || UserUtils.hasRole(InterfaceContant.RoleName.ROLE_FINANCE)){
            return true;
        }
        if (UserUtils.hasRole(InterfaceContant.RoleName.ROLE_VENDOR) == false){
            return false;
        }
        IUser user = UserUtils.getCurrentLoginUser();
        CustomUserDetail customUserDetail = (CustomUserDetail) user.getCustomDetail();
        //book.setPublisherId(customUserDetail.getPublisherId());
        List<String> publisherIdStrList = customUserDetail.getPublisherIdList();
        if (publisherIdStrList.contains(publisherId+"")){
            return true;
        }
        return false;
    }

}
