package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBlog;
import com.aaron.spring.service.EnyanBlogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/1/21
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/blog")
public class BlogController extends BaseController{
	@Resource
	private EnyanBlogService enyanBlogService;

	@RequestMapping(value = "/list")
	public String list(HttpServletRequest req, EnyanBlog record, ModelMap modelMap){
		//logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
		if (null == record){
			record = new EnyanBlog();
		}
		if (null == record.getPage()){
			record.setPage(new Page());
		}
		Map<String, Object> queryParams = new HashMap<>();

		// 获取分页参数
		String total = req.getParameter("total");
		String currentPage = req.getParameter("pageNo");

		if (StringUtils.hasLength(total)) {
			record.getPage().setTotalRecord(Integer.parseInt(total));
		}
		if (StringUtils.hasLength(currentPage)) {
			record.getPage().setCurrentPage(Integer.parseInt(currentPage));
		}


		// 高级查询条件：
		String searchText = req.getParameter("searchText");
		String searchType = req.getParameter("searchType");
		if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
			switch (searchType){
				case "0":
					record.setBlogTitle(searchText);
					break;
				case "1":
					break;

			}
			queryParams.put("searchText",searchText);
			queryParams.put("searchType",searchType);
		}

		record.addOrder(new OrderObj("create_at", InterfaceContant.OrderBy.DESC));
//        book.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
		Page<EnyanBlog> page = enyanBlogService.queryRecords(record.getPage(),record);
		record.setPage(page);
		record.excutePageLand(queryParams);

		modelMap.addAttribute("list",page.getRecords());
		modelMap.addAttribute("pageLand",record.getPageLand());
		modelMap.addAttribute("dto",record);
		modelMap.addAttribute("explan","文章管理");

		return "admin/blogList";
	}

	@RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
	public String getById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("getById");
		EnyanBlog record = enyanBlogService.queryRecordByPrimaryKey(id).getResult();

		modelMap.addAttribute("record",record);
		modelMap.addAttribute("publisherList", Constant.publishersList);
		return "admin/blogAdd";
	}

	@RequestMapping("/del-{id}")
	public String delById(@PathVariable("id")Long id , ModelMap modelMap){
		log.debug("method delById");
		enyanBlogService.deleteRecordByPrimaryKey(id);
		return "redirect:/blog/list";
	}

	@RequestMapping("/addUI")
	public String addCompanyUI(EnyanBlog record, ModelMap modelMap){
		log.debug("method addCompanyUI");
		record.setIsDeleted(0);
		modelMap.addAttribute("publisherList",Constant.publishersList);
		modelMap.addAttribute("record",record);
		return "admin/blogAdd";
	}

	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public String saveCompany(HttpServletRequest request, EnyanBlog record, ModelMap modelMap){
		log.debug("method saveBlog："+record);
		modelMap.addAttribute("publisherList",Constant.publishersList);
		if (StringUtils.hasLength(record.getBlogTitle()) == false){
			this.setErrorMsg(modelMap,"请填写文章名称");
			modelMap.addAttribute("record",record);
			return "admin/blogAdd";
		}
		if (StringUtils.hasLength(record.getBlogAbstract()) == false){
			this.setErrorMsg(modelMap,"请填写文章摘要");
			modelMap.addAttribute("record",record);
			return "admin/blogAdd";
		}
		if (StringUtils.hasLength(record.getAuthor()) == false){
			this.setErrorMsg(modelMap,"请填写作者");
			modelMap.addAttribute("record",record);
			return "admin/blogAdd";
		}
		if (StringUtils.hasLength(record.getBlogContent()) == false){
			this.setErrorMsg(modelMap,"请填写文章内容");
			modelMap.addAttribute("record",record);
			return "admin/blogAdd";
		}

		if (null == record.getBlogId()){
			this.setSuccessMsg(modelMap,"添加文章成功");
			record.setReadCount(0);
			record.setLikeCount(0);
			record.setIsDeleted(0);
			record.setCreateAt(new Date());
			enyanBlogService.addRecord(record);
		}else {
			this.setSuccessMsg(modelMap,"修改文章成功");
			enyanBlogService.updateRecord(record);
		}
		return "redirect:/blog/list";
	}
}
