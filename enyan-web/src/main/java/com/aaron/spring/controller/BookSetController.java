package com.aaron.spring.controller;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.NameAndValue;
import com.aaron.common.NameAndValueDTO;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestBookSet;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.BookWebInfo;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookSet;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanBookSetService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2017/11/17
 * @Modified By:
 */
@Slf4j
@Controller
@RequestMapping("/bookSetAdmin")
public class BookSetController extends BaseController{
    @Resource
    private EnyanBookSetService enyanBookSetService;

    @Resource
    private EnyanBookService enyanBookService;

    @RequestMapping(value = "/list")
    public String discountPage(HttpServletRequest req, EnyanBookSet record, ModelMap modelMap){
        log.debug("list");
        try {
            if (null == record){
                record = new EnyanBookSet();
            }
            if (null == record.getPage()){
                record.setPage(new Page());
            }
            Map<String, Object> queryParams = new HashMap<>();

            // 获取分页参数
            String total = req.getParameter("total");
            String currentPage = req.getParameter("pageNo");

            if (StringUtils.hasLength(total)) {
                record.getPage().setTotalRecord(Integer.parseInt(total));
            }
            if (StringUtils.hasLength(currentPage)) {
                record.getPage().setCurrentPage(Integer.parseInt(currentPage));
            }

            // 高级查询条件：
            String searchText = req.getParameter("searchText");
            String searchType = req.getParameter("searchType");
            if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
                switch (searchType){
                    case "0":
                        record.setSetName(searchText);
                        break;
                }
                queryParams.put("searchText",searchText);
                queryParams.put("searchType",searchType);
            }
            record.addOrder(new OrderObj("show_order", InterfaceContant.OrderBy.DESC));
            Page<EnyanBookSet> page = enyanBookSetService.queryRecords(record.getPage(), record);

            record.setPage(page);
            record.excutePageLand(queryParams);

            modelMap.addAttribute("list",page.getRecords());
            modelMap.addAttribute("pageLand", record.getPageLand());

            modelMap.addAttribute("record", record);
            modelMap.addAttribute("explan","书系管理");
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "admin/bookSetList";
    }
    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("getById");
        EnyanBookSet bookSet = enyanBookSetService.queryRecordByPrimaryKey(id).getResult();
        if (null != bookSet.getBookWebInfo()){
            bookSet.setBookWebSalePapers(bookSet.getBookWebInfo().getSalePapersJson());
            bookSet.setBookWebVersions(bookSet.getBookWebInfo().getVersionsJson());
        }
        modelMap.addAttribute("enyanBookSet",bookSet);
        return "admin/bookSetAdd";
    }

    @RequestMapping(value = "/set-{id}", method = RequestMethod.GET)
    public String setById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("setById");
        EnyanBookSet record = enyanBookSetService.queryRecordByPrimaryKey(id).getResult();
        modelMap.addAttribute("bookIDsList", Constant.booksList);
        modelMap.addAttribute("record",record);

        List<NameAndValue> selectedList = enyanBookService.findBookNameAndValueBySetId(id);
        
        String[] ids = new String[selectedList.size()];
        for (int i = 0; i < selectedList.size(); i++) {
            ids[i] = selectedList.get(i).getValue();
        }
        record.setBookIDs(ids);
        record.setBookIDsOld(ids);
        return "admin/bookSetEdit";
    }
    /*
    @RequestMapping(value = "/associate-{id}", method = RequestMethod.GET)
    public String associateById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("associate");
        EnyanBookSet EnyanBookSet = enyanBookSetService.queryRecordByPrimaryKey(id).getResult();
        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setDiscountSingleIsValid(Constant.BYTE_VALUE_1);
        enyanBook.setDiscountSingleId(id);
        enyanBook.setDiscountSingleStartTime(EnyanBookSet.getStartTime());
        enyanBook.setDiscountSingleEndTime(EnyanBookSet.getEndTime());
        enyanBook.setDiscountSingleValue(EnyanBookSet.getDiscountSingleValue());
        enyanBook.setDiscountSingleType(Constant.BYTE_VALUE_0);
        enyanBook.setDiscountSingleDescription(enyanBook.getBookDiscountDescription());
        enyanBookService.updateBookSingleDiscountAssociateNDiscount(enyanBook);
        return "redirect:/closePage";
    }*/

    /**
     * <p>保存折扣与信息的关联</p>
     * @param request
     * @param modelMap
     * @return java.lang.String
     * @since : 2023/5/23
     **/
    @RequestMapping(value = "/saveBookSet", method = RequestMethod.POST)
    public String saveBookSet(HttpServletRequest request, EnyanBookSet enyanBookSet, ModelMap modelMap){
        log.debug("method saveBookDiscount："+ enyanBookSet);
        //EnyanBookSet record = enyanBookSetService.queryRecordByPrimaryKey(enyanBookSet.getSetId()).getResult();

        String[] ids = enyanBookSet.getBookIDs();
        String[] idsOld = enyanBookSet.getBookIDsOld();
        if (idsOld == null){
            idsOld = new String[]{};
        }

        EnyanBook enyanBook = new EnyanBook();
        enyanBook.setSetId(enyanBookSet.getSetId());
        enyanBook.setSetName(enyanBookSet.getSetName());

        enyanBookService.updateBookSet(enyanBook,ids,idsOld);
        return "redirect:/closePage";
    }

    @RequestMapping("/del-{id}")
    public String delById(@PathVariable("id")Long id , ModelMap modelMap){
        log.debug("method delById");
        enyanBookSetService.deleteRecordByPrimaryKey(id);
        return "redirect:/bookSetAdmin/list";
    }

    @ResponseBody
    @RequestMapping(value = "/reset")
    public ExecuteResult<String> reset(@RequestBody RestBookSet restObj, ModelMap modelMap,
                                       HttpServletRequest request, HttpServletResponse response){
        Long id = restObj.getSetId();

        ExecuteResult<String> result = new ExecuteResult<>();
        log.debug("SetId:{}",id);
        if (null == id ){
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_PARAM_INVALID);
            result.addErrorMessage(RestBaseController.ReturnInfo.ERROR_E1);
            return result;
        }
        enyanBookSetService.resetCache(id);
        return result;
    }

    @RequestMapping("/addUI")
    public String addUI(EnyanBookSet enyanBookSet){
        log.debug("method addUI");
        enyanBookSet.setIsValid(1);
        enyanBookSet.setCanAllBuy(1);
        return "admin/bookSetAdd";
    }

    /**
     * <p>添加套装信息</p>
     * @param request
     * @param enyanBookSet
     * @param modelMap
     * @return java.lang.String
     * @since : 2023/5/23
     **/
    @RequestMapping(value = "/saveSet", method = RequestMethod.POST)
    public String saveSet(HttpServletRequest request, EnyanBookSet enyanBookSet, ModelMap modelMap){
        log.debug("method saveSet："+ enyanBookSet);

        if (StringUtils.hasLength(enyanBookSet.getSetName()) == false){
            this.setErrorMsg(modelMap,"请填写书系名");
            return "admin/bookSetAdd";
        }
        if (null == enyanBookSet.getDiscountValue()){
            enyanBookSet.setDiscountValue(100);
        }
        if (enyanBookSet.getDiscountValue() < 100){
            enyanBookSet.setIsDiscountValid(1);
        }else{
            enyanBookSet.setIsDiscountValid(0);
        }
        if (StringUtils.hasLength(enyanBookSet.getBookWebSalePapers()) || StringUtils.hasLength(enyanBookSet.getBookWebVersions())){
            BookWebInfo bookWebInfo = new BookWebInfo();
            if (StringUtils.hasLength(enyanBookSet.getBookWebSalePapers())){
                NameAndValueDTO nameAndValueDTO = JSON.parseObject(enyanBookSet.getBookWebSalePapers(),NameAndValueDTO.class);
                if (null != nameAndValueDTO){
                    bookWebInfo.setSalePapers(nameAndValueDTO.getValues());
                }
            }
            if (StringUtils.hasLength(enyanBookSet.getBookWebVersions())){
                NameAndValueDTO nameAndValueDTO = JSON.parseObject(enyanBookSet.getBookWebVersions(),NameAndValueDTO.class);
                if (null != nameAndValueDTO){
                    bookWebInfo.setVersions(nameAndValueDTO.getValues());
                }
            }
            enyanBookSet.setBookWeb(JSON.toJSONString(bookWebInfo));
        }
        if (null == enyanBookSet.getSetId()){
            this.setSuccessMsg(modelMap,"添加书系成功");
            enyanBookSetService.addRecord(enyanBookSet);
        }else {
            this.setSuccessMsg(modelMap,"修改书系成功");
            enyanBookSetService.updateRecord(enyanBookSet);
        }
        return "redirect:/bookSetAdmin/list";
    }

}
