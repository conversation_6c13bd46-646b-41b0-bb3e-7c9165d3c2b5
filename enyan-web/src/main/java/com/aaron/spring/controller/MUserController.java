package com.aaron.spring.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/muserController")
public class MUserController {
    /*private final Log logger = LogFactory.getLog(MUserController.class);
    private MUserServiceI muserService;

    public MUserServiceI getMuserService() {
        return muserService;
    }

    @Autowired
    public void setMuserService(MUserServiceI muserService) {
        this.muserService = muserService;
    }

    @RequestMapping(value = "/listUser")
    public String listUser(HttpServletRequest request) {

        List<MUser> list = muserService.getAll();
        request.setAttribute("userlist", list);
        return "admin/listUser";
    }

    @RequestMapping(value = "/addUser")
    public String addUser(MUser muser) {

        String id = UUID.randomUUID().toString();
        muser.setId(id);
        muserService.insert(muser);
        return "redirect:/muserController/listUser.do";
    }

    @RequestMapping(value = "/deleteUser")
    public String deleteUser(String id) {

        muserService.delete(id);
        return "redirect:/muserController/listUser.do";
    }

    @RequestMapping(value = "/updateUserUI")
    public String updateUserUI(String id, HttpServletRequest request) {

        MUser muser = muserService.selectByPrimaryKey(id);
        request.setAttribute("user", muser);
        return "updateUser";
    }

    @RequestMapping(value = "/updateUser")
    public String updateUser(MUser muser) {
        muserService.update(muser);
        return "redirect:/muserController/listUser.do";
    }*/

    /**
     * 分页时获取所有的学生
     *
     * @return
     */
    /*@RequestMapping("pageStus")
    public String pageAllUsers(HttpServletRequest req, Pages pager) {
        try {
            Query query = new Query();
            //Pages pager = new Pages();
            Map<String, Object> queryParams = new HashMap<String, Object>();
            logger.info("queryParams:" + queryParams);
            // 获取分页参数
            String total = req.getParameter("total");
            String currentPage = req.getParameter("page");

            if (StringUtils.hasLength(total)) {
                pager.setTotalRows(Integer.parseInt(total));
            }
            if (StringUtils.hasLength(currentPage)) {
                pager.setCurrentPage(Integer.parseInt(currentPage));
            }

            // 高级查询条件：学生真实姓名
            String trueNameForQuery = req.getParameter("trueNameForQuery");
            if (StringUtils.hasLength(trueNameForQuery)) {
                queryParams.put(" u.REAL_NAME like ", "'%" + trueNameForQuery
                        + "%'");
            }

            query.setPages(pager);
            query.setQueryParams(queryParams);
            List<MUser> users = muserService.findUserPage(query);

            req.setAttribute("userlist", users);
            req.setAttribute("pager", PageContext.getInstance().get());
            return "admin/listUserPage";
        } catch (Exception e) {
            logger.error("pageAllUsers error : " + e.getMessage());
        }
        return null;
    }

    @RequestMapping("getPager")
    @ResponseBody
    public Pages getPager() {
        logger.info("method getPager");
        return PageContext.getInstance().get();
    }*/
}
