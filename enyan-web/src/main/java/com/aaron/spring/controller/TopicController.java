package com.aaron.spring.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodTopic;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.service.PodTopicService;
import com.aaron.spring.service.PodPodcastService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description: 专题后台管理控制器
 */
@Slf4j
@Controller
@RequestMapping("/admin/topic")
public class TopicController extends BaseController {

    @Resource
    private PodTopicService podTopicService;

    @Resource
    private PodPodcastService podPodcastService;
    
    @RequestMapping(value = "/list")
    public String list(HttpServletRequest req, PodTopic record, ModelMap modelMap) {
        if (null == record) {
            record = new PodTopic();
        }
        if (null == record.getPage()) {
            record.setPage(new Page());
        }
        
        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            record.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            record.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件
        Map<String, Object> queryParams = new HashMap<>();
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        String podcastId = req.getParameter("podcastId");

        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            record.setSearchText(searchText);
            record.setSearchType(Integer.parseInt(searchType));
            queryParams.put("searchText", searchText);
            queryParams.put("searchType", searchType);

            switch (searchType) {
                case "0": // 按名称搜索
                    record.setTitle(searchText);
                    break;
                case "1": // 按ID搜索
                    try {
                        record.setTopicId(Long.parseLong(searchText));
                    } catch (NumberFormatException e) {
                        // 如果ID格式不正确，设置一个不存在的ID，这样就不会查到任何结果
                        record.setTopicId(-1L);
                    }
                    break;
            }
        }

        // 播客筛选
        if (StringUtils.hasLength(podcastId)) {
            record.setPodcastId(Long.parseLong(podcastId));
            queryParams.put("podcastId", podcastId);
        }

        // 只查询未删除的专题
        record.setIsDeleted(0);

        Page<PodTopic> page = podTopicService.queryRecords(record.getPage(), record);
        record.setPage(page);
        record.excutePageLand(queryParams);

        modelMap.put("page", page);
        modelMap.put("record", record);

        // 获取所有播客列表用于筛选下拉框
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());
        
        return "admin/topic/list";
    }

    @RequestMapping("/addUI")
    public String addTopicUI(PodTopic record, ModelMap modelMap) {
        log.debug("method addTopicUI");
        record.setIsDeleted(0);
        modelMap.put("record", record);

        // 获取所有播客列表用于下拉选择
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());

        return "admin/topic/form";
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public String saveTopic(HttpServletRequest request, PodTopic record, ModelMap modelMap) {
        log.debug("method saveTopic：" + record);
        
        // 参数验证
        if (StringUtils.hasLength(record.getTitle()) == false) {
            this.setErrorMsg(modelMap, "请填写专题名称");
            modelMap.addAttribute("record", record);
            // 重新获取播客列表
            PodPodcast podcastQuery = new PodPodcast();
            podcastQuery.setIsDeleted(0);
            Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
            modelMap.put("podcastList", podcastPage.getRecords());
            return "admin/topic/form";
        }

        if (record.getPodcastId() == null) {
            this.setErrorMsg(modelMap, "请选择所属播客");
            modelMap.addAttribute("record", record);
            // 重新获取播客列表
            PodPodcast podcastQuery = new PodPodcast();
            podcastQuery.setIsDeleted(0);
            Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
            modelMap.put("podcastList", podcastPage.getRecords());
            return "admin/topic/form";
        }

        // 新增逻辑
        if (null == record.getTopicId()) {
            this.setSuccessMsg(modelMap, "添加专题成功");
            record.setEpisodeCount(0);
            record.setIsDeleted(0);
            record.setCreatedAt(new Date());
            podTopicService.addRecord(record);
        } 
        // 修改逻辑
        else {
            this.setSuccessMsg(modelMap, "修改专题成功");
            podTopicService.updateRecord(record);
        }
        
        return "redirect:/admin/topic/list";
    }

    @RequestMapping("/del-{id}")
    public String deleteTopic(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        ExecuteResult<String> result = podTopicService.deleteRecordByPrimaryKey(id);
        if (result.isSuccess()) {
            redirectAttributes.addFlashAttribute("successMsg", "删除专题成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "删除专题失败：" + result.getErrorMessages());
        }
        return "redirect:/admin/topic/list";
    }

    @RequestMapping(value = "/get-{id}")
    public String get(@PathVariable Long id, ModelMap modelMap) {
        ExecuteResult<PodTopic> result = podTopicService.queryRecordByPrimaryKey(id);
        modelMap.put("record", result.getResult());

        // 获取所有播客列表用于下拉选择
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());

        return "admin/topic/form";
    }

    /**
     * 批量删除专题
     */
    @RequestMapping(value = "/batchDelete", method = RequestMethod.POST)
    public String batchDelete(@RequestParam("ids") Long[] ids, RedirectAttributes redirectAttributes) {
        log.debug("批量删除专题，ids: " + java.util.Arrays.toString(ids));
        int successCount = 0;
        int failCount = 0;

        for (Long id : ids) {
            ExecuteResult<String> result = podTopicService.deleteRecordByPrimaryKey(id);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failCount++;
            }
        }

        if (failCount == 0) {
            redirectAttributes.addFlashAttribute("successMsg", "成功删除 " + successCount + " 个专题");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "删除完成：成功 " + successCount + " 个，失败 " + failCount + " 个");
        }

        return "redirect:/admin/topic/list";
    }

}
