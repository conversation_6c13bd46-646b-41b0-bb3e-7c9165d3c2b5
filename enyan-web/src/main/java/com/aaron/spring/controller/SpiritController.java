package com.aaron.spring.controller;

import co.endao.util.SpiritBookUtil;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.NameAndValue;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.SystemUtil;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanConfig;
import com.aaron.spring.model.EnyanSpirit;
import com.aaron.spring.service.BaseDataService;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanConfigService;
import com.aaron.spring.service.EnyanSpiritService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.*;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  11/16/20
 * @Modified By:
 */
@Controller
@RequestMapping("/spirit")
public class SpiritController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private EnyanSpiritService enyanSpiritService;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private BaseDataService baseDataService;

    @Resource
    private EnyanConfigService enyanConfigService;

    @RequestMapping(value = "/spirits")
    public String spiritsPage(HttpServletRequest req, EnyanSpirit enyanSpirit, ModelMap modelMap){
        //logger.debug("web path:"+System.getProperty(Constant.ENVIRONMENT_PATH));
        if (null == enyanSpirit){
            enyanSpirit = new EnyanSpirit();
        }
        if (null == enyanSpirit.getPage()){
            enyanSpirit.setPage(new Page());
        }
        Map<String, Object> queryParams = new HashMap<>();

        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            enyanSpirit.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            enyanSpirit.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件：
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            switch (searchType){
                case "0":
                    enyanSpirit.setName(searchText);
                    break;
                case "1":
                    enyanSpirit.setAuthor(searchText);
                    break;
            }
            queryParams.put("searchText",searchText);
            queryParams.put("searchType",searchType);
        }

        enyanSpirit.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
        //enyanSpirit.addOrder(new OrderObj("recommended_order", InterfaceContant.OrderBy.DESC));
        Page<EnyanBook> page = enyanSpiritService.queryRecords(enyanSpirit.getPage(), enyanSpirit);
        enyanSpirit.setPage(page);
        enyanSpirit.excutePageLand(queryParams);

        modelMap.addAttribute("list",page.getRecords());
        modelMap.addAttribute("pageLand", enyanSpirit.getPageLand());
        modelMap.addAttribute("enyanSpirit", enyanSpirit);
        modelMap.addAttribute("explan","灵修书籍管理");

        return "admin/spirits";
    }

    @RequestMapping(value = "/get-{id}", method = RequestMethod.GET)
    public String getBookById(@PathVariable("id")Long id , ModelMap modelMap){
        //logger.debug("getBookById");
        EnyanSpirit spirit = enyanSpiritService.queryRecordByPrimaryKey(id).getResult();

        String[] ids = {spirit.getBookId().toString()};
        spirit.setBookIDs(ids);

        modelMap.addAttribute("enyanSpirit",spirit);
        modelMap.addAttribute("bookIDsList", enyanBookService.getBookIDsList());

        return "admin/spiritEdit";
    }

    @RequestMapping("/shelfStatus-{id}-{status}")
    public String shelfStatusBookById(@PathVariable("id")Long id , @PathVariable("status")String status, ModelMap modelMap){
        logger.debug("method shelfStatusBookById");

        EnyanSpirit enyanSpiritNew = enyanSpiritService.queryRecordByPrimaryKey(id).getResult();
        if (enyanSpiritNew == null){
            return "redirect:/spirit/spirits";
        }

        EnyanSpirit enyanSpirit = new EnyanSpirit();
        enyanSpirit.setSpiritId(id);
        enyanSpirit.setBookId(enyanSpiritNew.getBookId());
        if ("0".equals(status)){
            enyanSpirit.setShelfStatus(Constant.BYTE_VALUE_0);

        }else if("1".equals(status)){
            enyanSpirit.setShelfStatus(Constant.BYTE_VALUE_1);
        }
        enyanSpiritService.updateByPrimaryKeySelective(enyanSpirit,enyanSpirit.getBookId());
        if (Constant.BYTE_VALUE_1.equals(enyanSpirit.getShelfStatus())){//有上架的书籍需要更新数据
            this.updateSpiritTimeConfig();
        }
        return "redirect:/spirit/spirits";
    }

    @RequestMapping("/addSpiritUI")
    public String addSpiritUI(EnyanSpirit enyanSpirit, ModelMap modelMap){
        logger.debug("method addBookUI");
        this.initBookValue(modelMap);
        return "admin/spiritAdd";
    }

    @RequestMapping(value = "/saveSpirit", method = RequestMethod.POST)
    public String save(EnyanSpirit enyanSpirit,
                                  @RequestParam(value = "uploadBook", required = false) MultipartFile filedataBook,
                                  ModelMap modelMap) {
        logger.debug("method save：" + enyanSpirit);
        this.initBookValue(modelMap);

        if (StringUtils.isEmpty(enyanSpirit.getName())) {
            this.setErrorMsg(modelMap, "请填写书籍名称");
            return "admin/spiritAdd";
        }
        if (StringUtils.isEmpty(enyanSpirit.getAuthor())) {
            this.setErrorMsg(modelMap, "请填写作者");
            return "admin/spiritAdd";
        }

        if (StringUtils.isEmpty(enyanSpirit.getBookDescription())) {
            this.setErrorMsg(modelMap, "请填写本书简介");
            return "admin/spiritAdd";
        }
        if (StringUtils.isEmpty(enyanSpirit.getAuthorDescription())) {
            this.setErrorMsg(modelMap, "请填写作者简介");
            return "admin/spiritAdd";
        }
        if (enyanSpirit.getBookDescription().length() >500){
            this.setErrorMsg(modelMap, "本书简介应在500字以内");
            return "admin/spiritAdd";
        }
        if (enyanSpirit.getAuthorDescription().length() >500){
            this.setErrorMsg(modelMap, "作者简介应在500字以内");
            return "admin/spiritAdd";
        }
        if (enyanSpirit.getCopyright().length() > 500){
            this.setErrorMsg(modelMap, "版权应在500字以内");
            return "admin/spiritAdd";
        }
        if (StringUtils.isEmpty(enyanSpirit.getBookImgUrl())) {
            this.setErrorMsg(modelMap, "请填写封面");
            return "admin/spiritAdd";
        }
        if (StringUtils.isEmpty(enyanSpirit.getCopyright())) {
            this.setErrorMsg(modelMap, "请填写版权");
            return "admin/spiritAdd";
        }
        if (StringUtils.isEmpty(enyanSpirit.getDays())) {
            this.setErrorMsg(modelMap, "请填写天数");
            return "admin/spiritAdd";
        }
        if (StringUtils.isEmpty(enyanSpirit.getInfoImgUrl())) {
            this.setErrorMsg(modelMap, "请填写书籍图片信息");
            return "admin/spiritAdd";
        }
        if (StringUtils.isEmpty(enyanSpirit.getToBuyImgUrl())) {
            this.setErrorMsg(modelMap, "请填写去购买的图片");
            return "admin/spiritAdd";
        }
        if (enyanSpirit.getBookIDs().length == 0){
            this.setErrorMsg(modelMap, "请选择关联的书籍");
            return "admin/spiritAdd";
        }
        if (filedataBook == null || filedataBook.isEmpty()) {
            this.setErrorMsg(modelMap, "请选择上传书籍！");
            return "admin/spiritAdd";
        }
        try {
            enyanSpirit.setBookId(Long.parseLong(enyanSpirit.getBookIDs()[0]));
            if (filedataBook != null && !filedataBook.isEmpty()) {
                String fileName = UUID.randomUUID().toString() + ".epub";
                String epubPath = SystemUtil.getEpubOfSpiritBaseDir() + fileName;

                File bookFile = new File(epubPath);

                filedataBook.transferTo(bookFile);

                enyanSpirit.setFileName(fileName);
                enyanSpirit.setBookVersion(filedataBook.getOriginalFilename());

                SpiritBookUtil.Companion.get().decompressBookInWeb(fileName, enyanSpirit.getBookId());
            }
            enyanSpiritService.addRecord(enyanSpirit);

            this.updateSpiritTimeConfig();
        } catch (Exception e){
            logger.error("上传书籍失败.", e);
            modelMap.addAttribute("msg", "上传书籍失败！");
            this.setErrorMsg(modelMap, "上传加密电子书失败！");
            return "admin/spiritAdd";
        }
        /*
        if (null == publication.getId()){
            this.setSuccessMsg(modelMap,"添加加密书籍成功");
            publicationService.addRecord(publication);
        }else {
            this.setSuccessMsg(modelMap,"修改加密书籍成功");
            publicationService.updateRecord(publication);
        */
            /*EnyanBook enyanBook = new EnyanBook();
            enyanBook.setPublisherId(enyanPublisher.getPublisherId());
            enyanBook.setPublisherName(enyanPublisher.getPublisherName());
            enyanBookService.updateBookByExample(enyanBook);*/

        return "redirect:/spirit/spirits";
    }

    @RequestMapping(value = "/updateSpirit1", method = RequestMethod.POST)
    public String update1(EnyanSpirit enyanSpirit, ModelMap modelMap) {
        logger.debug("method save：" + enyanSpirit);
        this.initBookValue(modelMap);

        if (StringUtils.isEmpty(enyanSpirit.getName())) {
            this.setErrorMsg(modelMap, "请填写书籍名称");
            return "admin/spiritEdit";
        }
        if (StringUtils.isEmpty(enyanSpirit.getAuthor())) {
            this.setErrorMsg(modelMap, "请填写作者");
            return "admin/spiritEdit";
        }
        if (StringUtils.isEmpty(enyanSpirit.getBookDescription())) {
            this.setErrorMsg(modelMap, "请填写本书简介");
            return "admin/spiritEdit";
        }
        if (StringUtils.isEmpty(enyanSpirit.getAuthorDescription())) {
            this.setErrorMsg(modelMap, "请填写作者简介");
            return "admin/spiritEdit";
        }
        if (StringUtils.isEmpty(enyanSpirit.getCopyright())) {
            this.setErrorMsg(modelMap, "请填写版权");
            return "admin/spiritEdit";
        }
        if (StringUtils.isEmpty(enyanSpirit.getDays())) {
            this.setErrorMsg(modelMap, "请填写天数");
            return "admin/spiritEdit";
        }
        if (enyanSpirit.getBookDescription().length() >500){
            this.setErrorMsg(modelMap, "本书简介应在500字以内");
            return "admin/spiritEdit";
        }
        if (enyanSpirit.getAuthorDescription().length() >500){
            this.setErrorMsg(modelMap, "作者简介应在500字以内");
            return "admin/spiritEdit";
        }
        if (enyanSpirit.getCopyright().length() >500){
            this.setErrorMsg(modelMap, "版权应在500字以内");
            return "admin/spiritEdit";
        }

        enyanSpiritService.updateRecord(enyanSpirit);
        //this.updateSpiritTimeConfig();

        return "redirect:/spirit/spirits";
    }

    @RequestMapping(value = "/updateSpirit2", method = RequestMethod.POST)
    public String update2(EnyanSpirit enyanSpirit, ModelMap modelMap) {
        logger.debug("method save：" + enyanSpirit);
        this.initBookValue(modelMap);

        if (StringUtils.isEmpty(enyanSpirit.getBookImgUrl())) {
            this.setErrorMsg(modelMap, "请填写封面");
            return "admin/spiritEdit";
        }

        if (StringUtils.isEmpty(enyanSpirit.getInfoImgUrl())) {
            this.setErrorMsg(modelMap, "请填写书籍图片信息");
            return "admin/spiritEdit";
        }
        if (StringUtils.isEmpty(enyanSpirit.getToBuyImgUrl())) {
            this.setErrorMsg(modelMap, "请填写去购买的图片");
            return "admin/spiritEdit";
        }

        enyanSpiritService.updateRecord(enyanSpirit);
        //this.updateSpiritTimeConfig();

        return "redirect:/spirit/spirits";
    }

    @RequestMapping(value = "/updateSpirit3", method = RequestMethod.POST)
    public String update3(EnyanSpirit enyanSpirit, ModelMap modelMap) {
        logger.debug("method save：" + enyanSpirit);
        this.initBookValue(modelMap);

        if (enyanSpirit.getBookIDs().length == 0){
            this.setErrorMsg(modelMap, "请选择关联的书籍");
            return "admin/spiritEdit";
        }
        enyanSpirit.setBookId(Long.parseLong(enyanSpirit.getBookIDs()[0]));
        enyanSpiritService.updateRecord(enyanSpirit);
        //this.updateSpiritTimeConfig();

        return "redirect:/spirit/spirits";
    }

    @RequestMapping(value = "/updateSpirit4", method = RequestMethod.POST)
    public String update4(EnyanSpirit enyanSpirit,
                          @RequestParam(value = "uploadBook", required = false) MultipartFile filedataBook,
                          ModelMap modelMap) {
        logger.debug("method save：" + enyanSpirit);
        this.initBookValue(modelMap);

        if (filedataBook == null || filedataBook.isEmpty()) {
            this.setErrorMsg(modelMap, "请选择上传书籍！");
            return "admin/spiritEdit";
        }
        try {

            if (filedataBook != null && !filedataBook.isEmpty()) {
                EnyanSpirit spirit = enyanSpiritService.queryRecordByPrimaryKey(enyanSpirit.getSpiritId()).getResult();
                String fileName = spirit.getFileName();
                String epubPath = SystemUtil.getEpubOfSpiritBaseDir() + fileName;

                File bookFile = new File(epubPath);

                filedataBook.transferTo(bookFile);

                SpiritBookUtil.Companion.get().decompressBookInWeb(fileName, spirit.getBookId());

                EnyanSpirit newSpirit = new EnyanSpirit();
                newSpirit.setBookVersion(filedataBook.getOriginalFilename());
                newSpirit.setSpiritId(spirit.getSpiritId());
                newSpirit.setBookId(spirit.getBookId());
                enyanSpiritService.updateByPrimaryKeySelective(newSpirit,newSpirit.getBookId());
            }
        } catch (Exception e){
            logger.error("上传书籍失败.", e);
            modelMap.addAttribute("msg", "上传书籍失败！");
            this.setErrorMsg(modelMap, "上传加密电子书失败！");
            return "admin/spiritEdit";
        }

        return "redirect:/spirit/spirits";
    }

    private void initBookValue(ModelMap modelMap){
        List<NameAndValue> bookIdsList = new ArrayList<>();
        NameAndValue nameAndValue = new NameAndValue();
        nameAndValue.setValue("-1");
        nameAndValue.setName("暂无书籍");

        bookIdsList.add(nameAndValue);
        bookIdsList.addAll(enyanBookService.getBookIDsList());

        modelMap.addAttribute("bookIDsList", bookIdsList);
    }

    /**
     * <p>修改灵修书籍后直接更新config</p>
     * @param
     * @return: void
     * @since : 11/20/20
     */
    private void updateSpiritTimeConfig(){
        EnyanConfig enyanConfig = new EnyanConfig();
        enyanConfig.setConfigId(252L);
        enyanConfig.setConfigValue(String.valueOf(System.currentTimeMillis()));
        enyanConfigService.updateRecord(enyanConfig);
    }
}
