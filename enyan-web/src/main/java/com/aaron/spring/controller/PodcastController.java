package com.aaron.spring.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.model.PodEpisode;
import com.aaron.spring.model.PodTopic;
import com.aaron.spring.service.PodPodcastService;
import com.aaron.spring.service.PodEpisodeService;
import com.aaron.spring.service.PodTopicService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Description: 播客后台管理控制器
 */
@Slf4j
@Controller
@RequestMapping("/admin/podcast")
public class PodcastController extends BaseController {

    @Resource
    private PodPodcastService podPodcastService;

    @Resource
    private PodEpisodeService podEpisodeService;

    @Resource
    private PodTopicService podTopicService;
    
    @RequestMapping(value = "/list")
    public String list(HttpServletRequest req, PodPodcast record, ModelMap modelMap) {
        if (null == record) {
            record = new PodPodcast();
        }
        if (null == record.getPage()) {
            record.setPage(new Page());
        }
        
        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");

        if (StringUtils.hasLength(total)) {
            record.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            record.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }

        // 高级查询条件
        Map<String, Object> queryParams = new HashMap<>();
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");

        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            record.setSearchText(searchText);
            record.setSearchType(Integer.parseInt(searchType));
            queryParams.put("searchText", searchText);
            queryParams.put("searchType", searchType);

            switch (searchType) {
                case "0": // 按名称搜索
                    record.setTitle(searchText);
                    break;
                case "1": // 按ID搜索
                    try {
                        record.setPodcastId(Long.parseLong(searchText));
                    } catch (NumberFormatException e) {
                        // 如果ID格式不正确，设置一个不存在的ID，这样就不会查到任何结果
                        record.setPodcastId(-1L);
                    }
                    break;
            }
        }

        Page<PodPodcast> page = podPodcastService.queryRecords(record.getPage(), record);
        record.setPage(page);
        record.excutePageLand(queryParams);

        modelMap.put("page", page);
        modelMap.put("record", record);
        
        return "admin/podcast/list";
    }

    @RequestMapping("/addUI")
    public String addPodcastUI(PodPodcast record, ModelMap modelMap) {
        log.debug("method addPodcastUI");
        record.setIsDeleted(0);
        modelMap.put("record", record);
        return "admin/podcast/form";
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public String savePodcast(HttpServletRequest request, PodPodcast record, ModelMap modelMap) {
        log.debug("method savePodcast：" + record);
        
        // 参数验证
        if (StringUtils.hasLength(record.getTitle()) == false) {
            this.setErrorMsg(modelMap, "请填写播客名称");
            modelMap.addAttribute("record", record);
            return "admin/podcast/form";
        }

        if (StringUtils.hasLength(record.getAuthorName()) == false) {
            this.setErrorMsg(modelMap, "请填写作者名称");
            modelMap.addAttribute("record", record);
            return "admin/podcast/form";
        }

        // 新增逻辑
        if (null == record.getPodcastId()) {
            this.setSuccessMsg(modelMap, "添加播客成功");
            record.setEpisodeCount(0);
            record.setIsPublished(0);
            record.setIsDeleted(0);
            record.setCreatedAt(new Date());
            podPodcastService.addRecord(record);
        } 
        // 修改逻辑
        else {
            this.setSuccessMsg(modelMap, "修改播客成功");
            podPodcastService.updateRecord(record);
        }
        
        return "redirect:/admin/podcast/list";
    }

    @RequestMapping("/del-{id}")
    public String deletePodcast(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        ExecuteResult<String> result = podPodcastService.deleteRecordByPrimaryKey(id);
        if (result.isSuccess()) {
            redirectAttributes.addFlashAttribute("successMsg", "删除播客成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "删除播客失败：" + result.getErrorMessages());
        }
        return "redirect:/admin/podcast/list";
    }

    @RequestMapping(value = "/get-{id}")
    public String get(@PathVariable Long id, ModelMap modelMap) {
        ExecuteResult<PodPodcast> result = podPodcastService.queryRecordByPrimaryKey(id);
        modelMap.put("record", result.getResult());
        return "admin/podcast/form";
    }

    /**
     * 批量删除播客
     */
    @RequestMapping(value = "/batchDelete", method = RequestMethod.POST)
    public String batchDelete(@RequestParam("ids") Long[] ids, RedirectAttributes redirectAttributes) {
        log.debug("批量删除播客，ids: " + java.util.Arrays.toString(ids));
        int successCount = 0;
        int failCount = 0;

        for (Long id : ids) {
            ExecuteResult<String> result = podPodcastService.deleteRecordByPrimaryKey(id);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failCount++;
            }
        }

        if (failCount == 0) {
            redirectAttributes.addFlashAttribute("successMsg", "成功删除 " + successCount + " 个播客");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "删除完成：成功 " + successCount + " 个，失败 " + failCount + " 个");
        }

        return "redirect:/admin/podcast/list";
    }

    // ==================== 单集管理相关方法 ====================

    /**
     * 单集列表页面
     */
    @RequestMapping(value = "/episode/list")
    public String episodeList(HttpServletRequest req, PodEpisode record, ModelMap modelMap) {
        if (null == record) {
            record = new PodEpisode();
        }
        if (null == record.getPage()) {
            record.setPage(new Page());
        }

        // 获取分页参数
        String total = req.getParameter("total");
        String currentPage = req.getParameter("pageNo");
        String podcastId = req.getParameter("podcastId");

        if (StringUtils.hasLength(total)) {
            record.getPage().setTotalRecord(Integer.parseInt(total));
        }
        if (StringUtils.hasLength(currentPage)) {
            record.getPage().setCurrentPage(Integer.parseInt(currentPage));
        }
        if (StringUtils.hasLength(podcastId)) {
            record.setPodcastId(Long.parseLong(podcastId));
        }

        // 高级查询条件
        Map<String, Object> queryParams = new HashMap<>();
        String searchText = req.getParameter("searchText");
        String searchType = req.getParameter("searchType");
        String topicId = req.getParameter("topicId");

        if (StringUtils.hasLength(searchText) && StringUtils.hasLength(searchType)) {
            queryParams.put("searchText", searchText);
            queryParams.put("searchType", searchType);

            switch (searchType) {
                case "0": // 按标题搜索
                    record.setTitle(searchText);
                    break;
                case "1": // 按ID搜索
                    try {
                        record.setEpisodeId(Long.parseLong(searchText));
                    } catch (NumberFormatException e) {
                        // 如果ID格式不正确，设置一个不存在的ID，这样就不会查到任何结果
                        record.setEpisodeId(-1L);
                    }
                    break;
            }
        }

        // 专题筛选
        if (StringUtils.hasLength(topicId)) {
            record.setTopicId(Long.parseLong(topicId));
            queryParams.put("topicId", topicId);
        }

        // 播客筛选参数传递
        if (StringUtils.hasLength(podcastId)) {
            queryParams.put("podcastId", podcastId);
        }

        Page<PodEpisode> page = podEpisodeService.queryRecords(record.getPage(), record);
        record.setPage(page);
        record.excutePageLand(queryParams);

        modelMap.put("page", page);
        modelMap.put("record", record);

        // 获取所有播客列表用于筛选下拉框
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());

        // 获取专题列表用于筛选下拉框（如果选择了播客，则只显示该播客下的专题）
        if (record.getPodcastId() != null) {
            PodTopic topicQuery = new PodTopic();
            topicQuery.setPodcastId(record.getPodcastId());
            topicQuery.setIsDeleted(0);
            Page<PodTopic> topicPage = podTopicService.queryRecords(new Page<>(), topicQuery);
            modelMap.put("topicList", topicPage.getRecords());
        }

        return "admin/podcast/episodeList";
    }

    /**
     * 单集添加页面
     */
    @RequestMapping("/episode/addUI")
    public String addEpisodeUI(@RequestParam(required = false) Long podcastId, PodEpisode record, ModelMap modelMap) {
        log.debug("method addEpisodeUI");
        record.setIsDeleted(0);
        record.setIsPublished(0);
        if (podcastId != null) {
            record.setPodcastId(podcastId);
        }
        modelMap.put("record", record);

        // 获取所有播客列表用于下拉选择
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());

        // 获取专题列表用于下拉选择（如果指定了播客ID）
        if (podcastId != null) {
            PodTopic topicQuery = new PodTopic();
            topicQuery.setPodcastId(podcastId);
            topicQuery.setIsDeleted(0);
            Page<PodTopic> topicPage = podTopicService.queryRecords(new Page<>(), topicQuery);
            modelMap.put("topicList", topicPage.getRecords());
        }

        return "admin/podcast/episodeForm";
    }

    /**
     * 保存单集
     */
    @RequestMapping(value = "/episode/save", method = RequestMethod.POST)
    public String saveEpisode(HttpServletRequest request, PodEpisode record, ModelMap modelMap) {
        log.debug("method saveEpisode：" + record);

        // 参数验证
        if (StringUtils.hasLength(record.getTitle()) == false) {
            this.setErrorMsg(modelMap, "请填写单集标题");
            modelMap.addAttribute("record", record);
            return "admin/podcast/episodeForm";
        }

        if (record.getPodcastId() == null) {
            this.setErrorMsg(modelMap, "请选择所属播客");
            modelMap.addAttribute("record", record);
            return "admin/podcast/episodeForm";
        }

        // 新增逻辑
        if (null == record.getEpisodeId()) {
            this.setSuccessMsg(modelMap, "添加单集成功");
            record.setIsDeleted(0);
            record.setCreatedAt(new Date());
            record.setListenCount(0);
            record.setLikeCount(0);
            podEpisodeService.addRecord(record);
        }
        // 修改逻辑
        else {
            this.setSuccessMsg(modelMap, "修改单集成功");
            podEpisodeService.updateRecord(record);
        }

        return "redirect:/admin/podcast/episode/list?podcastId=" + record.getPodcastId();
    }

    /**
     * 单集编辑页面
     */
    @RequestMapping(value = "/episode/get-{id}")
    public String getEpisode(@PathVariable Long id, ModelMap modelMap) {
        ExecuteResult<PodEpisode> result = podEpisodeService.queryRecordByPrimaryKey(id);
        PodEpisode episode = result.getResult();
        modelMap.put("record", episode);

        // 获取所有播客列表用于下拉选择
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        modelMap.put("podcastList", podcastPage.getRecords());

        // 获取专题列表用于下拉选择（如果单集有所属播客）
        if (episode != null && episode.getPodcastId() != null) {
            PodTopic topicQuery = new PodTopic();
            topicQuery.setPodcastId(episode.getPodcastId());
            topicQuery.setIsDeleted(0);
            Page<PodTopic> topicPage = podTopicService.queryRecords(new Page<>(), topicQuery);
            modelMap.put("topicList", topicPage.getRecords());
        }

        return "admin/podcast/episodeForm";
    }

    /**
     * 删除单集
     */
    @RequestMapping("/episode/del-{id}")
    public String deleteEpisode(@PathVariable Long id, @RequestParam(required = false) Long podcastId, RedirectAttributes redirectAttributes) {
        ExecuteResult<String> result = podEpisodeService.deleteRecordByPrimaryKey(id);
        if (result.isSuccess()) {
            redirectAttributes.addFlashAttribute("successMsg", "删除单集成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "删除单集失败：" + result.getErrorMessages());
        }

        if (podcastId != null) {
            return "redirect:/admin/podcast/episode/list?podcastId=" + podcastId;
        }
        return "redirect:/admin/podcast/episode/list";
    }

    /**
     * 批量删除单集
     */
    @RequestMapping(value = "/episode/batchDelete", method = RequestMethod.POST)
    public String batchDeleteEpisodes(@RequestParam("ids") Long[] ids, @RequestParam(required = false) Long podcastId, RedirectAttributes redirectAttributes) {
        log.debug("批量删除单集，ids: " + java.util.Arrays.toString(ids));
        int successCount = 0;
        int failCount = 0;

        for (Long id : ids) {
            ExecuteResult<String> result = podEpisodeService.deleteRecordByPrimaryKey(id);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failCount++;
            }
        }

        if (failCount == 0) {
            redirectAttributes.addFlashAttribute("successMsg", "成功删除 " + successCount + " 个单集");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "删除完成：成功 " + successCount + " 个，失败 " + failCount + " 个");
        }

        if (podcastId != null) {
            return "redirect:/admin/podcast/episode/list?podcastId=" + podcastId;
        }
        return "redirect:/admin/podcast/episode/list";
    }

    // ==================== 播客统计相关方法 ====================

    /**
     * 播客统计页面
     */
    @RequestMapping(value = "/stats")
    public String stats(ModelMap modelMap) {
        // 获取播客总数
        PodPodcast podcastQuery = new PodPodcast();
        podcastQuery.setIsDeleted(0);
        Page<PodPodcast> podcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        long totalPodcasts = podcastPage.getTotalRecord();

        // 获取单集总数
        PodEpisode episodeQuery = new PodEpisode();
        episodeQuery.setIsDeleted(0);
        Page<PodEpisode> episodePage = podEpisodeService.queryRecords(new Page<>(), episodeQuery);
        long totalEpisodes = episodePage.getTotalRecord();

        // 获取已发布播客数
        podcastQuery.setIsPublished(1);
        Page<PodPodcast> publishedPodcastPage = podPodcastService.queryRecords(new Page<>(), podcastQuery);
        long publishedPodcasts = publishedPodcastPage.getTotalRecord();

        // 获取已发布单集数
        episodeQuery.setIsPublished(1);
        Page<PodEpisode> publishedEpisodePage = podEpisodeService.queryRecords(new Page<>(), episodeQuery);
        long publishedEpisodes = publishedEpisodePage.getTotalRecord();

        modelMap.put("totalPodcasts", totalPodcasts);
        modelMap.put("totalEpisodes", totalEpisodes);
        modelMap.put("publishedPodcasts", publishedPodcasts);
        modelMap.put("publishedEpisodes", publishedEpisodes);

        return "admin/podcast/stats";
    }

    /**
     * 播客设置页面
     */
    @RequestMapping(value = "/settings")
    public String settings(ModelMap modelMap) {
        return "admin/podcast/settings";
    }

}
