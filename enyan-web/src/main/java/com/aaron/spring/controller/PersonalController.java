package com.aaron.spring.controller;

import com.aaron.spring.model.EnyanPublisher;
import com.aaron.spring.service.EnyanOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2017/12/12
 * @Modified By:
 */
@Controller
@RequestMapping("/my")
public class PersonalController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(PersonalController.class);

    @Resource
    private EnyanOrderService enyanOrderService;

    @RequestMapping(value = "/index")
    public String index(HttpServletRequest req, EnyanPublisher publisher, ModelMap modelMap){
        return "/my/index";
    }
    @RequestMapping(value = "/orders")
    public String orders(HttpServletRequest req, EnyanPublisher publisher, ModelMap modelMap){
        return "/my/orders";
    }
    @RequestMapping(value = "/cart")
    public String cart(HttpServletRequest req, EnyanPublisher publisher, ModelMap modelMap){
        return "/my/cart";
    }
}
