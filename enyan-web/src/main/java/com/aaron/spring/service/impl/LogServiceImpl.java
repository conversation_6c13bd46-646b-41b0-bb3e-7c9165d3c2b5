package com.aaron.spring.service.impl;

import com.aaron.http.HttpMethod;
import com.aaron.http.HttpProtocolHandler;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.*;
import com.aaron.spring.service.LogService;
import com.aaron.spring.common.SlackMsgBuild;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-03-28
 * @Modified By:
 */
@Service
public class LogServiceImpl implements LogService {
    public static final String SERVER_LOG_URL = "*******************************************************************************";
    public static final String ORDER_LOG_URL = "*******************************************************************************";

    @Override
    public void sendTimeLog(String msg) {
        if (Constant.IS_PRODUCT == false){ // 非生产环境，禁用
            return ;
        }
        SlackMsg slackMsg = new SlackMsg();
        slackMsg.setText(msg + DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        HttpProtocolHandler.execute(null,SERVER_LOG_URL, HttpMethod.POST,null,slackMsg.jsonString());
    }

    @Override
    public void sendOrderLog(String msg) {
        if (Constant.IS_PRODUCT == false){ // 非生产环境，禁用
            return ;
        }
        SlackMsg slackMsg = new SlackMsg();
        slackMsg.setText(msg + DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        HttpProtocolHandler.execute(null,ORDER_LOG_URL, HttpMethod.POST,null,slackMsg.jsonString());
    }

    @Override
    public void sendNormalLog(SlackMsg slackMsg) {
        if (!Constant.IS_PRODUCT){ // 非生产环境，禁用
            return ;
        }
        String jsonString = slackMsg.jsonString();

        //String jsonString = "{\"user\":{\"email\":\"<EMAIL>\",\"encrypted\":[\"email\"]},\"encryption\":{\"user_key\":{\"text_hint\":\"123\",\"hex_value\":\"a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3\"}}}";
        HttpProtocolHandler.execute(null,SERVER_LOG_URL, HttpMethod.POST,null,jsonString);
        //logger.debug(httpResult.getStringResult());
        /*try {
            if (httpResult.isSuccess()){
                //return httpResult.getStringResult();
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }*/
    }

    @Override
    public void sendOrderLog(EnyanOrder order) {
        if (Constant.IS_LOCAL){ // 本地环境，禁用
            return ;
        }
        if (order.getOrderDetailInfo().getAmountHkd().intValue() == 0){//免费订单不发通知
            return;
        }
        SlackMsg slackMsg = new SlackMsg();
        SlackMsgBuild slackMsgBuild = new SlackMsgBuild();

        if (Constant.IS_TEST){
            slackMsgBuild.addText("——测试服务器——");
            slackMsgBuild.addBrText();
        }

        slackMsgBuild.addText("恩道电子书-订单号："+order.getOrderNum());
        slackMsgBuild.addBrText();

        slackMsgBuild.addText("您已收到新的订单");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("订单号：");
        slackMsgBuild.addRedText(order.getOrderNum());
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("Email：");
        slackMsgBuild.addRedText(order.getUserEmail());
        slackMsgBuild.addBrText();

        List<ProductInfo> productInfos = order.getOrderDetailInfo().getProductInfoList();
        for (ProductInfo productInfo:productInfos){
            if (productInfo.isDiscountSingleIsValid()){
                slackMsgBuild.addQuoteText(productInfo.getName());
                slackMsgBuild.addStrokeText("HK$ "+productInfo.getPriceHkd());
                slackMsgBuild.addText("HK$ "+productInfo.getPriceHKDDiscount());
                slackMsgBuild.addQuoteBrText();
            }else {
                slackMsgBuild.addQuoteText(productInfo.getName());
                slackMsgBuild.addText("HK$ "+productInfo.getPriceHkd());
                slackMsgBuild.addQuoteBrText();
            }
        }
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("支付：");
        slackMsgBuild.addRedText(order.getOrderPayInfo().getCharge().payTypeDescription());
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("国家：");
        slackMsgBuild.addRedText(order.getOrderPayInfo().getCharge().getCountry());
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("购买来源：");
        slackMsgBuild.addRedText(EBookConstant.OrderFrom.APP == order.getOrderFrom() ? "App":"Web");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("类型：");
        switch (order.getOrderType()){
            case EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY:
                slackMsgBuild.addRedText("购买电子书单本");
                break;
            case EBookConstant.OrderType.ORDER_EBOOK_SET_BUY:
                slackMsgBuild.addRedText("购买电子书套装");
                break;
            case EBookConstant.OrderType.ORDER_BOOK_BUY:
                slackMsgBuild.addRedText("购买书籍");
                break;
            case EBookConstant.OrderType.ORDER_REDEEM_BUY:
                slackMsgBuild.addRedText("购买兑换码");
                if (productInfos.size() == 1){
                    ProductInfo productInfo = productInfos.get(0);
                    slackMsgBuild.addRedText("("+productInfo.getQuantity().toString()+")");
                }
                break;
            case EBookConstant.OrderType.ORDER_REDEEM_EXCHANGE:
                slackMsgBuild.addRedText("兑换码兑换书籍");
                break;
        }
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("实付金额：");
        slackMsgBuild.addRedText("HK$"+order.getOrderDetailInfo().getAmountHkd());
        slackMsgBuild.addBrText();

        if (order.getOrderDetailInfo().getAmountCoupon().doubleValue() > 0){
            slackMsgBuild.addBoldText("优惠码：");
            slackMsgBuild.addRedText("-HK$"+order.getOrderDetailInfo().getAmountCoupon());
            slackMsgBuild.addBrText();
        }

        if (order.getOrderDetailInfo().getAmountDiscount().doubleValue() > 0){
            slackMsgBuild.addBoldText("折扣金额：");
            slackMsgBuild.addRedText("-HK$"+order.getOrderDetailInfo().getAmountDiscount());
            slackMsgBuild.addBrText();
        }

        slackMsgBuild.addBoldText("时间：");
        slackMsgBuild.addRedText(DateFormatUtils.format(order.getPurchasedAt(),"yyyy-MM-dd HH:mm:ss"));
        slackMsgBuild.addBrText();

        slackMsg.setText(slackMsgBuild.getText());

        HttpProtocolHandler.execute(null,ORDER_LOG_URL, HttpMethod.POST,null,slackMsg.jsonString());
    }

    @Override
    public void sendRentOrderLog(EnyanRent order) {
        if (Constant.IS_LOCAL){ // 本地环境，禁用
            return ;
        }
        if (order.getTotalFee().intValue() == 0){//免费订单不发通知
            return;
        }
        SlackMsg slackMsg = new SlackMsg();
        SlackMsgBuild slackMsgBuild = new SlackMsgBuild();

        if (Constant.IS_TEST){
            slackMsgBuild.addText("——测试服务器——");
            slackMsgBuild.addBrText();
        }

        slackMsgBuild.addText("订购书籍套餐-订单号："+order.getOrderNum());
        slackMsgBuild.addBrText();

        slackMsgBuild.addText("您已收到新的订单");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("订单号：");
        slackMsgBuild.addRedText(order.getOrderNum());
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("Email：");
        slackMsgBuild.addRedText(order.getUserEmail());
        slackMsgBuild.addBrText();

        slackMsgBuild.addQuoteText("套餐类型：");
        slackMsgBuild.addStrokeText(BookUtil.getBookNameInRentType(order.getRentType(), order.getRentLang()));
        slackMsgBuild.addQuoteBrText();

        slackMsgBuild.addQuoteText("自动付款：");
        slackMsgBuild.addStrokeText(order.getIsAuto() == 1?"是":"否");
        slackMsgBuild.addQuoteBrText();


        slackMsgBuild.addBoldText("时间：");
        slackMsgBuild.addRedText(DateFormatUtils.format(order.getCreateAt(),"yyyy-MM-dd HH:mm:ss"));
        slackMsgBuild.addBrText();

        slackMsg.setText(slackMsgBuild.getText());

        HttpProtocolHandler.execute(null,ORDER_LOG_URL, HttpMethod.POST,null,slackMsg.jsonString());
    }

    @Override
    public void sendRentOrderDetailLog(EnyanRentDetail order) {
        if (Constant.IS_LOCAL){ // 本地环境，禁用
            return ;
        }
        if (order.getIncomeTotal().intValue() == 0){//免费订单不发通知
            return;
        }
        SlackMsg slackMsg = new SlackMsg();
        SlackMsgBuild slackMsgBuild = new SlackMsgBuild();

        if (Constant.IS_TEST){
            slackMsgBuild.addText("——测试服务器——");
            slackMsgBuild.addBrText();
        }

        slackMsgBuild.addText("订购书籍套餐-订单号："+order.getOrderNum());
        slackMsgBuild.addBrText();

        slackMsgBuild.addText("您已收到付费信息");
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("订单号：");
        slackMsgBuild.addRedText(order.getOrderNum());
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("Email：");
        slackMsgBuild.addRedText(order.getUserEmail());
        slackMsgBuild.addBrText();

        slackMsgBuild.addQuoteText("套餐类型：");
        slackMsgBuild.addStrokeText(BookUtil.getBookNameInRentType(order.getRentType(), order.getRentLang()));
        slackMsgBuild.addQuoteBrText();

        slackMsgBuild.addQuoteText("自动付款：");
        slackMsgBuild.addStrokeText(order.getIsAuto() == 1?"是":"否");
        slackMsgBuild.addQuoteBrText();


        slackMsgBuild.addBoldText("支付：");
        slackMsgBuild.addRedText(PayDetail.payTypeDescription(order.getPayType()));
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("国家：");
        slackMsgBuild.addRedText(order.getPayCountry());
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("购买来源：");
        slackMsgBuild.addRedText(BookUtil.getOrderFromInfo(order.getOrderFrom()));
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("实付金额：");
        slackMsgBuild.addRedText("HK$"+order.getIncomeTotal());
        slackMsgBuild.addBrText();

        slackMsgBuild.addBoldText("时间：");
        slackMsgBuild.addRedText(DateFormatUtils.format(order.getPurchasedAt(),"yyyy-MM-dd HH:mm:ss"));
        slackMsgBuild.addBrText();

        slackMsg.setText(slackMsgBuild.getText());

        HttpProtocolHandler.execute(null,ORDER_LOG_URL, HttpMethod.POST,null,slackMsg.jsonString());
    }

    @Override
    public void sendExceptionLog(SlackMsg slackMsg) {
        if (!Constant.IS_PRODUCT){ // 非生产环境，禁用
            return ;
        }
        HttpProtocolHandler.execute(null,SERVER_LOG_URL, HttpMethod.POST,null,slackMsg.jsonString());
    }
}
