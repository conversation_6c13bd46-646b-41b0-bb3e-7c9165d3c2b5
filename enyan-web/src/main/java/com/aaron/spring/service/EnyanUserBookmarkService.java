package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanUserBookmarks;
import com.aaron.spring.model.EnyanUserBookmarksExample;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/3/13
 * @Modified By:
 */
public interface EnyanUserBookmarkService extends IService<EnyanUserBookmarks, EnyanUserBookmarksExample>{
    List<EnyanUserBookmarks> findEnyanUserBookmarksList(EnyanUserBookmarks userBookmarks);
    /**
     *
     * 获取大于updateTime的高亮数据,主要用于同步
     * @param userBookmarks
     * @Date: 2019-09-04
     */
    Page findUserBookmarksGTUpdateTime(Page<EnyanUserBookmarks> page, EnyanUserBookmarks userBookmarks);

    ExecuteResult<EnyanUserBookmarks> queryRecordByPrimaryKey(String pkId);

    ExecuteResult<Boolean> addBookmarks(List<EnyanUserBookmarks> list);
}
