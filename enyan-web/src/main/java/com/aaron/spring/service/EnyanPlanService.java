package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanPlan;
import com.aaron.spring.model.EnyanPlanExample;
import com.aaron.spring.model.EnyanSpirit;
import com.aaron.spring.model.OrderDetailInfo;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  11/13/20
 * @Modified By:
 */
public interface EnyanPlanService extends IService<EnyanPlan, EnyanPlanExample>{

    /**
     *
     * 根据主键修改个别属性
     * @param enyanSpirit
     * @Date: 2018/1/3
     */
    //void updateByPrimaryKeySelective(EnyanSpirit enyanSpirit, Long bookId);

    /**
     * <p>根据email及bookId获取Plan信息</p>
     * @param email
     * @param bookId
     * @return: com.aaron.spring.model.EnyanPlan
     * @since : 12/7/20
     */
    EnyanPlan getPlanByEmailAndBookId(String email, Long bookId);

    /**
     * <p>根据email及bookId获取全部的Plan信息</p>
     * @param email
     * @param bookId
     * @return: com.aaron.spring.model.EnyanPlan
     * @since : 1/25/21
     */
    EnyanPlan getFullPlanByEmailAndBookId(String email, Long bookId);

    /**
     * <p>根据email和bookId删除Plan（set isDeleted = 1）</p>
     * @param enyanPlan
     * @return: void
     * @since : 12/7/20
     */
    void removePlan(EnyanPlan enyanPlan);

    /**
     * <p>根据email和bookId调整Plan（set isDeleted = 1， startFrom以及打卡情况）</p>
     * @param enyanPlan
     * @return: void
     * @since : 1/25/21
     */
    void adjustPlan(EnyanPlan enyanPlan);

    /**
     * <p>购买书籍后，修改计划为已购买</p>
     * @param email
     * @param detailInfo
     * @return: int
     * @since : 12/8/20
     */
    int updatePlanHasBuy(String email, OrderDetailInfo detailInfo);

    /**
     * <p>获取大于updateTime的计划数据,主要用于同步</p>
     * @param page
     * @param enyanPlan
     * @return: com.aaron.mybatis.dao.pojo.Page
     * @since : 12/10/20
     */
    Page findSpiritPlansGTUpdateTime(Page<EnyanPlan> page, EnyanPlan enyanPlan);

    /**
     * <p>app同步的plan信息存储</p>
     * @param list
     * @return: int
     * @since : 12/11/20
     */
    int updateSyncPlan(String email, List<EnyanPlan> list);

    /**
     * <p>注销用户</p>
     * @param email
     * @param revokedEmail
     * @return com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2022/8/26
     **/
    ExecuteResult<String> revokeUser(String email, String revokedEmail);
}
