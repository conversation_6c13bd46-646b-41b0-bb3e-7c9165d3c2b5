package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodPodcast;
import com.aaron.spring.model.PodPodcastExample;
import com.aaron.util.ExecuteResult;

/**
 * @Author: <PERSON>
 * @Description: 栏目服务接口
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
public interface PodPodcastService extends IService<PodPodcast, PodPodcastExample> {
    /**
     * <p>查询栏目基本信息列表</p>
     * @param page
     * @param record
     * @return com.aaron.mybatis.dao.pojo.Page
     * @since : 2025/5/13
     **/
    Page queryRecordsBasic(Page<PodPodcast> page, PodPodcast record);



    /**
     * <p>增加栏目播放量</p>
     * @param id
     * @return int
     * @since : 2025/5/13
     **/
    int updatePodcastPlayCountById(Long id);
    
    /**
     * <p>更新播客下所有单集的封面图片URL</p>
     * @param podcastId 播客ID
     * @param coverImageUrl 封面图片URL
     * @param coverImageUrl2 详情页长条图片URL
     * @return 更新结果
     */
    ExecuteResult<Boolean> updateAllEpisodesCoverImage(Long podcastId, String coverImageUrl, String coverImageUrl2);
    
    /**
     * <p>查询已发布的栏目列表</p>
     * @param page
     * @param record
     * @return com.aaron.mybatis.dao.pojo.Page
     * @since : 2025/5/13
     **/
    Page queryPublishedPodcasts(Page<PodPodcast> page, PodPodcast record);

    /**
     * <p>增加播客喜欢量</p>
     * @param id 播客ID
     * @return int 更新影响的行数
     * @since : 2025/5/21
     **/
    int updatePodcastLikeCountById(Long id);
}
