package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.v4.model.RestDailyWords;
import com.aaron.spring.common.Constant;
import com.aaron.spring.controller.DailyWordsController;
import com.aaron.spring.mapper.EnyanDailyWordsMapper;
import com.aaron.spring.mapper.custom.EnyanDailyWordsCustomMapper;
import com.aaron.spring.model.EnyanDailyWords;
import com.aaron.spring.model.EnyanDailyWordsExample;
import com.aaron.spring.service.EnyanDailyWordsService;
import com.aaron.util.ExecuteResult;
import com.ctc.wstx.shaded.msv_core.grammar.DataExp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Aaron Hao
 * @Description:
 * @Date: Created in  2022/7/18
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanDailyWordsServiceImpl implements EnyanDailyWordsService {
	@Resource
	private EnyanDailyWordsMapper enyanDailyWordsMapper;

	@Resource
	private EnyanDailyWordsCustomMapper enyanDailyWordsCustomMapper;

	/**
	 * 金句base url
	 **/
	private static final String BASE_IMG_PATH = "https://dl.edhub.cc/root/blogs/daily/";

	/**
	 * 下一批次总数
	 **/
	private static final Integer BATCH_COUNT = 60;
	@Override
	public Page queryRecords(Page<EnyanDailyWords> page, EnyanDailyWords record) {
		if (null == record){
			record = new EnyanDailyWords();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanDailyWordsExample example = new EnyanDailyWordsExample();
			EnyanDailyWordsExample.Criteria criteria = example.createCriteria();

			example.setPage(page);
			if (StringUtils.isNotBlank(record.getDataContent())){
				criteria.andDataContentLike("%"+record.getDataContent()+"%");
			}
			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			*/
			if (null != record.getDataAt()){
				criteria.andDataAtLessThanOrEqualTo(record.getDataAt());
			}
			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanDailyWordsMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanDailyWords> list;
			if (count > 0){
				list = enyanDailyWordsMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<EnyanDailyWords> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanDailyWords> result = new ExecuteResult<>();
		try {
			EnyanDailyWords record = enyanDailyWordsMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanDailyWords();
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanDailyWords> addRecord(EnyanDailyWords record) {
		ExecuteResult<EnyanDailyWords> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = enyanDailyWordsMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanDailyWords> updateRecord(EnyanDailyWords record) {
		ExecuteResult<EnyanDailyWords> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanDailyWordsMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = EnyanDailyWordsCustomMapper.updateRecordToDeletedById(pkId);
			int deleteFlag = 0;
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanDailyWords record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanDailyWords record) {
		return null;
	}

	@Override
	public List<EnyanDailyWords> findRecordsByDateAt(Integer dataAt) {
		return enyanDailyWordsCustomMapper.findRecordByDateAt(dataAt);
	}

	@Override
	public int updateRecordToLikeById(long dataId) {
		List<RestDailyWords> selectedList = Constant.DEFAULT_REST_CONFIG.getDailyWords().stream().filter(obj->obj.getDataId() == dataId).collect(Collectors.toList());
		if (null != selectedList && selectedList.isEmpty() == false){
			RestDailyWords dailyWords = selectedList.get(0);
			dailyWords.setLikeCount(dailyWords.getLikeCount()+1);
		}
		return enyanDailyWordsCustomMapper.updateRecordToLikeById(dataId);
	}

	@Override
	public void initDailywords() {
		String todayString = DateFormatUtils.format(new Date(), "yyyyMMdd");
		Integer today = Integer.parseInt(todayString);
		List<EnyanDailyWords> list = enyanDailyWordsCustomMapper.findRecordLessAndEqualDateTop10(today);
		List<RestDailyWords> dailyWords = new ArrayList<>();
		for (int i = list.size() - 1; i >= 0; i--) {
			EnyanDailyWords obj = list.get(i);
			RestDailyWords tmp = new RestDailyWords();
			tmp.initFrom(obj);
			dailyWords.add(tmp);
		}
		long count = enyanDailyWordsCustomMapper.countOfLessAndEqualDate(today);
		Constant.DEFAULT_REST_CONFIG.setDailyWords(dailyWords);
		Constant.DEFAULT_REST_CONFIG.setCountOfDailyWords(count);
	}

	@Override
	public void createDailyWordsNextBatch() {
		long maxDate = enyanDailyWordsCustomMapper.maxDate();
		String dateFormat = "yyyyMMdd";
		String currentDateString = "";
		Date now = new Date();
		try{
			Date current = DateUtils.parseDate(maxDate+"",dateFormat);
			for (int i = 0; i < BATCH_COUNT; i++) {
				current = DateUtils.addDays(current, 1);
				currentDateString = DateFormatUtils.format(current, dateFormat);

				EnyanDailyWords dailyWords = new EnyanDailyWords();
				dailyWords.setCreateAt(now);
				dailyWords.setBookTitle("书名");
				int dataAt = Integer.parseInt(currentDateString);
				dailyWords.setDataAt(dataAt);
				dailyWords.setDataContent("金句");
				dailyWords.setDataImgUrl(BASE_IMG_PATH+(currentDateString.substring(0,4))+"/"+(currentDateString.substring(4,6))+"/"+currentDateString+".png");
				dailyWords.setLikeCount(0);
				dailyWords.setBookAuthor("作者");
				dailyWords.setBookId(-1L);

				enyanDailyWordsMapper.insert(dailyWords);
			}
		}catch (Exception e){

		}
	}

	@Override
	public int updateRecordByDate(EnyanDailyWords dailyWords) {
		return enyanDailyWordsCustomMapper.updateRecordByDate(dailyWords);
	}
}
