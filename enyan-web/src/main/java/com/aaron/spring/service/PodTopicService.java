package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.PodTopic;
import com.aaron.spring.model.PodTopicExample;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description: 播客主题服务接口
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
public interface PodTopicService extends IService<PodTopic, PodTopicExample> {
    
    /**
     * <p>获取播客下的主题列表</p>
     * @param page 分页参数
     * @param record 查询条件
     * @return com.aaron.mybatis.dao.pojo.Page
     * @since : 2025/5/13
     **/
    Page queryPodcastTopicsByRestObj(Page<PodTopic> page, PodTopic record);
    
    /**
     * <p>获取播客下的主题列表</p>
     * @param page 分页参数
     * @param podcastId 播客ID
     * @return com.aaron.mybatis.dao.pojo.Page
     * @deprecated 使用 {@link #queryPodcastTopicsByRestObj(Page, PodTopic)} 替代
     **/
    @Deprecated
    Page queryPodcastTopics(Page<PodTopic> page, Long podcastId);
    
    /**
     * <p>根据ID获取主题详情</p>
     * @param topicId 主题ID
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.model.PodTopic>
     * @since : 2025/5/13
     **/
    ExecuteResult<PodTopic> getTopicDetail(Long topicId);
    
    /**
     * <p>添加播客主题</p>
     * @param topic 主题信息
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.model.PodTopic>
     * @since : 2025/5/13
     **/
    ExecuteResult<PodTopic> addTopic(PodTopic topic);
    
    /**
     * <p>更新播客主题</p>
     * @param topic 主题信息
     * @return com.aaron.util.ExecuteResult<com.aaron.spring.model.PodTopic>
     * @since : 2025/5/13
     **/
    ExecuteResult<PodTopic> updateTopic(PodTopic topic);
    
    /**
     * <p>删除播客主题</p>
     * @param topicId 主题ID
     * @return com.aaron.util.ExecuteResult<String>
     * @since : 2025/5/13
     **/
    ExecuteResult<String> deleteTopic(Long topicId);
    
    /**
     * <p>调整主题顺序</p>
     * @param topicId 主题ID
     * @param displayOrder 显示顺序
     * @return com.aaron.util.ExecuteResult<String>
     * @since : 2025/5/13
     **/
    ExecuteResult<String> updateTopicOrder(Long topicId, Integer displayOrder);
    
    /**
     * <p>批量查询播客是否有专题</p>
     * @param podcastIds 播客ID列表
     * @return 有专题的播客ID列表
     * @since : 2025/6/3
     **/
    List<Long> queryPodcastsWithTopics(List<Long> podcastIds);
}
