package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.Money;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.mapper.EnyanOrderDetailMapper;
import com.aaron.spring.mapper.EnyanRedeemCodeMapper;
import com.aaron.spring.mapper.custom.EnyanOrderCustomMapper;
import com.aaron.spring.mapper.custom.EnyanRedeemCodeCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanRedeemCodeService;
import com.aaron.spring.common.OrderUtil;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 *
 * @Author: Aaron Hao
 * @Date: Created in  2019-11-11
 * @Modified By:
 */
@Service
public class EnyanRedeemCodeServiceImpl implements EnyanRedeemCodeService {

    @Resource
    private EnyanRedeemCodeMapper enyanRedeemCodeMapper;

    @Resource
    private EnyanBookService enyanBookService;

    @Resource
    private EnyanOrderCustomMapper enyanOrderCustomMapper;

    @Resource
    private EnyanRedeemCodeCustomMapper enyanRedeemCodeCustomMapper;

    @Resource
    private EnyanOrderDetailMapper enyanOrderDetailMapper;

    @Override
    public Page queryRecords(Page<EnyanRedeemCode> page, EnyanRedeemCode record) {
        if (null == record){
            record = new EnyanRedeemCode();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            Date current = new Date();
            EnyanRedeemCodeExample example = new EnyanRedeemCodeExample();
            EnyanRedeemCodeExample.Criteria criteria = example.createCriteria();
            example.setPage(page);

            if (StringUtils.isNotBlank(record.getCreateTime())){
                //criteria.andCategoryNameEqualTo(record.getCategoryName());
                criteria.andCreateTimeEqualTo(record.getCreateTime());
            }
            if (StringUtils.isNotBlank(record.getCode())){
                criteria.andCodeEqualTo(record.getCode());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (null != record.getStatus()){
                criteria.andStatusEqualTo(record.getStatus());
            }
            if (record.isExpired()){//已经过期的兑换码
                criteria.andStatusEqualTo(0);//未兑换
                criteria.andEndAtLessThan(current);
            }
            //example.setOrderByClause("category_order desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanRedeemCodeMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanRedeemCode> list;
            if (count > 0){
                list = enyanRedeemCodeCustomMapper.selectByExampleWithBLOBs(example);
            }else {
                list = new ArrayList<>();
            }
            for (EnyanRedeemCode redeemCode:list){
                if (StringUtils.isNotBlank(redeemCode.getNote())){
                    RedeemCodeNoteInfo redeemCodeNoteInfo = JSONObject.parseObject(redeemCode.getNote(),RedeemCodeNoteInfo.class);
                    redeemCode.setRedeemCodeNoteInfo(redeemCodeNoteInfo);
                }
                if (redeemCode.getStatus() == 0 && null != redeemCode.getEndAt()){//未兑换，并且已经过期
                    if (redeemCode.getEndAt().compareTo(current) < 0){//过期
                        redeemCode.setExpired(true);
                    }
                }
            }
            page.setRecords(list);
            page.setTotalRecord(count);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanRedeemCode> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanRedeemCode> result = new ExecuteResult<>();
        try {
            EnyanRedeemCode record= enyanRedeemCodeMapper.selectByPrimaryKey(pkId);
            if (null == record){
                record = new EnyanRedeemCode();
            }
            if (StringUtils.isNotBlank(record.getNote())){
                RedeemCodeNoteInfo redeemCodeNoteInfo = JSONObject.parseObject(record.getNote(),RedeemCodeNoteInfo.class);
                record.setRedeemCodeNoteInfo(redeemCodeNoteInfo);
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanRedeemCode> addRecord(EnyanRedeemCode record) {
        ExecuteResult<EnyanRedeemCode> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkSaveRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanRedeemCodeMapper.insert(record);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
                result.setResult(record);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanRedeemCode> updateRecord(EnyanRedeemCode record) {
        ExecuteResult<EnyanRedeemCode> result = new ExecuteResult<>();
        try {
            //校验保存对象　
            String checkMsg = this.checkUpdateRecord(record);
            if (StringUtils.isNotBlank(checkMsg)){
                result.addErrorMessage("保存校验失败："+ checkMsg);
                return result;
            }
            int saveFlag = enyanRedeemCodeMapper.updateByPrimaryKeySelective(record);
            if (saveFlag>0){
                result.setResult(record);
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int deleteFlag = enyanRedeemCodeMapper.deleteByPrimaryKey(pkId);
            if (deleteFlag>0){
                result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
            }

        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public String checkSaveRecord(EnyanRedeemCode record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanRedeemCode record) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanRedeemCode> addRecord(EnyanRedeemCode record, List<EnyanBook> bookListToRedeem) {
        return null;
    }

    @Override
    public List<EnyanRedeemCode> findRecordsByRedeemCode(EnyanRedeemCode record) {
        if (null == record){
            record = new EnyanRedeemCode();
        }

        try {
            Date current = new Date();
            EnyanRedeemCodeExample example = new EnyanRedeemCodeExample();
            EnyanRedeemCodeExample.Criteria criteria = example.createCriteria();


            if (StringUtils.isNotBlank(record.getCreateTime())){
                //criteria.andCategoryNameEqualTo(record.getCategoryName());
                criteria.andCreateTimeEqualTo(record.getCreateTime());
            }
            if (StringUtils.isNotBlank(record.getCode())){
                criteria.andCodeEqualTo(record.getCode());
            }
            if (StringUtils.isNotBlank(record.getUserEmail())){
                criteria.andUserEmailEqualTo(record.getUserEmail());
            }
            if (StringUtils.isNotBlank(record.getStartDate()) && StringUtils.isNotBlank(record.getEndDate())){
                criteria.andCreateTimeBetween(record.getStartDate(),record.getEndDate());
            }
            if (null != record.getStatus()){
                criteria.andStatusEqualTo(record.getStatus());
            }
            if (record.isExpired()){//已经过期的兑换码
                criteria.andStatusEqualTo(0);//未兑换
                criteria.andEndAtLessThan(current);
            }
            //example.setOrderByClause("category_order desc");
            if (null != record.getOrderObjList()){
                for (OrderObj orderObj:record.getOrderObjList()){
                    example.setOrderByClause(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
                }
            }

            List<EnyanRedeemCode> list = enyanRedeemCodeMapper.selectByExampleWithBLOBs(example);
            for (EnyanRedeemCode redeemCode:list){
                if (StringUtils.isNotBlank(redeemCode.getNote())){
                    RedeemCodeNoteInfo redeemCodeNoteInfo = JSONObject.parseObject(redeemCode.getNote(),RedeemCodeNoteInfo.class);
                    redeemCode.setRedeemCodeNoteInfo(redeemCodeNoteInfo);
                }
                if (redeemCode.getStatus() == 0 && null != redeemCode.getEndAt()){//未兑换，并且已经过期
                    if (redeemCode.getEndAt().compareTo(current) < 0){//过期
                        redeemCode.setExpired(true);
                    }
                }
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<EnyanRedeemCode> getRecordByCode(String code) {
        return null;
    }

    @Override
    public void splitOrder(EnyanOrder enyanOrder) {
        this.splitOrder(enyanOrder, false);
    }

    @Override
    public ExecuteResult<String> revokeUser(String email, String revokedEmail) {
        ExecuteResult<String> result = new ExecuteResult<>();
        try {
            int saveFlag = enyanRedeemCodeCustomMapper.deleteAllByEmail(email);
            if (saveFlag>0){
                result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
            }else {
                result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE);
        }
        return result;
    }

    /**
     *
     *  兑换码的购买只有一个产品列表
     * @param enyanOrder
     * @param updateOrderCount
     * @Date: 2020-06-24
     */
    public void splitOrder(EnyanOrder enyanOrder, boolean updateOrderCount) {
        if (enyanOrder.getOrderType() != EBookConstant.OrderType.ORDER_REDEEM_BUY){//只分离兑换码购买的订单
            return;
        }
        if (StringUtils.isNotBlank(enyanOrder.getOrderDetail())){
            OrderDetailInfo orderDetailInfo = JSON.parseObject(enyanOrder.getOrderDetail(),OrderDetailInfo.class);
            orderDetailInfo.resetFromJson();
            enyanOrder.setOrderDetailInfo(orderDetailInfo);
        }

        if (StringUtils.isNotBlank(enyanOrder.getPayInfo())){
            OrderPayInfo orderPayInfo = JSON.parseObject(enyanOrder.getPayInfo(),OrderPayInfo.class);
            enyanOrder.setOrderPayInfo(orderPayInfo);
        }
        if (enyanOrder.getOrderDetailInfo().getProductInfoList().size() != 1){
            return;
        }
        ProductInfo productInfo = enyanOrder.getOrderDetailInfo().getProductInfoList().get(0);

        String dateStr = DateFormatUtils.format(enyanOrder.getPurchasedAt(),"yyyyMMdd");
        int dateInt = Integer.parseInt(dateStr);

        //String rateValue = this.getRateValue(dateInt,enyanOrder.getPurchasedAt());
        String rateValue = "";
        BigDecimal payFee = OrderUtil.getPayFee(enyanOrder);
        BigDecimal avgPayFee = this.getAvgPayFee(enyanOrder,payFee);
        String currentDate = DateFormatUtils.format(new Date(),"yyyyMMddHHmmss");

        EnyanBook enyanBook = enyanBookService.queryRecordByPrimaryKey(productInfo.getCode()).getResult();
        if (null == enyanBook){
            return;
        }
        EnyanBook newBook = new EnyanBook();
        newBook.setBookId(enyanBook.getBookId());
        newBook.setBookTitle(enyanBook.getBookTitle());

        List<EnyanBook> newBookList = new ArrayList<>();//兑换码的List
        newBookList.add(newBook);

        EnyanOrderDetail buyOrderDetail = null;

        for (int i = 0; i < productInfo.getQuantity().intValue(); i++) {

            //}
            //for (ProductInfo productInfo : enyanOrder.getOrderDetailInfo().getProductInfoList()){
            EnyanOrderDetail orderDetail = new EnyanOrderDetail();
            orderDetail.setBookId(productInfo.getCode());
            orderDetail.setBookTitle(productInfo.getName());
            orderDetail.setOrderCurrency(enyanOrder.getOrderCurrency());
            orderDetail.setOrderNum(enyanOrder.getOrderNum());

            orderDetail.setSalesModel(enyanBook.getSalesModel());
            orderDetail.setOrderType(enyanOrder.getOrderType());
            orderDetail.setPayCountry(enyanOrder.getOrderPayInfo().getCharge().getCountry());//支付的国家
            orderDetail.setPayType(enyanOrder.getOrderPayInfo().getCharge().getPayType());

            orderDetail.setPayFee(avgPayFee); //支付时的费用
            orderDetail.setVendorPercent(enyanBook.getVendorPercent()); //版税费率

            EnyanOrderDetail orderDetailNew = OrderUtil.getCalcOrder(productInfo,orderDetail);
            orderDetail.setPriceFixed(orderDetailNew.getPriceFixed());
            orderDetail.setPriceSelling(orderDetailNew.getPriceSelling());
            orderDetail.setIncomeTotal(orderDetailNew.getIncomeTotal());
            orderDetail.setIncomeReal(orderDetailNew.getIncomeReal());
            orderDetail.setIncomeVendor(orderDetailNew.getIncomeVendor());
            orderDetail.setIncomePlat(orderDetailNew.getIncomePlat());
            orderDetail.setNetSales(orderDetailNew.getNetSales());

            orderDetail.setPurchasedAt(enyanOrder.getPurchasedAt());

            orderDetail.setQuantity(1);
            orderDetail.setPurchasedDay(dateInt);
            orderDetail.setUserEmail(enyanOrder.getUserEmail());
            orderDetail.setUserId(enyanOrder.getUserId());
            orderDetail.setIsCounted(EBookConstant.BalanceStatus.NOT);
            orderDetail.setPublisherId(productInfo.getPublisherId());
            orderDetail.setBookPubCode(productInfo.getBookPubCode());
            orderDetail.setBookEsin(productInfo.getBookEsin());
            orderDetail.setRateValue(rateValue);

            if (null == buyOrderDetail){
                buyOrderDetail = new EnyanOrderDetail();
                this.buyOrderDetail(orderDetail,buyOrderDetail,productInfo.getQuantity().intValue());
            }



//            EnyanRedeemCode redeemCode = new EnyanRedeemCode();
//            redeemCode.setCode("B-"+ UUID.randomUUID().toString());
//            redeemCode.setStatus(EBookContant.RedeemStatus.DEFAULT);
//            redeemCode.setType(EBookContant.RedeemType.SPECIAL);

            RedeemCodeNoteInfo redeemCodeNoteInfo = new RedeemCodeNoteInfo();
            redeemCodeNoteInfo.setSource(EBookConstant.RedeemSource.BUY_DESCRIPTION);
            redeemCodeNoteInfo.setType(EBookConstant.RedeemType.SPECIAL);
            redeemCodeNoteInfo.setOtherThings("用户购买兑换码");
            redeemCodeNoteInfo.setBooksToRedeemList(newBookList);
            redeemCodeNoteInfo.setEnyanOrderDetail(orderDetail);

            EnyanRedeemCode enyanRedeemCode = new EnyanRedeemCode();
            enyanRedeemCode.setCode("B-"+ UUID.randomUUID().toString());
            enyanRedeemCode.setCreateTime(currentDate);
            enyanRedeemCode.setStatus(EBookConstant.RedeemStatus.DEFAULT);
            enyanRedeemCode.setType(EBookConstant.RedeemType.SPECIAL);
            enyanRedeemCode.setNote(JSON.toJSONString(redeemCodeNoteInfo));
            enyanRedeemCode.setUserEmail(enyanOrder.getUserEmail());
            try {
                enyanRedeemCodeMapper.insert(enyanRedeemCode);
            }catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

            }
        }
        enyanOrderDetailMapper.insert(buyOrderDetail);
        if (updateOrderCount){
            enyanOrderCustomMapper.updateOrderCounted(enyanOrder.getOrderId());
        }
    }

    /**
     *
     * 按照购买数量平均的支付费用,每次购买，productList 只有一个产品
     * @param enyanOrder
     * @Date: 2020-04-09
     */
    public static BigDecimal getAvgPayFee(EnyanOrder enyanOrder, BigDecimal payFee){
        if (null == enyanOrder.getOrderDetailInfo().getProductInfoList()
                ||enyanOrder.getOrderDetailInfo().getProductInfoList().size() != 1){
            return new BigDecimal("0");
        }
        ProductInfo productInfo = enyanOrder.getOrderDetailInfo().getProductInfoList().get(0);
        return payFee.divide(productInfo.getQuantity(), Money.HKD.getDefaultFractionDigits(),Money.DEFAULT_ROUNDING);
    }
    /**
     * <p>将购买兑换码的信息添加到销售明细，count值为实际购买的数值</p>
     *  * @param orderDetail
      * @param buyOrderDetail
     * @return: void
     * @since : 2021/3/22
     */
    private void buyOrderDetail(EnyanOrderDetail orderDetail, EnyanOrderDetail buyOrderDetail, int count){

        buyOrderDetail.setBookId(orderDetail.getBookId());
        buyOrderDetail.setBookTitle(orderDetail.getBookTitle());
        buyOrderDetail.setOrderCurrency(orderDetail.getOrderCurrency());
        buyOrderDetail.setOrderNum(orderDetail.getOrderNum());

        buyOrderDetail.setSalesModel(orderDetail.getSalesModel());
        buyOrderDetail.setOrderType(orderDetail.getOrderType());
        buyOrderDetail.setPayCountry(orderDetail.getPayCountry());//支付的国家
        buyOrderDetail.setPayType(orderDetail.getPayType());

        buyOrderDetail.setPayFee(orderDetail.getPayFee().multiply(new BigDecimal(String.valueOf(count)))); //支付时的费用
        buyOrderDetail.setVendorPercent(orderDetail.getVendorPercent()); //版税费率

        buyOrderDetail.setPriceFixed(orderDetail.getPriceFixed());
        buyOrderDetail.setPriceSelling(orderDetail.getPriceSelling());
        buyOrderDetail.setIncomeTotal(orderDetail.getIncomeTotal().multiply(new BigDecimal(String.valueOf(count))));
        buyOrderDetail.setIncomeReal(orderDetail.getIncomeReal().multiply(new BigDecimal(String.valueOf(count))));
        buyOrderDetail.setIncomeVendor(orderDetail.getIncomeVendor().multiply(new BigDecimal(String.valueOf(count))));
        buyOrderDetail.setIncomePlat(orderDetail.getIncomePlat().multiply(new BigDecimal(String.valueOf(count))));
        buyOrderDetail.setNetSales(orderDetail.getNetSales().multiply(new BigDecimal(String.valueOf(count))));

        buyOrderDetail.setPurchasedAt(orderDetail.getPurchasedAt());

        buyOrderDetail.setQuantity(count);
        buyOrderDetail.setPurchasedDay(orderDetail.getPurchasedDay());
        buyOrderDetail.setUserEmail(orderDetail.getUserEmail());
        buyOrderDetail.setUserId(orderDetail.getUserId());
        buyOrderDetail.setIsCounted(EBookConstant.BalanceStatus.NOT);
        buyOrderDetail.setPublisherId(orderDetail.getPublisherId());
        buyOrderDetail.setBookPubCode(orderDetail.getBookPubCode());
        buyOrderDetail.setBookEsin(orderDetail.getBookEsin());
        buyOrderDetail.setRateValue(orderDetail.getRateValue());
    }
}
