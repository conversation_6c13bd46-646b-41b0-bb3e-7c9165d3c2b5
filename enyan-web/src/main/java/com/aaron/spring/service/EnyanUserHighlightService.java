package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.*;
import com.aaron.util.ExecuteResult;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/3/13
 * @Modified By:
 */
public interface EnyanUserHighlightService extends IService<EnyanUserHighlights, EnyanUserHighlightsExample>{
    List<EnyanUserHighlights> findEnyanUserHighlightsList(EnyanUserHighlights userHighlights);
    /**
     *
     * 获取大于updateTime的高亮数据,主要用于同步
     * @param userHighlights
     * @Date: 2019-09-04
     */
    Page findUserHighlightsGTUpdateTime(Page<EnyanUserHighlights> page, EnyanUserHighlights userHighlights);

    ExecuteResult<EnyanUserHighlights> queryRecordByPrimaryKey(String pkId);

    ExecuteResult<Boolean> addHighlights(List<EnyanUserHighlights> list);
}
