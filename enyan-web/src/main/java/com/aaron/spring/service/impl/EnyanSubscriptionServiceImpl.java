package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.mapper.EnyanSubscriptionMapper;
import com.aaron.spring.mapper.custom.EnyanSubscriptionCustomMapper;
import com.aaron.spring.model.EnyanSubscription;
import com.aaron.spring.model.EnyanSubscriptionExample;
import com.aaron.spring.model.EnyanSubscription;
import com.aaron.spring.service.EnyanSubscriptionService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/1/25
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanSubscriptionServiceImpl implements EnyanSubscriptionService {

	@Resource
	private EnyanSubscriptionMapper enyanSubscriptionMapper;

	@Resource
	private EnyanSubscriptionCustomMapper enyanSubscriptionCustomMapper;

	@Override
	public Page queryRecords(Page<EnyanSubscription> page, EnyanSubscription record) {
		if (null == record){
			record = new EnyanSubscription();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanSubscriptionExample example = new EnyanSubscriptionExample();
			EnyanSubscriptionExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}*/

			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanSubscriptionMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanSubscription> list;
			if (count > 0){
				list = enyanSubscriptionMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<EnyanSubscription> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanSubscription> result = new ExecuteResult<>();
		try {
			EnyanSubscription record = enyanSubscriptionMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanSubscription();
			}
			result.setResult(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanSubscription> addRecord(EnyanSubscription record) {
		ExecuteResult<EnyanSubscription> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = 0;
			try {
				saveFlag = enyanSubscriptionMapper.insert(record);
			}catch (DuplicateKeyException e){//因已添加索引，排除统计重复（数据库去重）

			}
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanSubscription> updateRecord(EnyanSubscription record) {
		ExecuteResult<EnyanSubscription> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanSubscriptionMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = EnyanSubscriptionMapper.deleteByPrimaryKey(pkId);
			int deleteFlag = enyanSubscriptionMapper.deleteByPrimaryKey(pkId);
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanSubscription record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanSubscription record) {
		return null;
	}

	@Override
	public List<EnyanSubscription> findAllRecores() {
		return enyanSubscriptionCustomMapper.findAllRecores();
	}
}
