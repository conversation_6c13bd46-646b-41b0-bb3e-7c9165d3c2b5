package com.aaron.spring.service;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.spring.model.EmailHistory;
import com.aaron.spring.model.EmailHistoryExample;
import com.aaron.util.ExecuteResult;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/3/2
 * @Modified By:
 */
public interface EmailHistoryService extends IService<EmailHistory,EmailHistoryExample>{
    /**
     *
     * 更新记录时间
     * @param id
     * @Date: 2018/3/2
     */
    ExecuteResult<String> refreshEmailHistory(long id);

    /**
     *
     *
     * @param email
     * @Date: 2018/3/5
     */
    ExecuteResult<EmailHistory> getEmailHistoryByName(String email, Integer emailType, Integer yearMonthDay);
}
