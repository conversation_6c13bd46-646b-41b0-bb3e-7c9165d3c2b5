/*
 * The MIT License (MIT)
 *
 * Copyright (c) 2014 <EMAIL>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

package com.aaron.spring.service;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.dao.Mapper;
import com.aaron.util.ExecuteResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 通用接口
 */
public interface IService<T,TExample> {
    /**
     *
     * 分页查询
     * @param page
     * @param record
     * @Date: 2017/11/1
     */
    Page queryRecords(Page<T> page, T record);
    /**
     *
     * 根据主键查询
     * @param pkId
     * @Date: 2017/11/1
     */
    ExecuteResult<T> queryRecordByPrimaryKey(Long pkId);
    /**
     *
     * 添加记录
     * @param record
     * @Date: 2017/11/1
     */
    ExecuteResult<T> addRecord(T record);
    /**
     *
     * 修改记录
     * @param record
     * @Date: 2017/11/1
     */
    ExecuteResult<T> updateRecord(T record);
    /**
     *
     * 删除记录
     * @param pkId
     * @Date: 2017/11/1
     */
    ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId);
    /**
     *
     * 保存时判断是否有异常对象属性信息
     * @param record
     * @param isUpdate
     * @Date: 2017/11/1
     */
    String checkSaveRecord(T record);

    /**
     *
     * 修改时判断是否有异常对象属性信息
     * @param record
     * @param isUpdate
     * @Date: 2017/11/1
     */
    String checkUpdateRecord(T record);
}
