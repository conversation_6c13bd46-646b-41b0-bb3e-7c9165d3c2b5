package com.aaron.spring.service;

import com.aaron.drm.model.DrmInfo;
import com.aaron.drm.model.Licenses;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.model.EnyanOrder;
import com.aaron.spring.model.EnyanOrderDetail;
import com.aaron.spring.model.EnyanOrderDetailExample;
import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.util.ExecuteResult;

import java.util.Date;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/4/23
 * @Modified By:
 */
public interface EnyanOrderDetailService extends IService<EnyanOrderDetail, EnyanOrderDetailExample> {
    /**
     * @param endTime
     *
     * 拆单
     * @Date: 2018/4/23
     */
    void splitOrders(Date endTime);

    /**
     *
     * 分离某订单
     * @param enyanOrder
     * @Date: 2019-08-02
     */
    void splitOrder(EnyanOrder enyanOrder);
    /**
     *
     *  更新订单明细的版税占比
     * @param
     * @Date: 2020-04-09
     */
    void updateOrderDetailFee();

    /**
     * <p>注销用户</p>
     * @param email
     * @param revokedEmail
     * @return com.aaron.util.ExecuteResult<java.lang.String>
     * @since : 2022/8/26
     **/
    ExecuteResult<String> revokeUser(String email, String revokedEmail);

    /**
     *
     * 从兑换码生成的订单，生成订单、明细并修改兑换码状态
     * @param enyanOrder
     * @Date: 2019-11-13
     */
    EnyanOrder saveOrderFromRedeem(EnyanOrder enyanOrder, EnyanRedeemCode enyanRedeemCodeToUpdate);

    /**
     * @param page
     * @param record
     *
     * 按照订单方式查看明细
     * @Date: 2018/4/26
     */
    Page findRecordsByOrder(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    List<EnyanOrderDetail> findRecordsByOrder(EnyanOrderDetail record);

    /**
     * @param page
     * @param record
     *
     * 按照书籍方式查看明细
     * @Date: 2018/4/26
     */
    Page queryRecordsByBook(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    List<EnyanOrderDetail> queryRecordsByBook(EnyanOrderDetail record);

    Page queryRecordsByVendor(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    List<EnyanOrderDetail> queryRecordsByVendor(EnyanOrderDetail record);

    /**
     * @param page
     * @param record
     *
     *
     * @Date: 2018/11/28
     */
    Page searchRecordsByUser(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    /**
     *
     *
     * @param page
     * @param record
     * @Date: 2019-10-29
     */
    Page searchRecordsByUserAndBookAndAuthorList(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    /**
     * @param record
     *
     *
     * @Date: 2018/11/30
     */
    List<EnyanOrderDetail> searchRecordsByUserList(EnyanOrderDetail record);


    /**
     * @param page
     * @param record
     *
     * 按照书籍方式查看明细
     * @Date: 2018/11/28
     */
    Page searchRecordsByUserAndBook(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    /**
     * @param page
     * @param record
     *
     * 按照day方式查看明细统计
     * @Date: 2018/5/17
     */
    Page queryOrderCountsByDay(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderCountsByDay(EnyanOrderDetail record);

    /**
     * @param page
     * @param record
     *
     * 按照week方式查看明细统计
     * @Date: 2018/5/30
     */
    Page queryOrderCountsByWeek(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderCountsByWeek(EnyanOrderDetail record);

    /**
     * @param page
     * @param record
     *
     * 按照month方式查看明细统计
     * @Date: 2018/5/30
     */
    Page queryOrderCountsByMonth(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderCountsByMonth(EnyanOrderDetail record);

    /**
     * @param page
     * @param record
     *
     * 按照year方式查看明细统计
     * @Date: 2018/5/30
     */
    Page queryOrderCountsByYear(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    List<EnyanOrderDetail> queryOrderCountsByYear(EnyanOrderDetail record);

    /**
     * @param page
     * @param record
     *
     * 按照具体的某个月进行订单明细查询
     * @Date: 2018/6/5
     */
    Page queryOrderDetailBySpecialMonth(Page<EnyanOrderDetail> page, EnyanOrderDetail record);

    /**
     * @param record
     *
     *
     * @Date: 2018/11/28
     */
    long countOrderDetailByOrder(EnyanOrderDetail record);

    /**
     * @param
     *
     *
     * @Date: 2019-06-09
     */
    void resetOrderDetailAndPurchaseInfo();

    /**
     * <p></p>
     * @param email
     * @return void
     * @since : 2024-11-12
     **/
    void resetOrderDetailAndPurchaseInfoByEmail(String email);

    /**
     * <p>重置订单的付费国家</p>
     * @param
     * @return: void
     * @since : 2020-07-15
     */
    void resetOrderDetailAndCountry();

    /**
     * @param enyanOrderDetail
     *
     * 获取所有的订单详情
     * @Date: 2019-06-09
     */
    List<EnyanOrderDetail> findAllOrderDetailList(EnyanOrderDetail enyanOrderDetail);

    /**
     * @param purchaseId
     *
     *
     * @Date: 2019-06-10
     */
    Licenses getLicenseByPurchaseId(int purchaseId);
    /**
     *
     * 根据LicenseID获取License JSON信息
     * @param licenseId
     * @Date: 2019-09-24
     */
    String getLicenseStringByLicenseId(String licenseId);

    /**
     *
     *  根据 licenseId 下载文件
     * @param drmInfo
     * @Date: 2019-06-10
     */
    byte[] downloadLcpFiles(DrmInfo drmInfo);

    /**
     * <p>根据email获取bookId及bookName</p>
     * @param email
     * @return: java.util.List<com.aaron.spring.model.EnyanOrderDetail>
     * @since : 2020-11-05
     */
    List<EnyanOrderDetail> findBookIDAndNameByEmail(String email);

    /**
     * <p>根据bookId获取bookId、bookName及email</p>
     *  * @param bookId
     * @return: java.util.List<com.aaron.spring.model.EnyanOrderDetail>
     * @since : 2021/3/16
     */
    List<EnyanOrderDetail> findBookIDAndNameAndEmailByBookID(Long bookId);

    /**
     * <p>根据email及bookId获取书籍信息</p>
     * @param email
     * @param bookId
     * @return: com.aaron.spring.model.EnyanOrderDetail
     * @since : 12/7/20
     */
    List<EnyanOrderDetail> getBookIDAndNameByEmailAndBookId(String email, Long bookId);
}
