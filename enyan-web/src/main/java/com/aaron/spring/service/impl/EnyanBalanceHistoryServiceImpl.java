package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.mapper.EnyanBalanceHistoryMapper;
import com.aaron.spring.mapper.custom.EnyanBalanceCustomMapper;
import com.aaron.spring.mapper.custom.EnyanBookCostCustomMapper;
import com.aaron.spring.mapper.custom.EnyanOrderDetailCustomMapper;
import com.aaron.spring.model.*;
import com.aaron.spring.service.EnyanBalanceHistoryService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/6/8
 * @Modified By:
 */
@Service
public class EnyanBalanceHistoryServiceImpl implements EnyanBalanceHistoryService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private EnyanBalanceHistoryMapper enyanBalanceHistoryMapper;

    @Resource
    private EnyanOrderDetailCustomMapper enyanOrderDetailCustomMapper;

    @Resource
    private EnyanBalanceCustomMapper enyanBalanceCustomMapper;

    @Resource
    private EnyanBookCostCustomMapper enyanBookCostCustomMapper;

    @Override
    public Page queryRecords(Page<EnyanBalanceHistory> page, EnyanBalanceHistory record) {

        if (null == record){
            record = new EnyanBalanceHistory();
        }
        if (null == page){
            page = new Page<>();
        }
        try {
            EnyanBalanceHistoryExample example = new EnyanBalanceHistoryExample();
            EnyanBalanceHistoryExample.Criteria criteria = example.createCriteria();

            example.setPage(page);
            example.setOrderByClause("balance_at desc");

            if (null != record.getPublisherId()){
                criteria.andPublisherIdEqualTo(record.getPublisherId());
            }
            if (StringUtils.isNotBlank(record.getStartDate())){
                criteria.andBalanceDayGreaterThanOrEqualTo(Integer.parseInt(record.getStartDate()));
            }

            if (StringUtils.isNotBlank(record.getEndDate())){
                criteria.andBalanceDayLessThanOrEqualTo(Integer.parseInt(record.getEndDate()));
            }
            long count = page.getTotalRecord();
            if (count<=0){
                count = enyanBalanceHistoryMapper.countByExample(example);
                page.setTotalRecord(count);
            }
            List<EnyanBalanceHistory> list;
            if (count > 0){
                list = enyanBalanceHistoryMapper.selectByExample(example);
            }else {
                list = new ArrayList<>();
            }

            page.setTotalRecord(count);
            page.setRecords(list);
        } catch (Exception e) {
            e.printStackTrace();
            page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }

        return page;
    }

    @Override
    public ExecuteResult<EnyanBalanceHistory> queryRecordByPrimaryKey(Long pkId) {
        ExecuteResult<EnyanBalanceHistory> result = new ExecuteResult<>();
        try {
            EnyanBalanceHistory record = enyanBalanceHistoryMapper.selectByPrimaryKey(pkId);
            if (null == record){
                //throw new BusinessException(Integer.valueOf("103"),"订单不存在！");
                //record = new EnyanOrder();
                return result;
            }
            result.setResult(record);
        } catch (Exception e) {
            e.printStackTrace();
            result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
        }
        return result;
    }

    @Override
    public ExecuteResult<EnyanBalanceHistory> addRecord(EnyanBalanceHistory record) {
        return null;
    }

    @Override
    public ExecuteResult<EnyanBalanceHistory> updateRecord(EnyanBalanceHistory record) {
        return null;
    }

    @Override
    public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
        return null;
    }

    @Override
    public String checkSaveRecord(EnyanBalanceHistory record) {
        return null;
    }

    @Override
    public String checkUpdateRecord(EnyanBalanceHistory record) {
        return null;
    }

    @Override
    public void saveBalanceHistory(EnyanBalanceHistory balanceHistory, BalanceDetail balanceDetail) {
        try {
            List<EnyanBookCost> bookCostList = new ArrayList<>();
            for (EnyanBook book:balanceDetail.getBookCostList()){
                EnyanBookCost bookCost = new EnyanBookCost();
                bookCost.setBookCost(book.getBookCost());
                bookCost.setBookId(book.getBookId());
                bookCost.setBookTitle(book.getBookTitle());
                bookCost.setIsCounted(EBookConstant.BalanceStatus.ING);
                bookCost.setPublisherId(balanceDetail.getPublisher().getPublisherId());
                bookCost.setPurchasedAt(new Date());

                bookCostList.add(bookCost);
            }
            List<Long> balanceIdList = new ArrayList<>();
            for (EnyanBalance balance:balanceDetail.getBalanceList()){
                balanceIdList.add(balance.getBalanceId());
            }
            enyanBookCostCustomMapper.insertBookCostBatch(bookCostList);
            enyanBalanceCustomMapper.updateBalanceStatusByIds(balanceIdList,balanceDetail.getPublisher().getPublisherId(), EBookConstant.BalanceStatus.ING);
            enyanBalanceHistoryMapper.insert(balanceHistory);
        }catch (Exception e){
            logger.error("saveBalanceHistory error:{}",balanceHistory.getBalanceDetail());
            e.printStackTrace();
        }
    }

    @Override
    public void confirmBalanceHistory(Long historyId, Byte isCounted) {
        try {
            EnyanBalanceHistory balanceHistory = enyanBalanceHistoryMapper.selectByPrimaryKey(historyId);

            BalanceDetail balanceDetail = JSONObject.parseObject(balanceHistory.getBalanceDetail(),BalanceDetail.class);

            List<Long> balanceIdList = new ArrayList<>();
            for (EnyanBalance balance:balanceDetail.getBalanceList()){
                balanceIdList.add(balance.getBalanceId());
            }
            enyanBalanceCustomMapper.updateBalanceStatusByIds(balanceIdList,balanceHistory.getPublisherId(),isCounted);

            EnyanBalanceHistory balanceHistoryNew = new EnyanBalanceHistory();
            balanceHistoryNew.setBalanceHistoryId(balanceHistory.getBalanceHistoryId());
            balanceHistoryNew.setIsCounted(EBookConstant.BalanceStatus.DONE);

            enyanBalanceHistoryMapper.updateByPrimaryKeySelective(balanceHistoryNew);
        }catch (Exception e){
            logger.error("confirmBalanceHistory error:{}",historyId);
            e.printStackTrace();
        }
    }
}
