package com.aaron.spring.service;

import com.aaron.spring.model.EnyanRedeemCode;
import com.aaron.spring.model.EnyanRent;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/11/11
 * @Modified By:
 */
public interface SendEmailService {

	void sendMailOfRedeemCode(EnyanRedeemCode redeemCode, String lang);

	/**
	 * <p>根据邮件的Code发送先租后买的邮件</p>
	 * @param rent
	 * @param mailCode
	 * @return void
	 * @since : 2022/11/16
	 **/
	void sendMailOfRent(EnyanRent rent, String mailCode);
}
