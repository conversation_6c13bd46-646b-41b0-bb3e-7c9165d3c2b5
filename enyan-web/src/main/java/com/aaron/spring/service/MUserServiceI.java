package com.aaron.spring.service;

import java.util.List;

import com.aaron.spring.model.MUser;
import com.aaron.spring.service.page.Query;

public interface MUserServiceI {

	List<MUser> getAll();
	
	MUser selectByPrimaryKey(String id);
	
    int insert(MUser muser);
    
    int update(MUser muser);
    
    int delete(String id);
    
    /**
     * 根据条件分页查询
     *
     * 
     * @param page
     * @param rows
     * @return
     */
    List<MUser> findUserPage(Query query);
}
