package com.aaron.spring.service;

import com.aaron.spring.model.EnyanOrder;
import com.aaron.spring.model.EnyanRent;
import com.aaron.spring.model.EnyanRentDetail;
import com.aaron.spring.model.SlackMsg;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-03-28
 * @Modified By:
 */
public interface LogService {
    /**
     *
     *  msg + 当前时间
     * @param msg
     * @Date: 2020-03-28
     */
    void sendTimeLog(String msg);

    /**
     * <p></p>
     * @param msg
     * @return void
     * @since : 2023/3/7
     **/
    void sendOrderLog(String msg);
    /**
     *
     * 发送常规log
     * @param slackMsg
     * @Date: 2020-03-28
     */
    void sendNormalLog(SlackMsg slackMsg);

    /**
     *
     * 发送订单log
     * @param enyanOrder
     * @Date: 2020-03-28
     */
    void sendOrderLog(EnyanOrder enyanOrder);

    /**
     *
     * 发送订阅订单log
     * @param enyanRent
     * @Date: 2022-11-02
     */
    void sendRentOrderLog(EnyanRent enyanRent);

    /**
     *
     * 发送订阅支付明细订单log
     * @param order
     * @Date: 2022-11-02
     */
    void sendRentOrderDetailLog(EnyanRentDetail order);

    /**
     *
     * 发送Exceptionlog
     * @param slackMsg
     * @Date: 2020-03-28
     */
    void sendExceptionLog(SlackMsg slackMsg);
}
