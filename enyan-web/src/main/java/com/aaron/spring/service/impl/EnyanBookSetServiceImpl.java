package com.aaron.spring.service.impl;

import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.data.DataInterface;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.common.Constant;
import com.aaron.spring.mapper.EnyanBookSetMapper;
import com.aaron.spring.mapper.custom.EnyanBookCustomMapper;
import com.aaron.spring.mapper.custom.EnyanBookSetCustomMapper;
import com.aaron.spring.model.BookWebInfo;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanBookSet;
import com.aaron.spring.model.EnyanBookSetExample;
import com.aaron.spring.service.EnyanBookSetService;
import com.aaron.util.ExecuteResult;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON> Hao
 * @Description:
 * @Date: Created in  2023/2/13
 * @Modified By:
 */
@Slf4j
@Service
public class EnyanBookSetServiceImpl implements EnyanBookSetService {
	@Resource
	private EnyanBookSetMapper enyanBookSetMapper;

	@Resource
	private EnyanBookSetCustomMapper enyanBookSetCustomMapper;

	@Resource
	private EnyanBookCustomMapper enyanBookCustomMapper;

	@Resource
	private DataInterface dataInterface;

	@Override
	public Page queryRecords(Page<EnyanBookSet> page, EnyanBookSet record) {
		if (null == record){
			record = new EnyanBookSet();
		}
		if (null == page){
			page = new Page<>();
		}
		try {
			EnyanBookSetExample example = new EnyanBookSetExample();
			EnyanBookSetExample.Criteria criteria = example.createCriteria();

			example.setPage(page);

			/*
			if (StringUtils.isNotBlank(record.getPublisherName())){
				criteria.andPublisherNameLike("%"+record.getPublisherName()+"%");
			}
			if (null != record.getPublisherId()){
				criteria.andPublisherIdEqualTo(record.getPublisherId());
			}
			*/
			//criteria.andIsDeletedEqualTo(0);
			if (StringUtils.isNotBlank(record.getSetName())){
				criteria.andSetNameLike("%"+record.getSetName()+"%");
			}
			if (null != record.getOrderObjList()){
				StringBuffer buffer = new StringBuffer();
				for (int i = 0; i < record.getOrderObjList().size(); i++) {
					OrderObj orderObj = record.getOrderObjList().get(i);
					if (i!=0){
						buffer.append(",");
					}
					buffer.append(orderObj.getOrderBy() + " " +orderObj.getAscOrDesc());
				}
				example.setOrderByClause(buffer.toString());
			}
			long count = page.getTotalRecord();
			if (count<=0){
				count = enyanBookSetMapper.countByExample(example);
				page.setTotalRecord(count);
			}
			List<EnyanBookSet> list;
			if (count > 0){
				list = enyanBookSetMapper.selectByExample(example);
			}else {
				list = new ArrayList<>();
			}

			page.setRecords(list);
			page.setTotalRecord(count);
		} catch (Exception e) {
			e.printStackTrace();
			page.setErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}

		return page;
	}

	@Override
	public ExecuteResult<EnyanBookSet> queryRecordByPrimaryKey(Long pkId) {
		ExecuteResult<EnyanBookSet> result = new ExecuteResult<>();
		try {
			EnyanBookSet record = dataInterface.getBookSetByID(pkId+"");
			if (null != record){
				result.setResult(record);
				return result;
			}
			record = enyanBookSetMapper.selectByPrimaryKey(pkId);
			if (null == record){
				record = new EnyanBookSet();
			}
			if (StringUtils.isNotBlank(record.getBookWeb())){
				String bookWeb = record.getBookWeb();
				BookWebInfo bookWebInfo = JSON.parseObject(bookWeb,BookWebInfo.class);
				record.setBookWebInfo(bookWebInfo);
			}
			result.setResult(record);
			dataInterface.saveBookSet(record);
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanBookSet> addRecord(EnyanBookSet record) {
		ExecuteResult<EnyanBookSet> result = new ExecuteResult<>();
		try {
			//校验保存对象　
			String checkMsg = this.checkSaveRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}

			int saveFlag = enyanBookSetMapper.insert(record);
			if (saveFlag>0){
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_SAVE_DESCRIPTION);
				result.setResult(record);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_SAVE_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<EnyanBookSet> updateRecord(EnyanBookSet record) {
		ExecuteResult<EnyanBookSet> result = new ExecuteResult<>();
		try {
			EnyanBookSet oldValue = this.queryRecordByPrimaryKey(record.getSetId()).getResult();
			if (null == oldValue){
				return result;
			}
			String oldName = oldValue.getSetName();
			boolean hasChangeName = false;
			if (oldName.equals(record.getSetName()) == false){
				hasChangeName = true;
			}
			//校验保存对象　
			String checkMsg = this.checkUpdateRecord(record);
			if (StringUtils.isNotBlank(checkMsg)){
				result.addErrorMessage("保存校验失败："+ checkMsg);
				return result;
			}
			int saveFlag = enyanBookSetMapper.updateByPrimaryKeySelective(record);
			if (saveFlag>0){
				result.setResult(record);
				result.setSuccessMessage(InterfaceContant.DbStatusConfig.DB_SUCCESS_UPDATE_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_UPDATE_DESCRIPTION);
			}
			dataInterface.delBookSetById(record.getSetId()+"");
			if (hasChangeName == true){//只有更新了书系名字后才需要更新数据
				enyanBookCustomMapper.updateBookSetName(record.getSetName(),record.getSetId());
				/*
				if (Constant.IS_PRODUCT == true){//清空缓存
					List<EnyanBook> bookList =  enyanBookCustomMapper.findBookIDAndNameBySetId(record.getSetId());
					for (EnyanBook book:bookList){
						dataInterface.delBook(book.getBookId());
					}
				}*/
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public ExecuteResult<String> deleteRecordByPrimaryKey(Long pkId) {
		ExecuteResult<String> result = new ExecuteResult<>();
		try {
			//int deleteFlag = enyanBookSetCustomMapper.updateRecordToDeletedById(pkId);
			int deleteFlag = 0;
			if (deleteFlag>0){
				result.setResult(InterfaceContant.DbStatusConfig.DB_SUCCESS_DEL_DESCRIPTION);
			}else {
				result.addErrorMessage(InterfaceContant.DbStatusConfig.DB_FAILURE_DEL_DESCRIPTION);
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.addErrorMessage(InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_DESCRIPTION);
		}
		return result;
	}

	@Override
	public String checkSaveRecord(EnyanBookSet record) {
		return null;
	}

	@Override
	public String checkUpdateRecord(EnyanBookSet record) {
		return null;
	}

	@Override
	public void resetCache(Long dataId) {
		dataInterface.delBookSetById(dataId+"");
	}

	@Override
	public List<EnyanBookSet> findBookSetByIndex(Integer index) {
		return enyanBookSetCustomMapper.findBookSetByIndex(index);
	}
}
