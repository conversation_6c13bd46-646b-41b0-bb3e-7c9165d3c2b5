package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.common.BitSetUtil;
import com.aaron.spring.model.EnyanSpirit;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2020-10-28
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestSpirit extends RestBaseDTO {
    private static final long serialVersionUID = -8405239264399564384L;
    private String name;
    private String author;
    private String planId;
    private Long bookId;
    private Integer days;//灵修计划的天数
    private String slogan;
    private Integer languageType;
    private String bookDescription;
    private String authorDescription;
    private String copyright;
    private String benefitImgUrl;
    private String toBuyImgUrl;
    private String infoImgUrl;//详情图片
    private String bookImgUrl;//封面图片
    private String random;//一些随机值
    private Integer currentDay;
    private String finished;
    private String dataBits;//灵修计划中排除的时间
    private Long startFrom;
    private Integer isDeleted;
    private Long updateTime;
    private List<RestSpirit> list;

    public void initFrom(EnyanSpirit obj){
        this.name = obj.getName();
        this.author = obj.getAuthor();
        this.bookId = obj.getBookId();
        this.days = obj.getDays();
        this.bookDescription = obj.getBookDescription();
        this.authorDescription = obj.getAuthorDescription();
        this.copyright = obj.getCopyright();
        //this.benefitImgUrl = obj.getBenefitImgUrl();
        this.toBuyImgUrl = obj.getToBuyImgUrl();
        this.infoImgUrl = obj.getInfoImgUrl();
        this.bookImgUrl = obj.getBookImgUrl();
        this.dataBits = obj.getDataBits();
        this.slogan = obj.getSlogan();
        this.languageType = obj.getLanguageType();
        /*if (obj.getDataBitsBitSet() != null){
            this.dataBits = BitSetUtil.encode(obj.getDataBitsBitSet());
        }*/
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public String getBookDescription() {
        return bookDescription;
    }

    public void setBookDescription(String bookDescription) {
        this.bookDescription = bookDescription;
    }

    public String getAuthorDescription() {
        return authorDescription;
    }

    public void setAuthorDescription(String authorDescription) {
        this.authorDescription = authorDescription;
    }

    public String getCopyright() {
        return copyright;
    }

    public void setCopyright(String copyright) {
        this.copyright = copyright;
    }

    public String getToBuyImgUrl() {
        return toBuyImgUrl;
    }

    public void setToBuyImgUrl(String toBuyImgUrl) {
        this.toBuyImgUrl = toBuyImgUrl;
    }

    public String getInfoImgUrl() {
        return infoImgUrl;
    }

    public void setInfoImgUrl(String infoImgUrl) {
        this.infoImgUrl = infoImgUrl;
    }

    public String getBookImgUrl() {
        return bookImgUrl;
    }

    public void setBookImgUrl(String bookImgUrl) {
        this.bookImgUrl = bookImgUrl;
    }

    public String getRandom() {
        return random;
    }

    public void setRandom(String random) {
        this.random = random;
    }

    public Integer getCurrentDay() {
        return currentDay;
    }

    public void setCurrentDay(Integer currentDay) {
        this.currentDay = currentDay;
    }

    public String getFinished() {
        return finished;
    }

    public void setFinished(String finished) {
        this.finished = finished;
    }

    public Long getStartFrom() {
        return startFrom;
    }

    public void setStartFrom(Long startFrom) {
        this.startFrom = startFrom;
    }

    public String getDataBits() {
        return dataBits;
    }

    public void setDataBits(String dataBits) {
        this.dataBits = dataBits;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public List<RestSpirit> getList() {
        return list;
    }

    public void setList(List<RestSpirit> list) {
        this.list = list;
    }

    public String getSlogan() {
        return slogan;
    }

    public void setSlogan(String slogan) {
        this.slogan = slogan;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getBenefitImgUrl() {
        return benefitImgUrl;
    }

    public void setBenefitImgUrl(String benefitImgUrl) {
        this.benefitImgUrl = benefitImgUrl;
    }

    public static  void main(String[] args){
        printInit("com.aaron.spring.api.v3.model.RestSpirit","EnyanSpirit");
    }
}
