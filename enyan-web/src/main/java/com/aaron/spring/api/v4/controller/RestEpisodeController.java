package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestEpisode;
import com.aaron.spring.model.PodEpisode;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.PodEpisodeService;
import com.aaron.spring.service.PodPodcastService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Author: <PERSON>
 * @Description: 播客单集API控制器
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Slf4j
@RestController("RestEpisodeControllerV4")
@RequestMapping("/api/v4/episode")
public class RestEpisodeController extends RestBaseController {

    @Resource
    private PodEpisodeService podEpisodeService;
    
    @Resource
    private PodPodcastService podPodcastService;

//    /**
//     * 根据播客ID获取单集列表
//     * @param restObj 请求参数
//     * @param request HTTP请求
//     * @return 单集分页列表
//     */
//    @LoginAnonymous
//    @RequestMapping(value = "/list", method = RequestMethod.POST)
//    public PageResult<RestEpisode> list(@RequestBody RestEpisode restObj, HttpServletRequest request) {
//        restObj.initHeaderValue(request);
//
//        PageResult<RestEpisode> pageResult = new PageResult<>();
//        if (null == restObj.getPage() || null == restObj.getPodcastId()) {
//            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
//            return pageResult;
//        }
//
//        Page<PodEpisode> page = new Page();
//        page.setCurrentPage(restObj.getPage());
//        page.setPageSize(pageResult.getPageSize());
//
//        // 更新播客的播放量
//        podPodcastService.updatePodcastPlayCountById(restObj.getPodcastId());
//
//        // 获取播客的单集列表
//        page = podEpisodeService.queryEpisodesByPodcastId(page, restObj.getPodcastId());
//
//        for (PodEpisode obj : page.getRecords()) {
//            RestEpisode tmp = new RestEpisode();
//            tmp.initFrom(obj);
//            pageResult.getResult().add(tmp);
//        }
//        pageResult.setCurrentPage(page.getCurrentPage());
//        pageResult.setTotalRecord(page.getTotalRecord());
//        return pageResult;
//    }

    /**
     * 获取单集详情
     * @param restObj 请求参数
     * @param request HTTP请求
     * @return 单集详情
     */
    @LoginAnonymous
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public ExecuteResult<RestEpisode> episodeInfo(@RequestBody RestEpisode restObj, HttpServletRequest request) {
        restObj.initHeaderValue(request);

        ExecuteResult<RestEpisode> result = new ExecuteResult<>();
        if (null == restObj.getEpisodeId()) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        PodEpisode episode = podEpisodeService.queryRecordByPrimaryKey(restObj.getEpisodeId()).getResult();
        if (null == episode || episode.getIsDeleted() == 1) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        RestEpisode tmp = new RestEpisode();
        tmp.initFrom(episode);
        result.setResult(tmp);
        
        // 更新播放次数
        podEpisodeService.updateEpisodeListenCountById(restObj.getEpisodeId());
        
        return result;
    }

    /**
     * 播客单集点赞
     * @param restObj 请求参数
     * @param request HTTP请求
     * @return 操作结果
     */
    @LoginAnonymous
    @RequestMapping(value = "/like", method = RequestMethod.POST)
    public ExecuteResult<RestEpisode> like(@RequestBody RestEpisode restObj, HttpServletRequest request) {
        restObj.initHeaderValue(request);

        ExecuteResult<RestEpisode> result = new ExecuteResult<>();
        if (null == restObj.getEpisodeId()) {
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }

        podEpisodeService.updateEpisodeLikeCountById(restObj.getEpisodeId());
        return result;
    }
    
    /**
     * 获取最新播客单集列表
     * @param restObj 请求参数
     * @param request HTTP请求
     * @return 最新单集分页列表
     */
    @LoginAnonymous
    @RequestMapping(value = "/latest", method = RequestMethod.POST)
    public PageResult<RestEpisode> latest(@RequestBody RestEpisode restObj, HttpServletRequest request) {
        restObj.initHeaderValue(request);

        PageResult<RestEpisode> pageResult = new PageResult<>();
        if (null == restObj.getPage()) {
            pageResult.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return pageResult;
        }

        PodEpisode searchObj = new PodEpisode();
        Page<PodEpisode> page = new Page();
        page.setCurrentPage(restObj.getPage());
        page.setPageSize(pageResult.getPageSize());
        searchObj.setPage(page);
        searchObj.setIsDeleted(0);
        searchObj.setIsPublished(1);

        // 按发布时间降序排序
        OrderObj orderObj = new OrderObj("publication_date", InterfaceContant.OrderBy.DESC);
        searchObj.addOrder(orderObj);

        page = podEpisodeService.queryRecords(page, searchObj);

        for (PodEpisode obj : page.getRecords()) {
            RestEpisode tmp = new RestEpisode();
            tmp.initFrom(obj);
            pageResult.getResult().add(tmp);
        }
        pageResult.setCurrentPage(page.getCurrentPage());
        pageResult.setTotalRecord(page.getTotalRecord());
        return pageResult;
    }
}
