package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.EnyanReaderHighlights;
import com.aaron.spring.model.EnyanUserHighlights;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/8/24
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestHighlight extends RestBaseDTO{

    private static final long serialVersionUID = 374573397125414827L;

    private String id;

    private String highlightID;

    private Long userId;

    private String publicationID;

    private Long bookID;

    private Integer resourceIndex;

    private String resourceHref;

    private String resourceType;

    private String resourceTitle;

    private String location;

    private String locatorText;

    private Integer color;

    private String annotation;

    private Integer type;//数据类型 0：划线；1：书签；

    private Long creationDate;

    private Integer isDeleted;

    private Long updateTime;

    private List<RestHighlight> list;

    public void initFrom(EnyanReaderHighlights obj){
        this.id = obj.getId();
        this.highlightID = obj.getHighlightId();
        this.userId = obj.getUserId();
        this.setEmail(obj.getUserEmail());
        this.publicationID = obj.getPublicationId();
        this.bookID = obj.getBookId();
        this.resourceIndex = obj.getResourceIndex();
        this.color = obj.getColor();
        this.annotation = obj.getAnnotation();
        this.resourceHref = obj.getResourceHref();
        this.resourceType = obj.getResourceType();
        this.resourceTitle = obj.getResourceTitle();
        this.location = obj.getLocation();
        this.type = obj.getType();
        this.locatorText = obj.getLocatorText();
        this.creationDate = obj.getCreationDate();
        this.isDeleted = obj.getIsDeleted();
        this.updateTime = obj.getUpdateTime();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHighlightID() {
        return highlightID;
    }

    public void setHighlightID(String highlightID) {
        this.highlightID = highlightID;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPublicationID() {
        return publicationID;
    }

    public void setPublicationID(String publicationID) {
        this.publicationID = publicationID;
    }

    public Long getBookID() {
        return bookID;
    }

    public void setBookID(Long bookID) {
        this.bookID = bookID;
    }

    public Integer getResourceIndex() {
        return resourceIndex;
    }

    public void setResourceIndex(Integer resourceIndex) {
        this.resourceIndex = resourceIndex;
    }

    public Integer getColor() {
        return color;
    }

    public void setColor(Integer color) {
        this.color = color;
    }

    public String getAnnotation() {
        return annotation;
    }

    public void setAnnotation(String annotation) {
        this.annotation = annotation;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getResourceHref() {
        return resourceHref;
    }

    public void setResourceHref(String resourceHref) {
        this.resourceHref = resourceHref;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getResourceTitle() {
        return resourceTitle;
    }

    public void setResourceTitle(String resourceTitle) {
        this.resourceTitle = resourceTitle;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLocatorText() {
        return locatorText;
    }

    public void setLocatorText(String locatorText) {
        this.locatorText = locatorText;
    }

    public Long getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Long creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public List<RestHighlight> getList() {
        return list;
    }

    public void setList(List<RestHighlight> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

    public static  void main(String[] args){
        //printInit("com.aaron.spring.api.v3.model.RestHighlight","EnyanReaderHighlights");
        printProperty("com.aaron.spring.api.v3.model.RestHighlight","EnyanReaderHighlights");
    }
}
