package com.aaron.spring.api.v3.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.EnyanPlanNote;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2020-11-10
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestPlanNote extends RestBaseDTO {
    private static final long serialVersionUID = -5304792316062952326L;


    private Long bookId;

    //private String name;

    private Integer day;

    private String dayName;

    private String note;

    private Long createTime;

    private Long updateTime;

    private Integer isDeleted;

    private List<RestPlanNote> list;

    public void initFrom(EnyanPlanNote obj){
        this.setEmail(obj.getUserEmail());
        this.bookId = obj.getBookId();
        this.day = obj.getDay();
        this.dayName = obj.getDayName();
        this.note = obj.getNote();
        this.isDeleted = obj.getIsDeleted();
        this.createTime = obj.getCreateTime();
        this.updateTime = obj.getUpdateTime();
    }



    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }


    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public String getDayName() {
        return dayName;
    }

    public void setDayName(String dayName) {
        this.dayName = dayName;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public List<RestPlanNote> getList() {
        return list;
    }

    public void setList(List<RestPlanNote> list) {
        this.list = list;
    }

    public static  void main(String[] args){
        printInit("com.aaron.spring.api.v3.model.RestPlanNote","EnyanPlanNote");
    }
}
