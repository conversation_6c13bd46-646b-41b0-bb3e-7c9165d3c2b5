package com.aaron.spring.api.v4.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.BaseDTO;
import com.aaron.spring.model.EnyanRentDetail;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/10/26
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestRentDetail extends RestBaseDTO {
	private static final long serialVersionUID = 647716133754496336L;
	private Long detailId;

	private String orderNum;

	private String userEmail;

	private Integer rentType;

	private Integer rentLang;

	private Integer isAuto;

//	private Long publisherId;

	private String rateValue;

	private BigDecimal fee;

	private Integer rentMonths;

	private Integer vendorPercent;

	private BigDecimal incomeVendor;

	private BigDecimal incomePlat;

	private BigDecimal incomeTotal;

	private BigDecimal incomeReal;

	private BigDecimal payFee;

	private BigDecimal totalFee;

	private BigDecimal netSales;

	private Integer orderCurrency;

	/**
	 * 1，11：支付宝；2，21，22：信用卡；4：兑换码
	 * */
	private Integer payType;

	private String payCountry;

	private Integer orderFrom;

	private Long purchasedAt;

	private Long fromAt;

	private Long expiredAt;

	public void initFrom(EnyanRentDetail obj){
		this.detailId = obj.getDetailId();
		this.orderNum = obj.getOrderNum();
		this.userEmail = obj.getUserEmail();
		this.rentType = obj.getRentType();
		this.rentLang = obj.getRentLang();
		this.isAuto = obj.getIsAuto();
//		this.rateValue = obj.getRateValue();
		this.fee = obj.getIncomeTotal();
		this.rentMonths = obj.getRentMonths();
		this.totalFee = obj.getIncomeTotal();
//		this.vendorPercent = obj.getVendorPercent();
//		this.incomeVendor = obj.getIncomeVendor();
//		this.incomePlat = obj.getIncomePlat();
//		this.incomeTotal = obj.getIncomeTotal();
//		this.incomeReal = obj.getIncomeReal();
//		this.payFee = obj.getPayFee();
//		this.netSales = obj.getNetSales();
//		this.orderCurrency = obj.getOrderCurrency();
		this.payType = obj.getPayType();
//		this.payCountry = obj.getPayCountry();
//		this.orderFrom = obj.getOrderFrom();
		this.purchasedAt = null == obj.getPurchasedAt()? null : obj.getPurchasedAt().getTime();
//		this.fromAt = null == obj.getFromAt()? null : obj.getFromAt().getTime();
//		this.expiredAt = null == obj.getExpiredAt()? null : obj.getExpiredAt().getTime();
	}

	public Long getDetailId() {
		return detailId;
	}

	public void setDetailId(Long detailId) {
		this.detailId = detailId;
	}

	public String getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(String orderNum) {
		this.orderNum = orderNum;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public Integer getRentType() {
		return rentType;
	}

	public void setRentType(Integer rentType) {
		this.rentType = rentType;
	}

	public Integer getRentLang() {
		return rentLang;
	}

	public void setRentLang(Integer rentLang) {
		this.rentLang = rentLang;
	}

	public Integer getIsAuto() {
		return isAuto;
	}

	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}

	public String getRateValue() {
		return rateValue;
	}

	public void setRateValue(String rateValue) {
		this.rateValue = rateValue;
	}

	public BigDecimal getFee() {
		return fee;
	}

	public void setFee(BigDecimal fee) {
		this.fee = fee;
	}

	public Integer getRentMonths() {
		return rentMonths;
	}

	public void setRentMonths(Integer rentMonths) {
		this.rentMonths = rentMonths;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getOrderFrom() {
		return orderFrom;
	}

	public void setOrderFrom(Integer orderFrom) {
		this.orderFrom = orderFrom;
	}

	public Long getPurchasedAt() {
		return purchasedAt;
	}

	public void setPurchasedAt(Long purchasedAt) {
		this.purchasedAt = purchasedAt;
	}

	public Long getFromAt() {
		return fromAt;
	}

	public void setFromAt(Long fromAt) {
		this.fromAt = fromAt;
	}

	public Long getExpiredAt() {
		return expiredAt;
	}

	public void setExpiredAt(Long expiredAt) {
		this.expiredAt = expiredAt;
	}

	public Integer getVendorPercent() {
		return vendorPercent;
	}

	public void setVendorPercent(Integer vendorPercent) {
		this.vendorPercent = vendorPercent;
	}

	public BigDecimal getIncomeVendor() {
		return incomeVendor;
	}

	public void setIncomeVendor(BigDecimal incomeVendor) {
		this.incomeVendor = incomeVendor;
	}

	public BigDecimal getIncomePlat() {
		return incomePlat;
	}

	public void setIncomePlat(BigDecimal incomePlat) {
		this.incomePlat = incomePlat;
	}

	public BigDecimal getIncomeTotal() {
		return incomeTotal;
	}

	public void setIncomeTotal(BigDecimal incomeTotal) {
		this.incomeTotal = incomeTotal;
	}

	public BigDecimal getIncomeReal() {
		return incomeReal;
	}

	public void setIncomeReal(BigDecimal incomeReal) {
		this.incomeReal = incomeReal;
	}

	public BigDecimal getPayFee() {
		return payFee;
	}

	public void setPayFee(BigDecimal payFee) {
		this.payFee = payFee;
	}

	public BigDecimal getNetSales() {
		return netSales;
	}

	public void setNetSales(BigDecimal netSales) {
		this.netSales = netSales;
	}

	public Integer getOrderCurrency() {
		return orderCurrency;
	}

	public void setOrderCurrency(Integer orderCurrency) {
		this.orderCurrency = orderCurrency;
	}

	public String getPayCountry() {
		return payCountry;
	}

	public void setPayCountry(String payCountry) {
		this.payCountry = payCountry;
	}

	public BigDecimal getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(BigDecimal totalFee) {
		this.totalFee = totalFee;
	}

	public static  void main(String[] args){
		printInit("com.aaron.spring.api.v4.model.RestRentDetail","EnyanRentDetail");
	}
}
