package com.aaron.spring.api.v3.controller;

import co.endao.util.SpiritBookUtil;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.exception.BusinessException;
import com.aaron.exception.RestException;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v3.model.RestSpirit;
import com.aaron.spring.common.AaronHtmlUtils;
import com.aaron.spring.model.EnyanPlan;
import com.aaron.spring.model.EnyanSpirit;
import com.aaron.spring.service.EnyanPlanService;
import com.aaron.spring.service.EnyanSpiritService;
import com.aaron.util.FilePropertiesUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;

/**
 *
 *  BusinessException 捕获异常 6XX
 * @Author: Aaron Hao
 * @Date: Created in  2020/10/28
 * @Modified By:
 */
@Controller("RestReadControllerV3")
@RequestMapping("/api/v3/spiritRead")
public class RestReadController extends RestBaseController {
    private final Log logger = LogFactory.getLog(RestReadController.class);
    public static final Long ABS_DAY_OFFSET = 24 * 60 * 60 * 1000L; //24小时
    public static final Long ONE_DAY = 24 * 60 * 60 * 1000L; //24小时
    private static final String ERROR_NAME = "error.txt";
    private static final String epubBaseDir = FilePropertiesUtil.props.getProperty("epubBaseDir");//
    private static String Book_Img1 = "https://ehome.endao.co/book_image/detail/1601437535784.png";
    private static String Book_Img2 = "https://ehome.endao.co/book_image/detail/1600848324877.png";
    private static String Spirit_List = "https://ehome.endao.co/statics/images/tmp/spiritList.png";
    private static String Spirit_Read = "https://ehome.endao.co/statics/images/tmp/spiritRead.xhtml";
    private static String Link_Base = "https://ehome.endao.co/statics/Styles/";


    @Resource
    private EnyanSpiritService enyanSpiritService;

    @Resource
    private EnyanPlanService enyanPlanService;


    @RequestMapping(value = "/read") //,produces = {MediaType.TEXT_HTML_VALUE}
    public String read(@RequestBody RestSpirit rest, HttpServletRequest request, ModelMap modelMap){//produces = "text/html;charset=utf-8"

        rest.initHeaderValue(request);
        if (StringUtils.isBlank(rest.getEmail()) || null == rest.getCurrentDay()|| null == rest.getBookId()){
            return this.getEmptyReadInfo(modelMap,"001");
        }

        EnyanSpirit spirit = enyanSpiritService.getSpiritByBookId(rest.getBookId());
        if (null == spirit || StringUtils.isBlank(spirit.getFileName())){
            return this.getEmptyReadInfo(modelMap,"002");
        }

        EnyanPlan plan = enyanPlanService.getPlanByEmailAndBookId(rest.getEmail(), rest.getBookId());
        if (null == plan){
            return this.getEmptyReadInfo(modelMap,"003");
        }
        if (plan.getHasBuy() == 0){//未购买
            Long dayOffset = System.currentTimeMillis() - plan.getStartFrom() - rest.getCurrentDay() * ONE_DAY;
            if (Math.abs(dayOffset) > ABS_DAY_OFFSET){//在偏差时间之外
                return this.getEmptyReadInfo(modelMap,"004");
            }
        }
        int dayRead = rest.getCurrentDay();
        if (dayRead >= spirit.getDays()){
            dayRead = spirit.getDays() - 1;
        }
        String filePath = SpiritBookUtil.Companion.get().getPathForDayInWeb(spirit.getFileName(), dayRead);
        if (StringUtils.isBlank(filePath)){
            return this.getEmptyReadInfo(modelMap,"005");
        }
        File readFile = new File(filePath);
        if (!readFile.exists()){
            throw new RestException(601, InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "内容不存在，book="+ rest.getBookId()+",day="+ rest.getCurrentDay());
        }
        try {
            String text =  FileUtils.readFileToString(readFile, Charset.defaultCharset());
            modelMap.addAttribute("text", AaronHtmlUtils.replaceLinkBasePath(text,Link_Base));
            return "emptyButInputText";
        } catch (IOException e) {
            throw new RestException(602, InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "内容不存在，book="+ rest.getBookId()+",day="+ rest.getCurrentDay());
        }
    }

    @RequestMapping(value = "/getPage1") //,produces = {MediaType.TEXT_HTML_VALUE}
    public String getPage1(HttpServletRequest request, ModelMap modelMap){//produces = "text/html;charset=utf-8"

        String bookIdStr = request.getParameter("bookId");
        String dayStr = request.getParameter("day");
        if (StringUtils.isBlank(bookIdStr) || StringUtils.isBlank(dayStr)){
            return this.getEmptyReadInfo(modelMap,"001");
        }

        Long bookId = Long.parseLong(bookIdStr);
        int day = Integer.parseInt(dayStr);

        EnyanSpirit spirit = enyanSpiritService.getSpiritByBookId(bookId);
        if (null == spirit || StringUtils.isBlank(spirit.getFileName())){
            return this.getEmptyReadInfo(modelMap,"002");
        }
        String filePath = SpiritBookUtil.Companion.get().getPathForDayInWeb(spirit.getFileName(),day);
        if (StringUtils.isBlank(filePath)){
            return this.getEmptyReadInfo(modelMap,"003");
        }
        File readFile = new File(filePath);
        if (!readFile.exists()){
            throw new RestException(Integer.valueOf(601), InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "内容不存在，book="+bookIdStr+",day="+dayStr);
        }
        try {
            String text =  FileUtils.readFileToString(readFile, Charset.defaultCharset());
            modelMap.addAttribute("text", AaronHtmlUtils.replaceLinkBasePath(text,Link_Base));
            return "emptyButInputText";
        } catch (IOException e) {
            throw new RestException(Integer.valueOf(602), InterfaceContant.ApiErrorConfig.SYSTEM_ERROR_CODE, "内容不存在，book="+bookIdStr+",day="+dayStr);
        }
    }

    private String getEmptyReadInfo(ModelMap modelMap, String errorMsg){
        StringBuffer sbHtml = new StringBuffer();
        sbHtml.append("<!doctype html><html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">");
        sbHtml.append("<title>阅读灵修书籍</title></head><body>暂无内容-");
        sbHtml.append(errorMsg);
        sbHtml.append("</body></html>");
        modelMap.addAttribute("text",sbHtml.toString());
        return "emptyButInputText";
    }

}
