package com.aaron.spring.api.v3.model;

import com.aaron.spring.api.RestBaseDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/4/28
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestCoupon<T> extends RestBaseDTO {
   private static final long serialVersionUID = -1658335695012052812L;
   private String couponCode;
   private BigDecimal value;
   private Integer useMax;//用户个人最大使用次数
   private Long beginDate ;
   private Long endDate;
   private BigDecimal minLimitValue;//满减最低限额
   private List<T> list;

   public String getCouponCode() {
      return couponCode;
   }

   public void setCouponCode(String couponCode) {
      this.couponCode = couponCode;
   }

   public BigDecimal getValue() {
      return value;
   }

   public Integer getUseMax() {
      return useMax;
   }

   public void setUseMax(Integer useMax) {
      this.useMax = useMax;
   }

   public void setValue(BigDecimal value) {
      this.value = value;
   }

   public Long getBeginDate() {
      return beginDate;
   }

   public void setBeginDate(Long beginDate) {
      this.beginDate = beginDate;
   }

   public Long getEndDate() {
      return endDate;
   }

   public void setEndDate(Long endDate) {
      this.endDate = endDate;
   }

   public BigDecimal getMinLimitValue() {
      return minLimitValue;
   }

   public void setMinLimitValue(BigDecimal minLimitValue) {
      this.minLimitValue = minLimitValue;
   }

   public List<T> getList() {
      return list;
   }

   public void setList(List<T> list) {
      this.list = list;
   }
}
