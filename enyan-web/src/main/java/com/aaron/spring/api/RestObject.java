package com.aaron.spring.api;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  11/19/20
 * @Modified By:
 */
public class RestObject<T> implements Serializable {
    private static final long serialVersionUID = -5166858144391577982L;
    private T obj;

    public T getObj() {
        return obj;
    }

    public void setObj(T obj) {
        this.obj = obj;
    }
}
