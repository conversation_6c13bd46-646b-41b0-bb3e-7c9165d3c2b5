package com.aaron.spring.api.v4.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.common.WebUtil;
import com.aaron.spring.model.EnyanBlog;
import com.aaron.util.ServerUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/7/20
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestBlog extends RestBaseDTO {
	private Long blogId;

	private String blogTitle;

	private String author;

	private Long publisherId;

	private String blogCover;

	private String blogCoverApp;

	private String blogAbstract;

	private Integer recommendedOrder;

	private String recommendedCaption;

	private Integer categoryId;

	private Integer readCount;

	private Integer likeCount;

	private Integer isDeleted;

	private Date createAt;

	private String blogContent;

	private Integer orderBy;//0:默认；1:最新；2：热门

	private String toUrl;

	public void initFrom(EnyanBlog obj){
		this.blogId = obj.getBlogId();
		this.blogTitle = obj.getBlogTitle();
		this.author = obj.getAuthor();
		this.publisherId = obj.getPublisherId();
		this.blogCover = obj.getBlogCover();
		this.blogCoverApp = obj.getBlogCoverApp();
		this.blogAbstract = obj.getBlogAbstract();
		this.recommendedOrder = obj.getRecommendedOrder();
		this.recommendedCaption = obj.getRecommendedCaption();
		this.categoryId = obj.getCategoryId();
		this.readCount = obj.getReadCount();
		this.likeCount = obj.getLikeCount();
		this.isDeleted = obj.getIsDeleted();
		this.createAt = obj.getCreateAt();
		this.blogContent = obj.getBlogContent();
		this.toUrl = WebUtil.getBasePath()+"blogA-"+obj.getBlogId();
	}

	public Long getBlogId() {
		return blogId;
	}

	public void setBlogId(Long blogId) {
		this.blogId = blogId;
	}

	public String getBlogTitle() {
		return blogTitle;
	}

	public void setBlogTitle(String blogTitle) {
		this.blogTitle = blogTitle;
	}

	public String getAuthor() {
		return author;
	}

	public void setAuthor(String author) {
		this.author = author;
	}

	public Long getPublisherId() {
		return publisherId;
	}

	public void setPublisherId(Long publisherId) {
		this.publisherId = publisherId;
	}

	public String getBlogCover() {
		return blogCover;
	}

	public void setBlogCover(String blogCover) {
		this.blogCover = blogCover;
	}

	public String getBlogCoverApp() {
		return blogCoverApp;
	}

	public void setBlogCoverApp(String blogCoverApp) {
		this.blogCoverApp = blogCoverApp;
	}

	public String getBlogAbstract() {
		return blogAbstract;
	}

	public void setBlogAbstract(String blogAbstract) {
		this.blogAbstract = blogAbstract;
	}

	public Integer getRecommendedOrder() {
		return recommendedOrder;
	}

	public void setRecommendedOrder(Integer recommendedOrder) {
		this.recommendedOrder = recommendedOrder;
	}

	public String getRecommendedCaption() {
		return recommendedCaption;
	}

	public void setRecommendedCaption(String recommendedCaption) {
		this.recommendedCaption = recommendedCaption;
	}

	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getReadCount() {
		return readCount;
	}

	public void setReadCount(Integer readCount) {
		this.readCount = readCount;
	}

	public Integer getLikeCount() {
		return likeCount;
	}

	public void setLikeCount(Integer likeCount) {
		this.likeCount = likeCount;
	}

	public Integer getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Date getCreateAt() {
		return createAt;
	}

	public void setCreateAt(Date createAt) {
		this.createAt = createAt;
	}

	public String getBlogContent() {
		return blogContent;
	}

	public void setBlogContent(String blogContent) {
		this.blogContent = blogContent;
	}

	public Integer getOrderBy() {
		return orderBy;
	}

	public void setOrderBy(Integer orderBy) {
		this.orderBy = orderBy;
	}

	public String getToUrl() {
		return toUrl;
	}

	public void setToUrl(String toUrl) {
		this.toUrl = toUrl;
	}

	public static void main(String[] args) {
		printInit("com.aaron.spring.api.v4.model.RestBlog","EnyanBlog");
	}
}
