package com.aaron.spring.api.v1.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.EnyanUserHighlights;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/8/24
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestHighlight extends RestBaseDTO{

    private static final long serialVersionUID = 374573397125414827L;
    private String highlightId;

    private Long userId;

    private String email;

    private Long bookId;

    private String content;

    private Long createTime;

    private String rangy;

    private String noteForHighlight;

    private String chapterName;

    private Integer pageChapter;

    private Integer currentPage;

    private Integer totalPage;

    private Integer isDeleted;

    private Long updateTime;

    private Integer pageProcess;

    private List<RestHighlight> list;

    public void initFrom(EnyanUserHighlights obj){
        this.highlightId = obj.getHighlightId();
        this.userId = obj.getUserId();
        this.email = obj.getUserEmail();
        this.bookId = obj.getBookId();
        this.content = obj.getContent();
        this.createTime = obj.getCreateTime();
        this.rangy = obj.getRangy();
        this.noteForHighlight = obj.getNoteForHighlight();
        this.chapterName = obj.getChapterName();
        this.pageChapter = obj.getPageChapter();
        this.currentPage = obj.getCurrentPage();
        this.totalPage = obj.getTotalPage();
        this.isDeleted = obj.getIsDeleted();
        this.updateTime = obj.getUpdateTime();
        this.pageProcess = obj.getPageProcess();
    }

    public String getHighlightId() {
        return highlightId;
    }

    public void setHighlightId(String highlightId) {
        this.highlightId = highlightId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String getEmail() {
        return email;
    }

    @Override
    public void setEmail(String email) {
        this.email = email;
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRangy() {
        return rangy;
    }

    public void setRangy(String rangy) {
        this.rangy = rangy;
    }

    public String getNoteForHighlight() {
        return noteForHighlight;
    }

    public void setNoteForHighlight(String noteForHighlight) {
        this.noteForHighlight = noteForHighlight;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public Integer getPageChapter() {
        return pageChapter;
    }

    public void setPageChapter(Integer pageChapter) {
        this.pageChapter = pageChapter;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }


    public List<RestHighlight> getList() {
        return list;
    }

    public void setList(List<RestHighlight> list) {
        this.list = list;
    }

    public Integer getPageProcess() {
        return pageProcess;
    }

    public void setPageProcess(Integer pageProcess) {
        this.pageProcess = pageProcess;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public static  void main(String[] args){
        printInit("com.aaron.spring.api.v1.model.RestHighlight","EnyanUserHighlights");
    }
}
