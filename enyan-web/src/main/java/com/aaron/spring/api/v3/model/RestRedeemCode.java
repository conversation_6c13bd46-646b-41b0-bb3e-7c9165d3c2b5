package com.aaron.spring.api.v3.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.EnyanBook;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/7/19
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestRedeemCode extends RestBaseDTO {
   private static final long serialVersionUID = -8011174917852033310L;
   private String code;
   private String sendText;
   private String toEmail;
   private Integer status;
   private String createTime;
   private Long exchangeTime;
   private RestRedeemCodeNoteInfo redeemNoteInfo;
   private List<RestBook> booksToRedeem;

   public String getCode() {
      return code;
   }

   public void setCode(String code) {
      this.code = code;
   }

   public String getSendText() {
      return sendText;
   }

   public void setSendText(String sendText) {
      this.sendText = sendText;
   }

   public String getToEmail() {
      return toEmail;
   }

   public void setToEmail(String toEmail) {
      this.toEmail = toEmail;
   }

   public Integer getStatus() {
      return status;
   }

   public void setStatus(Integer status) {
      this.status = status;
   }

   public String getCreateTime() {
      return createTime;
   }

   public void setCreateTime(String createTime) {
      this.createTime = createTime;
   }

   public Long getExchangeTime() {
      return exchangeTime;
   }

   public void setExchangeTime(Long exchangeTime) {
      this.exchangeTime = exchangeTime;
   }

   public RestRedeemCodeNoteInfo getRedeemNoteInfo() {
      return redeemNoteInfo;
   }

   public void setRedeemNoteInfo(RestRedeemCodeNoteInfo redeemNoteInfo) {
      this.redeemNoteInfo = redeemNoteInfo;
   }

   public List<RestBook> getBooksToRedeem() {
      return booksToRedeem;
   }

   public void setBooksToRedeem(List<RestBook> booksToRedeem) {
      this.booksToRedeem = booksToRedeem;
   }
}
