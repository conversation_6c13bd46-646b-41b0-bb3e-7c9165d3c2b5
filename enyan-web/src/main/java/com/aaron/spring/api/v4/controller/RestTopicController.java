package com.aaron.spring.api.v4.controller;

import com.aaron.annotation.LoginAnonymous;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.common.OrderObj;
import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestTopic;
import com.aaron.spring.model.PodTopic;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.PodTopicService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description: 播客主题API控制器
 * @Date: Created in  2025/5/13
 * @Modified By:
 */
@Slf4j
@RestController("RestTopicControllerV4")
@RequestMapping("/api/v4/topic")
public class RestTopicController extends RestBaseController {

    @Resource
    private PodTopicService podTopicService;


    
    /**
     * 添加播客主题
     * @param restObj 请求参数
     * @param request HTTP请求
     * @return 添加结果
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ExecuteResult<RestTopic> addTopic(@RequestBody RestTopic restObj, HttpServletRequest request) {
        restObj.initHeaderValue(request);
        ExecuteResult<RestTopic> result = new ExecuteResult<>();
        
        // 权限校验
//        if (!checkAdminOrScoper(request, result)) {
//            return result;
//        }
        
        if (null == restObj.getPodcastId()) {
            result.addErrorMessage("播客ID不能为空");
            return result;
        }
        
        if (StringUtils.isBlank(restObj.getTitle())) {
            result.addErrorMessage("主题标题不能为空");
            return result;
        }
        
        PodTopic topic = restObj.toPodTopic();
        ExecuteResult<PodTopic> addResult = podTopicService.addTopic(topic);
        
        if (!addResult.isSuccess() || null == addResult.getResult()) {
            result.setErrorMessages(addResult.getErrorMessages());
            return result;
        }
        
        RestTopic restTopic = new RestTopic();
        restTopic.initFrom(addResult.getResult());
        result.setResult(restTopic);
        result.setSuccessMessage("添加主题成功");
        
        return result;
    }
    
    /**
     * 更新播客主题
     * @param restObj 请求参数
     * @param request HTTP请求
     * @return 更新结果
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ExecuteResult<RestTopic> updateTopic(@RequestBody RestTopic restObj, HttpServletRequest request) {
        restObj.initHeaderValue(request);
        ExecuteResult<RestTopic> result = new ExecuteResult<>();
        
        // 权限校验
//        if (!checkAdminOrScoper(request, result)) {
//            return result;
//        }
        
        if (null == restObj.getTopicId()) {
            result.addErrorMessage("主题ID不能为空");
            return result;
        }
        
        PodTopic topic = restObj.toPodTopic();
        ExecuteResult<PodTopic> updateResult = podTopicService.updateTopic(topic);
        
        if (!updateResult.isSuccess()) {
            result.setErrorMessages(updateResult.getErrorMessages());
            return result;
        }
        
        if (null != updateResult.getResult()) {
            RestTopic restTopic = new RestTopic();
            restTopic.initFrom(updateResult.getResult());
            result.setResult(restTopic);
        }
        result.setSuccessMessage("更新主题成功");
        
        return result;
    }
    
    /**
     * 删除播客主题
     * @param restObj 请求参数
     * @param request HTTP请求
     * @return 删除结果
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ExecuteResult<String> deleteTopic(@RequestBody RestTopic restObj, HttpServletRequest request) {
        restObj.initHeaderValue(request);
        ExecuteResult<String> result = new ExecuteResult<>();
        
        // 权限校验
//        if (!checkAdminOrScoper(request, result)) {
//            return result;
//        }
        
        if (null == restObj.getTopicId()) {
            result.addErrorMessage("主题ID不能为空");
            return result;
        }
        
        ExecuteResult<String> deleteResult = podTopicService.deleteTopic(restObj.getTopicId());
        result.setResult(deleteResult.getResult());
        result.setErrorMessages(deleteResult.getErrorMessages());
        
        if (deleteResult.isSuccess()) {
            result.setSuccessMessage("删除主题成功");
        }
        
        return result;
    }
    
    /**
     * 更新主题顺序
     * @param restObj 请求参数
     * @param request HTTP请求
     * @return 更新结果
     */
    @RequestMapping(value = "/updateOrder", method = RequestMethod.POST)
    public ExecuteResult<String> updateTopicOrder(@RequestBody RestTopic restObj, HttpServletRequest request) {
        restObj.initHeaderValue(request);
        ExecuteResult<String> result = new ExecuteResult<>();
        
        // 权限校验
//        if (!checkAdminOrScoper(request, result)) {
//            return result;
//        }
        
        if (null == restObj.getTopicId()) {
            result.addErrorMessage("主题ID不能为空");
            return result;
        }
        
        if (null == restObj.getDisplayOrder()) {
            result.addErrorMessage("显示顺序不能为空");
            return result;
        }
        
        ExecuteResult<String> updateResult = podTopicService.updateTopicOrder(restObj.getTopicId(), restObj.getDisplayOrder());
        result.setResult(updateResult.getResult());
        result.setErrorMessages(updateResult.getErrorMessages());
        
        if (updateResult.isSuccess()) {
            result.setSuccessMessage("更新主题顺序成功");
        }
        
        return result;
    }
}
