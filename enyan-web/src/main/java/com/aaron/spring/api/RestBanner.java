package com.aaron.spring.api;

import com.aaron.spring.model.EnyanBanner;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/6/27
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestBanner extends RestBaseDTO{
	private static final long serialVersionUID = -3495089423511173053L;

	//@JSONField(name = "imgUrl")
	//@JsonProperty("imgUrl")
	private String imgUrl;

	//@J<PERSON><PERSON><PERSON>(name = "toUrl")
	//@JsonProperty("toUrl")
	private String toUrl;

	private String title;

	public void initFrom(EnyanBanner obj){
		this.title = obj.getDataName();
		this.imgUrl = obj.getDataImgUrl();
		this.toUrl = obj.getDataToUrl();
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getToUrl() {
		return toUrl;
	}

	public void setToUrl(String toUrl) {
		this.toUrl = toUrl;
	}

	public static  void main(String[] args){
		printInit("com.aaron.spring.api.RestBanner","EnyanBanner");
	}
}
