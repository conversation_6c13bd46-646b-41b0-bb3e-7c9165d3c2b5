package com.aaron.spring.api.v2.controller;

import com.aaron.mybatis.dao.pojo.Page;
import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v2.model.RestHighlight;
import com.aaron.spring.model.EnyanUserHighlights;
import com.aaron.spring.pojo.PageResult;
import com.aaron.spring.service.EnyanUserHighlightService;
import com.aaron.util.ExecuteResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2019-09-04
 * @Modified By:
 */
@RestController("RestHighlightControllerV2")
@RequestMapping("/api/v2/highlight")
public class RestHighlightController extends RestBaseController {
    private final Log logger = LogFactory.getLog(RestHighlightController.class);
    private static final int PAGE_SIZE = 30;
    @Resource
    private EnyanUserHighlightService enyanUserHighlightService;

    @RequestMapping(value = "/sync",method = RequestMethod.POST)
    public PageResult<RestHighlight> highlights(@RequestBody RestHighlight restHighlight){
        if (StringUtils.isBlank(restHighlight.getEmail()) || null == restHighlight.getUpdateTime()){
            PageResult<RestHighlight> page = new PageResult<>();
            page.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return page;
        }

        Page<EnyanUserHighlights> searchPage = new Page<>();
        searchPage.setCurrentPage(1);
        searchPage.setPageSize(PAGE_SIZE);

        EnyanUserHighlights enyanUserHighlights = new EnyanUserHighlights();
        enyanUserHighlights.setUserEmail(restHighlight.getEmail());
        enyanUserHighlights.setUpdateTime(restHighlight.getUpdateTime());

        Page<EnyanUserHighlights> highlightsPage = enyanUserHighlightService.findUserHighlightsGTUpdateTime(searchPage, enyanUserHighlights);

        PageResult<RestHighlight> page = new PageResult<>(1,-1,searchPage.getPageSize());
        //System.out.println("sync:"+restHighlight.getEmail()+";"+highlightsPage.getRecords().size());
        for (EnyanUserHighlights obj :highlightsPage.getRecords()){
            RestHighlight highlight = new RestHighlight();
            highlight.initFrom(obj);
            page.getResult().add(highlight);
        }
        return page;
    }

    @RequestMapping(value = "/upload",method = RequestMethod.POST)
    public ExecuteResult<Long> upload(@RequestBody RestHighlight restHighlight){
        ExecuteResult<Long> result = new ExecuteResult<>();
        if (StringUtils.isBlank(restHighlight.getEmail())){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        if (restHighlight.getList() == null || restHighlight.getList().isEmpty()){
            result.addErrorMessage(ReturnInfo.ERROR_DATA_NONE);
            return result;
        }
        //System.out.println("upload:"+restHighlight.getEmail()+","+restHighlight.getList().size());
        Long updateTime = System.currentTimeMillis();
        List<EnyanUserHighlights> list = new ArrayList<>();
        boolean canNotUpload = false; //不能正常上传
        for (RestHighlight tmp : restHighlight.getList()){
            if (StringUtils.isNotBlank(restHighlight.getContent()) && restHighlight.getContent().length() > 210){
                canNotUpload = true;
                break;
            }
            if (StringUtils.isNotBlank(restHighlight.getNoteForHighlight()) && restHighlight.getNoteForHighlight().length() > 2000){
                canNotUpload = true;
                break;
            }
            if (StringUtils.isNotBlank(restHighlight.getChapterName()) && restHighlight.getChapterName().length() > 100){
                canNotUpload = true;
                break;
            }
            if (StringUtils.isNotBlank(restHighlight.getRangy()) && restHighlight.getRangy().length() > 100){
                canNotUpload = true;
                break;
            }
            EnyanUserHighlights highlight = new EnyanUserHighlights();
            highlight.setUpdateTime(updateTime);
            highlight.setUserEmail(restHighlight.getEmail());
            highlight.setBookId(tmp.getBookId());
            highlight.setChapterName(tmp.getChapterName());
            highlight.setNoteForHighlight(tmp.getNoteForHighlight());
            highlight.setHighlightId(tmp.getHighlightId());
            highlight.setCreateTime(tmp.getCreateTime());
            highlight.setCurrentPage(tmp.getCurrentPage());
            highlight.setPageChapter(tmp.getPageChapter());
            highlight.setTotalPage(tmp.getTotalPage());
            highlight.setUserId(-1L);
            highlight.setIsDeleted(tmp.getIsDeleted());
            highlight.setContent(tmp.getContent());
            highlight.setRangy(tmp.getRangy());
            highlight.setPageProcess(tmp.getPageProcess());
            list.add(highlight);
            updateTime += 1;
        }
        if (canNotUpload){
            result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
            return result;
        }
        enyanUserHighlightService.addHighlights(list);
        //result.setResult(updateTime);
        result.setSuccessMessage(String.valueOf(updateTime));
        return result;
    }
}
