package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.CartDiscountInfo;
import com.aaron.spring.model.EnyanOrder;
import com.aaron.spring.model.ProductInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/5/18
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestOrderDiscount extends RestBaseDTO {
   private static final long serialVersionUID = 26354730532498647L;
   private Long discountId;

   private Integer cumulatePackage;

   private Integer cumulateDiscount;

   private Integer cumulatePackageMuti;

   private Integer cumulateDiscountMuti;

   private String discountTitle;

   private List<RestBook> bookList;

   public void initFrom(CartDiscountInfo obj){
       this.discountId = obj.getDiscountId();
       this.cumulatePackage = obj.getCumulatePackage();
       this.cumulateDiscount = obj.getCumulateDiscount();
       this.cumulatePackageMuti = obj.getCumulatePackageMuti();
       this.cumulateDiscountMuti = obj.getCumulateDiscountMuti();
       this.discountTitle = obj.getDiscountTitle();
       bookList = new ArrayList<>();
       if (null != obj.getProductInfoList()) {
           for (ProductInfo productInfo : obj.getProductInfoList()){
               RestBook book = new RestBook();
               book.initFromProduct(productInfo);
               bookList.add(book);
           }
       }
   }

   public Long getDiscountId() {
    return discountId;
   }

   public void setDiscountId(Long discountId) {
    this.discountId = discountId;
   }

   public Integer getCumulatePackage() {
    return cumulatePackage;
   }

   public void setCumulatePackage(Integer cumulatePackage) {
    this.cumulatePackage = cumulatePackage;
   }

   public Integer getCumulateDiscount() {
    return cumulateDiscount;
   }

   public void setCumulateDiscount(Integer cumulateDiscount) {
    this.cumulateDiscount = cumulateDiscount;
   }

   public Integer getCumulatePackageMuti() {
    return cumulatePackageMuti;
   }

   public void setCumulatePackageMuti(Integer cumulatePackageMuti) {
    this.cumulatePackageMuti = cumulatePackageMuti;
   }

   public Integer getCumulateDiscountMuti() {
    return cumulateDiscountMuti;
   }

   public void setCumulateDiscountMuti(Integer cumulateDiscountMuti) {
    this.cumulateDiscountMuti = cumulateDiscountMuti;
   }

   public String getDiscountTitle() {
    return discountTitle;
   }

   public void setDiscountTitle(String discountTitle) {
    this.discountTitle = discountTitle;
   }

    public List<RestBook> getBookList() {
        return bookList;
    }

    public void setBookList(List<RestBook> bookList) {
        this.bookList = bookList;
    }
}
