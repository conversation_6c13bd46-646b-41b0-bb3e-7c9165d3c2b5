package com.aaron.spring.api;

import com.aaron.spring.api.v4.model.RestBook;
import com.aaron.spring.api.v4.model.RestDailyWords;
import com.aaron.spring.api.v4.model.RestRent;
import com.aaron.spring.model.EnyanBook;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-06-10
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestConfig  extends RestBaseDTO implements Cloneable{
    @Serial
    private static final long serialVersionUID = 1468340533018562912L;
    /**
     * 灵修材料最新更新时间
     * */
    private Long  spiritTime;//灵修材料最新更新时间
    /**
     * 最新通知最新更新时间
     * */
    private Long  latestNoticeTime;//
    private List<AdModel> adList = new ArrayList<>();

    private List<AdModel> adWebList = new ArrayList<>();

    private SpiritImgModel spiritImg;

    /**
     *返回的书籍信息
     * */
    private List<RestBook> books;

    /**
     *先租后买的列表
     * */
    private List<RestRent> rents;

    /**
     *先租后买的自动列表
     * */
    @JsonIgnore
    private List<RestRent> toBuyRentsAuto;

    /**
     *先租后买的手动列表
     * */
    @JsonIgnore
    private List<RestRent> toBuyRentsManual;
    /**
     *是否只要获取配置信息
     * */
    private Boolean onlyConfig;

    /**
     *首页的banner
     * */
    @JsonIgnore
    private List<RestBanner> indexBanners;//首页的banner

    /**
     *首页中部banner
     * */
    @JsonIgnore
    private List<RestBanner> middleBanners;//首页中部banner

    /**
     *读书会banner
     * */
    @JsonIgnore
    private List<RestBanner> readBanners;//读书会banner

    /**
     *读书会
     * */
    @JsonIgnore
    private List<RestBanner> readings;//读书会

    /**
     *从今天开始计算的金句
     * */
    @JsonIgnore
    private List<RestDailyWords> dailyWords;//金句

    /**
     *从今天开始计算的金句总数
     * */
    @JsonIgnore
    private Long countOfDailyWords;

    private List<NoticeMsg> noticeMsgList;//通知消息的列表

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public Long getSpiritTime() {
        return spiritTime;
    }

    public void setSpiritTime(Long spiritTime) {
        this.spiritTime = spiritTime;
    }

    public Long getLatestNoticeTime() {
        return latestNoticeTime;
    }

    public void setLatestNoticeTime(Long latestNoticeTime) {
        this.latestNoticeTime = latestNoticeTime;
    }

    public List<AdModel> getAdList() {
        return adList;
    }

    public void setAdList(List<AdModel> adList) {
        this.adList = adList;
    }

    public SpiritImgModel getSpiritImg() {
        return spiritImg;
    }

    public void setSpiritImg(SpiritImgModel spiritImg) {
        this.spiritImg = spiritImg;
    }

    public List<RestBook> getBooks() {
        return books;
    }

    public void setBooks(List<RestBook> books) {
        this.books = books;
    }

    public Boolean getOnlyConfig() {
        return onlyConfig;
    }

    public void setOnlyConfig(Boolean onlyConfig) {
        this.onlyConfig = onlyConfig;
    }

    public List<RestBanner> getIndexBanners() {
        return indexBanners;
    }

    public void setIndexBanners(List<RestBanner> indexBanners) {
        this.indexBanners = indexBanners;
    }

    public List<RestBanner> getMiddleBanners() {
        return middleBanners;
    }

    public void setMiddleBanners(List<RestBanner> middleBanners) {
        this.middleBanners = middleBanners;
    }

    public List<RestBanner> getReadBanners() {
        return readBanners;
    }

    public void setReadBanners(List<RestBanner> readBanners) {
        this.readBanners = readBanners;
    }


    public List<RestBanner> getReadings() {
        return readings;
    }

    public void setReadings(List<RestBanner> readings) {
        this.readings = readings;
    }

    public List<RestDailyWords> getDailyWords() {
        return dailyWords;
    }

    public void setDailyWords(List<RestDailyWords> dailyWords) {
        this.dailyWords = dailyWords;
    }

    public Long getCountOfDailyWords() {
        return countOfDailyWords;
    }

    public void setCountOfDailyWords(Long countOfDailyWords) {
        this.countOfDailyWords = countOfDailyWords;
    }

    public List<RestRent> getRents() {
        return rents;
    }

    public void setRents(List<RestRent> rents) {
        this.rents = rents;
    }

    public List<RestRent> getToBuyRentsAuto() {
        return toBuyRentsAuto;
    }

    public void setToBuyRentsAuto(List<RestRent> toBuyRentsAuto) {
        this.toBuyRentsAuto = toBuyRentsAuto;
    }

    public List<RestRent> getToBuyRentsManual() {
        return toBuyRentsManual;
    }

    public void setToBuyRentsManual(List<RestRent> toBuyRentsManual) {
        this.toBuyRentsManual = toBuyRentsManual;
    }

    public List<AdModel> getAdWebList() {
        return adWebList;
    }

    public void setAdWebList(List<AdModel> adWebList) {
        this.adWebList = adWebList;
    }

    public List<NoticeMsg> getNoticeMsgList() {
        return noticeMsgList;
    }

    public void setNoticeMsgList(List<NoticeMsg> noticeMsgList) {
        this.noticeMsgList = noticeMsgList;
    }

    public static  void main(String[] args){
        String json = "{\"page\":0,\"adList\":[{\"skipBtnType\":3,\"duration\":4,\"animationType\":0,\"imgUrl\":\"https://ehome.endao.co/book_image/detail/1591689891303.png\",\"toUrl\":\"https://ebook.endao.co\",\"endDate\":null}]}";
        //{"adList":"[{"animationType":0,"duration":4,"endDate":0,"imgUrl":"https://ehome.endao.co/book_image/detail/1591689891303.png","skipBtnType":3,"toUrl":"https://ebook.endao.co"}]\"}
        //{"page":0,"adList":[{"skipBtnType":3,"duration":4,"animationType":0,"imgUrl":"https://ehome.endao.co/book_image/detail/1591689891303.png","toUrl":"https://ebook.endao.co","endDate":null}]}
    }
}