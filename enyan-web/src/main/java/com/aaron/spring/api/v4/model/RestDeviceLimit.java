package com.aaron.spring.api.v4.model;

import com.aaron.spring.api.RestBaseDTO;
import com.aaron.spring.model.DeviceLimit;
import com.aaron.spring.model.EnyanBlog;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2025-03-17
 * @Modified By:
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestDeviceLimit extends RestBaseDTO {
   @Serial
   private static final long serialVersionUID = 7722304587991089346L;
   private String deviceCode;
   private String deviceName;
   private String deviceType;

   public void initFrom(DeviceLimit obj){
      this.deviceCode = obj.getDevice();
      this.deviceName = obj.getDeviceName();
      this.deviceType = obj.getDeviceType();
   }

 public String getDeviceCode() {
  return deviceCode;
 }

 public void setDeviceCode(String deviceCode) {
  this.deviceCode = deviceCode;
 }

 public String getDeviceName() {
  return deviceName;
 }

 public void setDeviceName(String deviceName) {
  this.deviceName = deviceName;
 }

 public String getDeviceType() {
  return deviceType;
 }

 public void setDeviceType(String deviceType) {
  this.deviceType = deviceType;
 }
}
