package com.aaron.spring.api.v4.controller;

import com.aaron.spring.api.RestBaseController;
import com.aaron.spring.api.v4.model.RestBook;
import com.aaron.spring.api.v4.model.RestFeedback;
import com.aaron.spring.api.v4.model.RestRent;
import com.aaron.spring.model.EnyanFeedback;
import com.aaron.spring.service.EnyanBookService;
import com.aaron.spring.service.EnyanFeedbackService;
import com.aaron.util.ExecuteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2023/2/8
 * @Modified By:
 */
@Slf4j
@RestController("RestFeedbackControllerV4")
@RequestMapping(path = {"/api/v4/feedback","/front/v4/feedback"})
public class RestFeedbackController  extends RestBaseController {
	@Resource
	private EnyanFeedbackService enyanFeedbackService;
	@RequestMapping(value = "/add",method = RequestMethod.POST)
	public ExecuteResult<RestFeedback> add(@RequestBody RestFeedback restObj, HttpServletRequest request){
		restObj.initHeaderValue(request);
		ExecuteResult<RestFeedback> result = new ExecuteResult<>();
		if (StringUtils.isBlank(restObj.getContent()) || null == restObj.getType() 
				    || StringUtils.isBlank(restObj.getFeedEmail()) || restObj.getContent().length() > 500){
			result.addErrorMessage(ReturnInfo.ERROR_PARAM_INVALID);
			return result;
		}
		EnyanFeedback feedback = new EnyanFeedback();
		feedback.setContent(restObj.getContent());
		feedback.setFeedEmail(restObj.getFeedEmail());
		feedback.setDeviceFrom(restObj.getDeviceFrom());
		feedback.setDeviceVersion(restObj.getDeviceVersion());
		feedback.setType(restObj.getType());
		feedback.setIsDeleted(0);
		feedback.setDoneType(0);
		feedback.setCreateAt(new Date());
		enyanFeedbackService.addRecord(feedback);
		return result;
	}
}
