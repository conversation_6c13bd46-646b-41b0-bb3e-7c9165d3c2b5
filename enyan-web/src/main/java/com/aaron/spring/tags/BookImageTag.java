package com.aaron.spring.tags;

import com.aaron.common.NameAndValue;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBook;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.NoSuchMessageException;
import org.springframework.web.servlet.tags.HtmlEscapingAwareTag;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import java.io.IOException;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/4/9
 * @Modified By:
 */
public class BookImageTag extends HtmlEscapingAwareTag {


    private String imageName;

    private String scope;//属于哪个范围（default,sample,detail,pdf,epub）

    private String imageType;

    private String bookId;

    @Override
    protected int doStartTagInternal() throws Exception {
        return EVAL_BODY_INCLUDE;
    }

    @Override
    public int doEndTag() throws JspException {
        try {
            // Resolve the unescaped message.
            String msg = resolveMessage();

            // HTML and/or JavaScript escape, if demanded.
            msg = htmlEscape(msg);
            writeMessage(msg);

        }
        catch (IOException ex) {
            throw new JspTagException(ex.getMessage(), ex);
        }
        catch (NoSuchMessageException ex) {
            //throw new JspTagException(getNoSuchMessageExceptionDescription(ex));
        }
        return EVAL_PAGE;
    }
    protected String resolveMessage() throws JspException, NoSuchMessageException {


        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(Constant.IMG_SERVER);
        stringBuffer.append("book_image/");
        if ("sample".equals(scope)){
            stringBuffer.append("sample/");
        }else if ("detail".equals(scope)){
            stringBuffer.append("detail/");
        }else if ("pdf".equals(scope)){
            stringBuffer.append("pdf/");
        }else if ("epub".equals(scope)){
            stringBuffer.append("epub/");
        }else if ("bookName".equals(scope)){
            if (StringUtils.isBlank(bookId)){
                return "";
            }
            for (NameAndValue nameAndValue : Constant.booksList){
                if (nameAndValue.getValue().equals(bookId)){
                    return nameAndValue.getName();
                }
            }
            return "";
        }
        stringBuffer.append(imageName);
        return stringBuffer.toString();

    }


    protected void writeMessage(String msg) throws IOException {
        pageContext.getOut().write(String.valueOf(msg));
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
        if ("0".equals(imageType)){

        }else if ("1".equals(imageType)){

        }
    }

    public String getBookId() {
        return bookId;
    }

    public void setBookId(String bookId) {
        this.bookId = bookId;
    }
}
