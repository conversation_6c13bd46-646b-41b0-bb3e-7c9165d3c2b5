package com.aaron.spring.tags;

import com.aaron.common.NameAndValue;
import com.aaron.common.NameAndValueDTO;
import com.aaron.spring.common.Constant;
import com.aaron.spring.model.EnyanBook;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.NoSuchMessageException;
import org.springframework.web.servlet.tags.HtmlEscapingAwareTag;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import java.io.IOException;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/4/9
 * @Modified By:
 */
public class BookWebTag extends HtmlEscapingAwareTag {
    private EnyanBook enyanBook;

    private String infoType;

    @Override
    protected int doStartTagInternal() throws Exception {
        return EVAL_BODY_INCLUDE;
    }

    @Override
    public int doEndTag() throws JspException {
        try {
            // Resolve the unescaped message.
            String msg = resolveMessage();

            // HTML and/or JavaScript escape, if demanded.
            msg = htmlEscape(msg);
            writeMessage(msg);

        }
        catch (IOException ex) {
            throw new JspTagException(ex.getMessage(), ex);
        }
        catch (NoSuchMessageException ex) {
            //throw new JspTagException(getNoSuchMessageExceptionDescription(ex));
        }
        return EVAL_PAGE;
    }
    protected String resolveMessage() throws JspException, NoSuchMessageException {
        if (null == enyanBook.getBookWebInfo()){
            return "";
        }
        if ("paper".equals(this.getInfoType())){
            if (null == enyanBook.getBookWebInfo().getSalePapers()){
                return "";
            }
            StringBuffer buffer = new StringBuffer();
            /*for (int i = 0; i < nameAndValueDTO.getValues().size(); i++) {
                NameAndValue valueAndName = nameAndValueDTO.getValues().get(i);
                if (i!=0){
                    buffer.append("|");
                }
                buffer.append(valueAndName.getName());
            }*/
            for (NameAndValue nameAndValue:enyanBook.getBookWebInfo().getSalePapers()){
                buffer.append("<li><a class=\"dropdown-item\" href=\"");
                buffer.append(nameAndValue.getValue());
                buffer.append("\" target=\"_blank\">");
                if ("zh_CN".equals(getRequestContext().getLocale().toString())){
                    buffer.append(StringUtils.isNotBlank(nameAndValue.getName())?nameAndValue.getName():"");
                }else {
                    buffer.append(StringUtils.isNotBlank(nameAndValue.getOther())?nameAndValue.getOther():"");
                }
                buffer.append("</a></li>");
            }
            return buffer.toString();
        }
        // All we have is a specified literal text.
        return "";
    }
    protected void writeMessage(String msg) throws IOException {
        pageContext.getOut().write(String.valueOf(msg));
    }

    public String getInfoType() {
        return infoType;
    }

    public void setInfoType(String infoType) {
        this.infoType = infoType;
    }

    public EnyanBook getEnyanBook() {
        return enyanBook;
    }

    public void setEnyanBook(EnyanBook enyanBook) {
        this.enyanBook = enyanBook;
    }
}
