package com.aaron.spring.tags;

import com.aaron.common.NameAndValue;
import com.aaron.spring.common.AaronHtmlUtils;
import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.aaron.spring.model.EnyanBook;
import com.aaron.spring.model.EnyanRent;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.NoSuchMessageException;
import org.springframework.web.servlet.tags.HtmlEscapingAwareTag;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/4/9
 * @Modified By:
 */
public class RentTag extends HtmlEscapingAwareTag {
    private String infoType;
    private EnyanRent enyanRent;
    private boolean trimEmpty;

    @Override
    protected int doStartTagInternal() throws Exception {
        return EVAL_BODY_INCLUDE;
    }

    @Override
    public int doEndTag() throws JspException {
        try {
            // Resolve the unescaped message.
            String msg = resolveMessage();

            // HTML and/or JavaScript escape, if demanded.
            msg = htmlEscape(msg);
            writeMessage(msg);

        }
        catch (IOException ex) {
            throw new JspTagException(ex.getMessage(), ex);
        }
        catch (NoSuchMessageException ex) {
            //throw new JspTagException(getNoSuchMessageExceptionDescription(ex));
        }
        return EVAL_PAGE;
    }
    protected String resolveMessage() throws JspException, NoSuchMessageException {
        if (StringUtils.isBlank(infoType)){
            return "";
        }

        if ("rentName".equals(infoType)){
            return BookUtil.getBookNameInRentType(enyanRent.getRentType(),enyanRent.getRentLang());
        }
        if ("rentStatus".equals(infoType)){
            Date current = new Date();
            StringBuffer buffer = new StringBuffer();
            if (enyanRent.getIsValid() == 1) {
                AaronHtmlUtils.addGreenMsg(buffer,"正常阅读 ");
            }
            if (enyanRent.getRentStatus() == EBookConstant.RentStatus.ON){
                if (enyanRent.getTotalMonths() == 36){
                    AaronHtmlUtils.addGreenMsg(buffer,"已订阅36个月 ");
                }else{
                    if (null == enyanRent.getExpiredAt()){
                        return "结束日期异常";
                    }
                    Date expiredInGrace = DateUtils.addDays(enyanRent.getExpiredAt(),3);//3天宽限期
                    if (current.after(enyanRent.getExpiredAt()) && current.before(expiredInGrace)){
                        AaronHtmlUtils.addRedMsg(buffer,"已到期(宽限期) ");
                    }
                }
            }else if (enyanRent.getRentStatus() == EBookConstant.RentStatus.expired){//到期
                if (null == enyanRent.getExpiredAt()){
                    return "结束日期异常";
                }
                if (current.after(enyanRent.getExpiredAt())){//确实已经过期，不是提前退订
                    Date leaveAtGrace = DateUtils.addMonths(enyanRent.getLeaveAt(),1);//1个月优惠购买
                    if (current.before(leaveAtGrace) && enyanRent.getLeaveBuy() == 0){//在优惠期区间之内,并且没有使用过优惠
                        AaronHtmlUtils.addRedMsg(buffer,"已到期(优惠购买期) ");
                    }else {
                        AaronHtmlUtils.addRedMsg(buffer,"已到期");
                    }
                }
            }else if (enyanRent.getRentStatus() == EBookConstant.RentStatus.leave){//直接退订
                if (null == enyanRent.getExpiredAt()){
                    return "结束日期异常";
                }
                if (current.after(enyanRent.getExpiredAt())){//确实已经过期，不是提前退订
                    Date leaveAtGrace = DateUtils.addMonths(enyanRent.getLeaveAt(),1);//1个月优惠购买
                    if (current.before(leaveAtGrace) && enyanRent.getLeaveBuy() == 0){//在优惠期区间之内,并且没有使用过优惠
                        AaronHtmlUtils.addRedMsg(buffer,"已到期(优惠购买期) ");
                    }else {
                        AaronHtmlUtils.addRedMsg(buffer,"已退订");
                    }
                }
            }
            return buffer.toString();
        }

        // All we have is a specified literal text.
        return "";
    }
    protected void writeMessage(String msg) throws IOException {
        pageContext.getOut().write(String.valueOf(msg));
    }



    public String getInfoType() {
        return infoType;
    }

    public void setInfoType(String infoType) {
        this.infoType = infoType;
    }

    public EnyanRent getEnyanRent() {
        return enyanRent;
    }

    public void setEnyanRent(EnyanRent enyanRent) {
        this.enyanRent = enyanRent;
    }

    public boolean isTrimEmpty() {
        return trimEmpty;
    }

    public void setTrimEmpty(boolean trimEmpty) {
        this.trimEmpty = trimEmpty;
    }
}
