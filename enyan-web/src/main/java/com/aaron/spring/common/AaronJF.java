package com.aaron.spring.common;

import org.nlpcn.commons.lang.jianfan.JianFan;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/9/24
 * @Modified By:
 */
public class AaronJF {

	/**
	 * 简体转繁体
	 * @param str
	 * @return
	 */
	public static String j2f(String str) {
		return JianFan.j2f(str) ;
	}

	/**
	 * 繁体转简体
	 * @param str
	 * @return
	 */
	public static String f2j(String str) {
		return JianFan.f2j(str) ;
	}

	public static  void main(String[] args){
		String word = "Frank Houghton";
		String toWord = AaronJF.j2f(word);
		System.out.println(toWord);
		System.out.println(JianFan.j2f(word));
	}
}
