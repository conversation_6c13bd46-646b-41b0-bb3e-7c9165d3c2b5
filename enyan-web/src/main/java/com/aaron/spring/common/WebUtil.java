package com.aaron.spring.common;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/5/26
 * @Modified By:
 */
public class WebUtil {
	public static final String BASE_PATH_PRODUCT = "https://e.bookapp.cc/";
	public static final String BASE_PATH_TEST = "https://ebookstore.endao.co/";
	public static final String BASE_PATH_LOCAL = "http://localhost:8080/";//http://localhost:8080/

	public static final String BASE_PATH_PRODUCT_NO_SUFFIX = "";
	public static final String BASE_PATH_TEST_NO_SUFFIX = "https://ebookstore.endao.co";
	public static final String BASE_PATH_LOCAL_NO_SUFFIX = "http://localhost:8080";

	public static final String BASE_PATH_PRODUCT_LOGIN = "/login";
	public static final String BASE_PATH_TEST_LOGIN = "https://ebookstore.endao.co/login";
	public static final String BASE_PATH_LOCAL_LOGIN = "http://localhost:8080/login";//http://localhost:8080/

	public static final String BASE_PATH_PRODUCT_REG = "/reg";
	public static final String BASE_PATH_TEST_REG = "https://ebookstore.endao.co/reg";
	public static final String BASE_PATH_LOCAL_REG = "http://localhost:8080/reg";//http://localhost:8080/

	/**
	 * <p>用于区分是否是阅读的域名</p>
	 **/
	public static final String BASE_PATH_READ_NAME = "read.endao.co";//ebook.tti.cloud, read.endao.co

	/**
	 * <p>获取server的basePath，是具体的域名网址信息，有“/”</p>
	 * @param
	 * @return java.lang.String
	 * @since : 2021/5/26
	 **/
	public static String getBasePath(){
		if (Constant.IS_PRODUCT){
			return BASE_PATH_PRODUCT;
		}
		if (Constant.IS_TEST){
			return BASE_PATH_PRODUCT;
		}
		return BASE_PATH_LOCAL;
	}
	/**
	 * <p>获取server的basePath，是具体的域名网址信息，有“/”，如果是测试服务器则直接转向到 正式服务器，其他都转向到“/”</p>
	 * @param
	 * @return java.lang.String
	 * @since : 2021/5/26
	 **/
	public static String getBaseBuyPath(){
		if (Constant.IS_TEST){
			return BASE_PATH_PRODUCT;
		}
		return "/";
	}
	/**
	 * <p>获取Email的email地址</p>
	 * @param
	 * @return java.lang.String
	 * @since : 2024-04-24
	 **/
	public static String getEmailBasePath(){
		if (Constant.IS_PRODUCT){
			return BASE_PATH_PRODUCT;
		}
		if (Constant.IS_TEST){
			return BASE_PATH_PRODUCT;
		}
		return BASE_PATH_LOCAL;
	}

	/**
	 * <p>没有后缀，目前正式服务器只是返回“”</p>
	 * @param
	 * @return java.lang.String
	 * @since : 2022/7/14
	 **/
	public static String getBasePathNoSuffix(){
		if (Constant.IS_PRODUCT){
			return BASE_PATH_PRODUCT_NO_SUFFIX;
		}
		if (Constant.IS_TEST){
			return BASE_PATH_PRODUCT_NO_SUFFIX;
		}
		return BASE_PATH_LOCAL_NO_SUFFIX;
	}

	public static String getBasePathLogin(){
		if (Constant.IS_PRODUCT){
			return BASE_PATH_PRODUCT_LOGIN;
		}
		if (Constant.IS_TEST){
			return BASE_PATH_PRODUCT_LOGIN;
		}
		return BASE_PATH_LOCAL_LOGIN;
	}

	public static String getBasePathReg(){
		if (Constant.IS_PRODUCT){
			return BASE_PATH_PRODUCT_REG;
		}
		if (Constant.IS_TEST){
			return BASE_PATH_PRODUCT_REG;
		}
		return BASE_PATH_LOCAL_REG;
	}
}
