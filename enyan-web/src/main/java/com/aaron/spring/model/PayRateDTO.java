package com.aaron.spring.model;

import java.io.Serial;
import java.io.Serializable;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-01-17
 * @Modified By:
 */
public class PayRateDTO implements Serializable {

    private static final long serialVersionUID = 6019548222293262368L;
    private  int timestamp;
    private  boolean success;
    private  String base;
    private  String date;
    private  Rate rates;

    public int getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(int timestamp) {
        this.timestamp = timestamp;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getBase() {
        return base;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Rate getRates() {
        return rates;
    }

    public void setRates(Rate rates) {
        this.rates = rates;
    }
}

class Rate implements Serializable{
    @Serial
    private static final long serialVersionUID = 7583628119920889691L;
    private  double USD;
    private  double MXN;
    private  double SGD;
    private  double MYR;
    private  double HKD;
    private  double TWD;
    private  double CAD;
    private  double IDR;
    private  double JPY;
    private  double CNY;
    private  double EUR;
    private  double AUD;
    private  double PLN;

    private  double GBP;
    private  double KRW;
    private  double THB;
    private  double NZD;
    private  double BRL;

    public double getUSD() {
        return USD;
    }

    public void setUSD(double USD) {
        this.USD = USD;
    }

    public double getMXN() {
        return MXN;
    }

    public void setMXN(double MXN) {
        this.MXN = MXN;
    }

    public double getSGD() {
        return SGD;
    }

    public void setSGD(double SGD) {
        this.SGD = SGD;
    }

    public double getMYR() {
        return MYR;
    }

    public void setMYR(double MYR) {
        this.MYR = MYR;
    }

    public double getHKD() {
        return HKD;
    }

    public void setHKD(double HKD) {
        this.HKD = HKD;
    }

    public double getTWD() {
        return TWD;
    }

    public void setTWD(double TWD) {
        this.TWD = TWD;
    }

    public double getCAD() {
        return CAD;
    }

    public void setCAD(double CAD) {
        this.CAD = CAD;
    }

    public double getIDR() {
        return IDR;
    }

    public void setIDR(double IDR) {
        this.IDR = IDR;
    }

    public double getJPY() {
        return JPY;
    }

    public void setJPY(double JPY) {
        this.JPY = JPY;
    }

    public double getCNY() {
        return CNY;
    }

    public void setCNY(double CNY) {
        this.CNY = CNY;
    }

    public double getEUR() {
        return EUR;
    }

    public void setEUR(double EUR) {
        this.EUR = EUR;
    }

    public double getAUD() {
        return AUD;
    }

    public void setAUD(double AUD) {
        this.AUD = AUD;
    }

    public double getPLN() {
        return PLN;
    }

    public void setPLN(double PLN) {
        this.PLN = PLN;
    }

    public double getGBP() {
        return GBP;
    }

    public void setGBP(double GBP) {
        this.GBP = GBP;
    }

    public double getKRW() {
        return KRW;
    }

    public void setKRW(double KRW) {
        this.KRW = KRW;
    }

    public double getTHB() {
        return THB;
    }

    public void setTHB(double THB) {
        this.THB = THB;
    }

    public double getNZD() {
        return NZD;
    }

    public void setNZD(double NZD) {
        this.NZD = NZD;
    }

    public double getBRL() {
        return BRL;
    }

    public void setBRL(double BRL) {
        this.BRL = BRL;
    }
}