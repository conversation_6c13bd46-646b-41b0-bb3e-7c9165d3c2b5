package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.util.ArrayList;
import java.util.List;

public class EmailHistoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EmailHistoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andEmailIdIsNull() {
            addCriterion("email_id is null");
            return (Criteria) this;
        }

        public Criteria andEmailIdIsNotNull() {
            addCriterion("email_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmailIdEqualTo(Long value) {
            addCriterion("email_id =", value, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdNotEqualTo(Long value) {
            addCriterion("email_id <>", value, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdGreaterThan(Long value) {
            addCriterion("email_id >", value, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("email_id >=", value, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdLessThan(Long value) {
            addCriterion("email_id <", value, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdLessThanOrEqualTo(Long value) {
            addCriterion("email_id <=", value, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdIn(List<Long> values) {
            addCriterion("email_id in", values, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdNotIn(List<Long> values) {
            addCriterion("email_id not in", values, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdBetween(Long value1, Long value2) {
            addCriterion("email_id between", value1, value2, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIdNotBetween(Long value1, Long value2) {
            addCriterion("email_id not between", value1, value2, "emailId");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailTypeIsNull() {
            addCriterion("email_type is null");
            return (Criteria) this;
        }

        public Criteria andEmailTypeIsNotNull() {
            addCriterion("email_type is not null");
            return (Criteria) this;
        }

        public Criteria andEmailTypeEqualTo(Integer value) {
            addCriterion("email_type =", value, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeNotEqualTo(Integer value) {
            addCriterion("email_type <>", value, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeGreaterThan(Integer value) {
            addCriterion("email_type >", value, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("email_type >=", value, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeLessThan(Integer value) {
            addCriterion("email_type <", value, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeLessThanOrEqualTo(Integer value) {
            addCriterion("email_type <=", value, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeIn(List<Integer> values) {
            addCriterion("email_type in", values, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeNotIn(List<Integer> values) {
            addCriterion("email_type not in", values, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeBetween(Integer value1, Integer value2) {
            addCriterion("email_type between", value1, value2, "emailType");
            return (Criteria) this;
        }

        public Criteria andEmailTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("email_type not between", value1, value2, "emailType");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesIsNull() {
            addCriterion("repeat_times is null");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesIsNotNull() {
            addCriterion("repeat_times is not null");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesEqualTo(Integer value) {
            addCriterion("repeat_times =", value, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesNotEqualTo(Integer value) {
            addCriterion("repeat_times <>", value, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesGreaterThan(Integer value) {
            addCriterion("repeat_times >", value, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("repeat_times >=", value, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesLessThan(Integer value) {
            addCriterion("repeat_times <", value, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesLessThanOrEqualTo(Integer value) {
            addCriterion("repeat_times <=", value, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesIn(List<Integer> values) {
            addCriterion("repeat_times in", values, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesNotIn(List<Integer> values) {
            addCriterion("repeat_times not in", values, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesBetween(Integer value1, Integer value2) {
            addCriterion("repeat_times between", value1, value2, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andRepeatTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("repeat_times not between", value1, value2, "repeatTimes");
            return (Criteria) this;
        }

        public Criteria andSendAtIsNull() {
            addCriterion("send_at is null");
            return (Criteria) this;
        }

        public Criteria andSendAtIsNotNull() {
            addCriterion("send_at is not null");
            return (Criteria) this;
        }

        public Criteria andSendAtEqualTo(Long value) {
            addCriterion("send_at =", value, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtNotEqualTo(Long value) {
            addCriterion("send_at <>", value, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtGreaterThan(Long value) {
            addCriterion("send_at >", value, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtGreaterThanOrEqualTo(Long value) {
            addCriterion("send_at >=", value, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtLessThan(Long value) {
            addCriterion("send_at <", value, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtLessThanOrEqualTo(Long value) {
            addCriterion("send_at <=", value, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtIn(List<Long> values) {
            addCriterion("send_at in", values, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtNotIn(List<Long> values) {
            addCriterion("send_at not in", values, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtBetween(Long value1, Long value2) {
            addCriterion("send_at between", value1, value2, "sendAt");
            return (Criteria) this;
        }

        public Criteria andSendAtNotBetween(Long value1, Long value2) {
            addCriterion("send_at not between", value1, value2, "sendAt");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayIsNull() {
            addCriterion("year_month_day is null");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayIsNotNull() {
            addCriterion("year_month_day is not null");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayEqualTo(Integer value) {
            addCriterion("year_month_day =", value, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayNotEqualTo(Integer value) {
            addCriterion("year_month_day <>", value, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayGreaterThan(Integer value) {
            addCriterion("year_month_day >", value, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("year_month_day >=", value, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayLessThan(Integer value) {
            addCriterion("year_month_day <", value, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayLessThanOrEqualTo(Integer value) {
            addCriterion("year_month_day <=", value, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayIn(List<Integer> values) {
            addCriterion("year_month_day in", values, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayNotIn(List<Integer> values) {
            addCriterion("year_month_day not in", values, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayBetween(Integer value1, Integer value2) {
            addCriterion("year_month_day between", value1, value2, "yearMonthDay");
            return (Criteria) this;
        }

        public Criteria andYearMonthDayNotBetween(Integer value1, Integer value2) {
            addCriterion("year_month_day not between", value1, value2, "yearMonthDay");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}