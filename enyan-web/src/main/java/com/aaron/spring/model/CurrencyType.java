package com.aaron.spring.model;

import com.aaron.spring.common.Constant;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 *
 *
 * @Date: Created in  2020-01-16
 * 人民币        ¥CNY	  RMB¥
 * 新台币	    $TWD	  NT$
 * 港元  	    $HKD	  HK$
 * 美元 	    $USD	  US$
 * 马来西亚林吉特 RM MYR   RM
 * 印尼盾       Rp IDR	  Rp
 * 加拿大元	    $CAD	  CA$
 * 新加坡元  	$SGD	  SG$
 * 澳大利亚元	$AUD	  AU$
 * 欧元	        €EUR	   €
 * 英国，韩国，日本，泰国，纽西兰，巴西
 *
 * 英国 GBP £
 * 韩国 KRW ₩
 * 日本 JPY ¥ 日元
 * 泰国 THB ฿ 泰铢
 * 新西兰 NZD $ 新西兰元
 * 巴西 BRL R$ 巴西雷亚尔
 * @Author: Aaron Hao
 * @Modified By:
 */
public enum CurrencyType {
    HKD("HKD(HK$)","HK$","港元","HKD"),
    CNY("CNY(¥)","¥","人民币","CNY"),
    TWD("TWD(NT$)","NT$","新台币","TWD"),
    USD("USD(US$)","US$","美元","USD"),
    CAD("CAD(CA$)","CA$","加拿大元","CAD"),
    EUR("EUR(€)","€","欧元","EUR"),
    AUD("AUD(AU$)","AU$","澳大利亚元","AUD"),
    SGD("SGD(SG$)","SG$","新加坡元","SGD"),
    RM_MYR("MYR(RM)","RM","马来西亚林吉特","MYR"),
    Rp_IDR("IDR(Rp)","Rp","印尼盾","IDR"),
    GBP("GBP(£)","£","英镑","GBP"),
    KRW("KRW(₩)","₩","韩圆","KRW"),
    JPY("JPY(円)","円","日元","JPY"),
    THB("THB(฿)","฿","泰铢","THB"),
    NZD("NZD(NZ$)","NZ$","新西兰元","NZD"),
    BRL("BRL(R$)","R$","巴西雷亚尔","BRL");

    /**
     *例如：HKD(HK$)
     * */
    private String headerName;
    /**
     *例如：HK$
     * */
    private String priceName;
    private String description;
    /**
     *例如：HKD
     * */
    private String currency;
    private BigDecimal value;

    CurrencyType(String headerName, String priceName, String description, String currency) {
        this.headerName = headerName;
        this.priceName = priceName;
        this.description = description;
        this.currency = currency;
    }

    public static CurrencyType getPriceNameByHeaderName(String headerName){
        if (StringUtils.isEmpty(headerName)){
            return CNY;
        }
        for (CurrencyType currencyType: CurrencyType.values()){
            if (currencyType.getHeaderName().equals(headerName)){
                return currencyType;
            }
        }
        return CNY;
    }

    public static CurrencyType getPriceNameByCurrency(String currency){
        return getCurrencyTypeByCurrency(currency);
    }

    public static CurrencyType getCurrencyTypeByCurrency(String currency){
        if (StringUtils.isEmpty(currency)){
            return CNY;
        }
        for (CurrencyType currencyType: CurrencyType.values()){
            if (currencyType.getCurrency().equals(currency)){
                return currencyType;
            }
        }
        return CNY;
    }

    /**
     * <p>根据货币类型及金钱获取价格信息</p>
     * @param price
     * @return java.lang.String
     * @since : 2021/5/20
     **/
    public BigDecimal getCurrencyByPriceValue(BigDecimal price){
        if (null != price && CurrencyType.HKD.getValue().doubleValue() > 0){
            BigDecimal value = price.multiply(this.getValue()).divide(CurrencyType.HKD.getValue(), Constant.NUM_SCALE_2, RoundingMode.HALF_UP);
            //System.out.println(CurrencyType.HKD.getValue()+","+value.toString()+","+currencyType.getHeaderName());
            return value;
        }
        return new BigDecimal("0");
    }

    /**
     * <p>根据价格获取有货币前缀的转换价格</p>
     * @param price
     * @return java.math.BigDecimal
     * @since : 2022/11/30
     **/
    public String getCurrencyAndHeaderByPriceValue(BigDecimal price){
        return getHeaderName()+getCurrencyByPriceValue(price).toString();
    }

    public String getHeaderName() {
        return headerName;
    }

    public void setHeaderName(String headerName) {
        this.headerName = headerName;
    }

    public String getPriceName() {
        return priceName;
    }

    public void setPriceName(String priceName) {
        this.priceName = priceName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }
}
