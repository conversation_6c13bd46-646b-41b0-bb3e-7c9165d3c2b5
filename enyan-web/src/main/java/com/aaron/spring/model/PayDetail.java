package com.aaron.spring.model;

import com.aaron.spring.common.EBookConstant;

import java.io.Serializable;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/6/16
 * @Modified By:
 */
public class PayDetail implements Serializable {
    private static final long serialVersionUID = -3398472112114761788L;

    public static final String PAY_TYPE_ALIPAY = "支付宝";
    public static final String PAY_TYPE_ALIPAY_HK = "AlipayHK";
    public static final String PAY_TYPE_STRIPE = "信用卡";
    public static final String PAY_TYPE_STRIPE_OUTSIDE_HK = "信用卡(非香港)";
    public static final String PAY_TYPE_STRIPE_IN_HK = "信用卡(香港)";
    public static final String PAY_TYPE_REDEEM_CODE = "兑换码";
    public static final String PAY_TYPE_FREE = "免费";
    public static final String PAY_TYPE_DIRECT = "直接支付";

    //商户订单号
    private String outTradeNO;
    //交易号
    private String tradeNO;
    //交易状态
    private String tradeStatus;

    private String amount;

    private String currency;

    private int payType;

    private String country;//CN HK

    private String payID;

    public String payTypeDescription(){
        return payTypeDescription(payType);
    }

    public static String payTypeDescription(int payType){
        switch (payType){
            case EBookConstant.PayType.ALI_PAY:
                return PAY_TYPE_ALIPAY;
            case EBookConstant.PayType.ALI_PAY_HK:
                return PAY_TYPE_ALIPAY_HK;
            case EBookConstant.PayType.STRIPE_PAY:
                return PAY_TYPE_STRIPE;
            case EBookConstant.PayType.FREE_PAY:
                return PAY_TYPE_FREE;
            case EBookConstant.PayType.REDEEM_CODE_PAY:
                return PAY_TYPE_REDEEM_CODE;
            case EBookConstant.PayType.STRIPE_PAY_IN_HK:
                return PAY_TYPE_STRIPE_IN_HK;
            case EBookConstant.PayType.STRIPE_PAY_OUTSIDE_HK:
                return PAY_TYPE_STRIPE_OUTSIDE_HK;
            case EBookConstant.PayType.DIRECT_PAY:
            case EBookConstant.PayType.DIRECT_PAY_ALIPAY:
                return PAY_TYPE_DIRECT;
        }
        return "";
    }


    @Override
    public String toString() {
        return payTypeDescription();
    }

    public String getOutTradeNO() {
        return outTradeNO;
    }

    public void setOutTradeNO(String outTradeNO) {
        this.outTradeNO = outTradeNO;
    }

    public String getTradeNO() {
        return tradeNO;
    }

    public void setTradeNO(String tradeNO) {
        this.tradeNO = tradeNO;
    }

    public String getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(String tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPayID() {
        return payID;
    }

    public void setPayID(String payID) {
        this.payID = payID;
    }
}
