package com.aaron.spring.model;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/4/8
 * @Modified By:
 */
public class CustomUserDetail implements Serializable{
    private static final long serialVersionUID = -8468426061933356997L;

    @JSONField(name = "pub")
    private long publisherId;

    @JSONField(name = "pubs")
    private List<String> publisherIdList;

    @JSONField(name = "cart")
    private CartInfoGerenal cartInfoGerenal;

    /**
     * 验证device的token
     * */
    @JSONField(name = "devices")
    private List<DeviceLimit> deviceLimitList;//验证device的token

    /**
     * 验证web 登录device的token
     * */
    @JSONField(name = "webDevice")
    private DeviceLimit webDeviceLimit;

    public long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(long publisherId) {
        this.publisherId = publisherId;
    }

    public List<String> getPublisherIdList() {
        return publisherIdList;
    }

    public void setPublisherIdList(List<String> publisherIdList) {
        this.publisherIdList = publisherIdList;
    }

    public CartInfoGerenal getCartInfoGerenal() {
        return cartInfoGerenal;
    }

    public void setCartInfoGerenal(CartInfoGerenal cartInfoGerenal) {
        this.cartInfoGerenal = cartInfoGerenal;
    }

    public List<DeviceLimit> getDeviceLimitList() {
        return deviceLimitList;
    }

    public void setDeviceLimitList(List<DeviceLimit> deviceLimitList) {
        this.deviceLimitList = deviceLimitList;
    }

    public DeviceLimit getWebDeviceLimit() {
        return webDeviceLimit;
    }

    public void setWebDeviceLimit(DeviceLimit webDeviceLimit) {
        this.webDeviceLimit = webDeviceLimit;
    }

    public String jsonString(){
        return JSONObject.toJSONString(this);
    }

    public static  void main(String[] args){
        CustomUserDetail customUserDetail = new CustomUserDetail();
        customUserDetail.setPublisherIdList(Arrays.asList("1", "2", "3"));
        customUserDetail.setPublisherId(2);

        String json = JSONObject.toJSONString(customUserDetail);
        System.out.println(json);
        System.out.println(customUserDetail.jsonString());

        CustomUserDetail detail = JSON.parseObject(json,CustomUserDetail.class);
        System.out.println(detail.jsonString());
    }
}
