package com.aaron.spring.model;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Accessors(chain = true)
public class PodPodcast extends BaseDTO{
    /** 播客(栏目)ID */
    private Long podcastId;
    /** 栏目标题 */
    private String title;
    /** 主持人/作者名 */
    private String authorName;
    /** 栏目描述 */
    private String description;
    /** 封面图片URL */
    private String coverImageUrl;
    /** 详情页面长条图片URL */
    private String coverImageUrl2;
    /** 首页展示顺序 */
    private Integer displayOrder;
    /** 总集数 */
    private Integer episodeCount;
    /** 是否已发布 */
    private Integer isPublished;
    /** 发布日期 */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date publicationDate;
    /** 是否已经删除 */
    private Integer isDeleted;
    /** 创建时间 */
    private Date createdAt;


}
