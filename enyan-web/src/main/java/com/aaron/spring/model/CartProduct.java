package com.aaron.spring.model;

import com.alibaba.fastjson2.annotation.JSONField;

import java.io.Serializable;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/3/12
 * @Modified By:
 */
public class CartProduct implements Serializable {
    private static final long serialVersionUID = -190546827124354044L;
    @JSONField(name = "c")
    private Long code;
    @JSONField(name = "q")
    private int quantity;

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }
}
