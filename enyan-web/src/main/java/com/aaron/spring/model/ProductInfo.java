package com.aaron.spring.model;

import com.aaron.spring.common.BookUtil;
import com.aaron.spring.common.Constant;
import com.aaron.spring.common.EBookConstant;
import com.alibaba.fastjson2.annotation.JSONField;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class ProductInfo implements Serializable,Cloneable {

    private static final long serialVersionUID = -6864504185848303368L;

	private Long code;
	private String name;
    private String productCover;
    private String producer;
	private BigDecimal price;

	private BigDecimal priceCny;
	private BigDecimal priceUsd;
    private BigDecimal priceHkd;

	private BigDecimal priceCnyDiscount;

	private BigDecimal priceUSDDiscount;

    private BigDecimal priceHKDDiscount;//使用优惠券或N件折后的平均价格

    private BigDecimal priceHKDMiddle;//价格的中间价格，主要用于未支付订单的展示（单个折扣或没有折扣后的真实价格）

    private boolean discountSingleIsValid;

    private boolean discountCouponIsValid;

    private boolean discountIsValid;//N件折

    private boolean discountAnyIsValid; //是否有折扣（单本书折扣；满打折；优惠码）

	private Long discountId;
	
	private CommonsMultipartFile fieData;

	private String priceText;

	private String priceOldText;

    private String priceTotalText;

	private Byte shelfStatus;

    private Long publisherId;

    private String bookEsin;

    private String bookPubCode;

    private boolean hasBought;

    private Integer salesModel;

    private BigDecimal quantity = new BigDecimal("1");

    private Integer orderType;

    @JSONField(serialize = false)
    private BigDecimal realPriceHKD; // 最终的价格，如果有折扣，就是折扣价格
	
	public ProductInfo() {
	}
	
	public ProductInfo(EnyanBook book) {
        this.initWithBook(book);
        if (book.getBookType() == EBookConstant.BookType.EBOOK_SET){
            this.orderType = EBookConstant.OrderType.ORDER_EBOOK_SET_BUY;
        }else {
            this.orderType = EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY;
        }
	}

    public ProductInfo(EnyanBook book, Integer orderType) {
        this.initWithBook(book);
        this.orderType = orderType;
    }
	
	public ProductInfo(Long code, String name, BigDecimal price) {
        this.code = code;
        this.name = name;
        this.price = price;
    }

    public ProductInfo(EnyanRent rent){
        this.code = Long.parseLong(rent.getRentType()+"");
        this.name = BookUtil.getBookNameInRentType(rent.getRentType(), rent.getRentLang());
        this.price = rent.getTotalFee();
        this.orderType = EBookConstant.OrderType.ORDER_EBOOK_SINGLE_BUY;
    }

    private void initWithBook(EnyanBook book){
        this.code = book.getBookId();
        this.name = book.getBookTitle();
        this.productCover = book.getBookCoverApp();
        this.producer = book.getAuthor();
        this.shelfStatus = book.getShelfStatus();
        this.bookEsin = book.getBookEsin();
        this.bookPubCode = book.getBookPubCode();

        if (null!=book.getPriceCny()){
            this.priceCny = book.getPriceCny();
        }
        if (null != book.getPriceUsd()){
            this.priceUsd = book.getPriceUsd();
        }
        if (null != book.getPriceHkd()){
            this.priceHkd = book.getPriceHkd();
            this.priceHKDMiddle = book.getPriceHkd();
        }

        this.discountSingleIsValid = Constant.BYTE_VALUE_1.equals(book.getDiscountSingleIsValid());
        if (discountSingleIsValid){
            this.priceCnyDiscount = book.getPriceCnyDiscount();
            this.priceUSDDiscount = book.getPriceUSDDiscount();
            this.priceHKDDiscount = book.getPriceHKDDiscount();
            this.priceHKDMiddle = book.getPriceHKDDiscount();
        }else {
            this.priceCnyDiscount = book.getPriceCny();
            this.priceUSDDiscount = book.getPriceUsd();
            this.priceHKDDiscount = book.getPriceHkd();
            this.priceHKDMiddle = book.getPriceHkd();
        }

        this.discountId = book.getDiscountId();
        EnyanDiscount enyanDiscount = Constant.discountMap.get(this.discountId);
        if (null == enyanDiscount || !Constant.BYTE_VALUE_1.equals(enyanDiscount.getIsValid())){
            this.discountId = Constant.DEFAULT_DISCOUNT_ID;
            this.discountIsValid = false;
        }else {
            this.discountIsValid = true;
        }
        this.publisherId = book.getPublisherId();
        this.salesModel = book.getSalesModel();
    }

    /**
     * <p>设置产品的满几件折扣,使用真实的Middle值乘以折扣</p>
     * <p>设置产品的满几件折扣,使用原价乘以折扣 2020/09/16</p>
     * @param discountValue
     * @return: void
     * @since : 2020-08-05
     */
    public void resetProductCumulateDiscount(Integer discountValue){
        if (discountValue >= 100){
            this.setPriceHKDDiscount(this.getPriceHKDMiddle());
            return;
        }
        //this.discountCouponIsValid = true;
        BigDecimal price = this.getPriceHkd().multiply(new BigDecimal(discountValue)).divide(Constant.VALUE_100,Constant.NUM_SCALE_2, RoundingMode.HALF_UP);
        this.setPriceHKDDiscount(price);
    }
		
	public Long getCode() {
		return code;
	}
	public void setCode(Long code) {
		this.code = code;
	}
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	

	public CommonsMultipartFile getFileData() {
		return fieData;
	}
	public void setFileData(CommonsMultipartFile fileData) {
		this.fieData = fileData;
	}

    public String getProductCover() {
        return productCover;
    }

    public void setProductCover(String productCover) {
        this.productCover = productCover;
    }

    public String getProducer() {
        return producer;
    }

    public void setProducer(String producer) {
        this.producer = producer;
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    public boolean isDiscountSingleIsValid() {
        return discountSingleIsValid;
    }

    public void setDiscountSingleIsValid(boolean discountSingleIsValid) {
        this.discountSingleIsValid = discountSingleIsValid;
    }

    public String getPriceText() {
        return priceText;
    }

    public void setPriceText(String priceText) {
        this.priceText = priceText;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPriceCny() {
        return priceCny;
    }

    public void setPriceCny(BigDecimal priceCny) {
        this.priceCny = priceCny;
    }

    public BigDecimal getPriceUsd() {
        return priceUsd;
    }

    public void setPriceUsd(BigDecimal priceUsd) {
        this.priceUsd = priceUsd;
    }

    public BigDecimal getPriceCnyDiscount() {
        return priceCnyDiscount;
    }

    public void setPriceCnyDiscount(BigDecimal priceCnyDiscount) {
        this.priceCnyDiscount = priceCnyDiscount;
    }

    public BigDecimal getPriceUSDDiscount() {
        return priceUSDDiscount;
    }

    public void setPriceUSDDiscount(BigDecimal priceUSDDiscount) {
        this.priceUSDDiscount = priceUSDDiscount;
    }

    public Byte getShelfStatus() {
        return shelfStatus;
    }

    public void setShelfStatus(Byte shelfStatus) {
        this.shelfStatus = shelfStatus;
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public String getPriceOldText() {
        return priceOldText;
    }

    public void setPriceOldText(String priceOldText) {
        this.priceOldText = priceOldText;
    }

    public String getPriceTotalText() {
        return priceTotalText;
    }

    public void setPriceTotalText(String priceTotalText) {
        this.priceTotalText = priceTotalText;
    }

    public String getBookEsin() {
        return bookEsin;
    }

    public void setBookEsin(String bookEsin) {
        this.bookEsin = bookEsin;
    }

    public String getBookPubCode() {
        return bookPubCode;
    }

    public void setBookPubCode(String bookPubCode) {
        this.bookPubCode = bookPubCode;
    }

    public BigDecimal getPriceHkd() {
        return priceHkd;
    }

    public void setPriceHkd(BigDecimal priceHkd) {
        this.priceHkd = priceHkd;
    }

    public BigDecimal getPriceHKDDiscount() {
        return priceHKDDiscount;
    }

    public void setPriceHKDDiscount(BigDecimal priceHKDDiscount) {
        this.priceHKDDiscount = priceHKDDiscount;
    }

    public boolean isHasBought() {
        return hasBought;
    }

    public void setHasBought(boolean hasBought) {
        this.hasBought = hasBought;
    }

    public Integer getSalesModel() {
        return salesModel;
    }

    public void setSalesModel(Integer salesModel) {
        this.salesModel = salesModel;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getRealPriceHKD() {
        if (null == priceHKDDiscount){
            return priceHkd;
        }
        return priceHKDDiscount;
    }


    public void setRealPriceHKD(BigDecimal realPriceHKD) {
        this.realPriceHKD = realPriceHKD;
    }

    public boolean isDiscountCouponIsValid() {
        return discountCouponIsValid;
    }

    public void setDiscountCouponIsValid(boolean discountCouponIsValid) {
        this.discountCouponIsValid = discountCouponIsValid;
    }

    public boolean isDiscountAnyIsValid() {
        return discountAnyIsValid || discountSingleIsValid || discountCouponIsValid || discountIsValid;
    }

    public void setDiscountAnyIsValid(boolean discountAnyIsValid) {
        this.discountAnyIsValid = discountAnyIsValid;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    public BigDecimal getPriceHKDMiddle() {
        return priceHKDMiddle;
    }

    public void setPriceHKDMiddle(BigDecimal priceHKDMiddle) {
        this.priceHKDMiddle = priceHKDMiddle;
    }

    public boolean isDiscountIsValid() {
        return discountIsValid;
    }

    public void setDiscountIsValid(boolean discountIsValid) {
        this.discountIsValid = discountIsValid;
    }

    @Override
    public String toString() {
        return "ProductInfo{" +
                "code=" + code +
                ", name='" + name + '\'' +
                ", productCover='" + productCover + '\'' +
                ", producer='" + producer + '\'' +
                ", price=" + price +
                ", priceCny=" + priceCny +
                ", priceUsd=" + priceUsd +
                ", priceHkd=" + priceHkd +
                ", priceCnyDiscount=" + priceCnyDiscount +
                ", priceUSDDiscount=" + priceUSDDiscount +
                ", priceHKDDiscount=" + priceHKDDiscount +
                ", discountSingleIsValid=" + discountSingleIsValid +
                ", discountId=" + discountId +
                ", fieData=" + fieData +
                ", priceText='" + priceText + '\'' +
                ", priceOldText='" + priceOldText + '\'' +
                ", shelfStatus=" + shelfStatus +
                ", publisherId=" + publisherId +
                ", bookEsin='" + bookEsin + '\'' +
                ", bookPubCode='" + bookPubCode + '\'' +
                ", hasBought=" + hasBought +
                ", salesModel=" + salesModel +
                ", quantity=" + quantity +
                ", orderType=" + orderType +
                '}';
    }
}
