package com.aaron.spring.model;

import java.io.Serializable;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/3/30
 * @Modified By:
 */
public class EmailStatusDTO implements Serializable{
    private static final long serialVersionUID = 4395392210408895216L;

    public static final String STEP_ACTIVE = "email_active";
    public static final String STEP_PASSWD = "email_passwd";

    private boolean success = false;
    private boolean end = false;
    private String head;
    private String explan;
    private String emailOperation;
    private String emailCode;
    private String error;
    private String step;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getExplan() {
        return explan;
    }

    public void setExplan(String explan) {
        this.explan = explan;
    }

    public String getEmailOperation() {
        return emailOperation;
    }

    public void setEmailOperation(String emailOperation) {
        this.emailOperation = emailOperation;
    }

    public String getEmailCode() {
        return emailCode;
    }

    public void setEmailCode(String emailCode) {
        this.emailCode = emailCode;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public boolean isEnd() {
        return end;
    }

    public void setEnd(boolean end) {
        this.end = end;
    }
}
