package com.aaron.spring.model;

import com.aaron.mybatis.dao.pojo.Page;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EnyanBalanceHistoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Page page;

    public EnyanBalanceHistoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setPage(Page page) {
        this.page=page;
    }

    public Page getPage() {
        return page;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andBalanceHistoryIdIsNull() {
            addCriterion("balance_history_id is null");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdIsNotNull() {
            addCriterion("balance_history_id is not null");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdEqualTo(Long value) {
            addCriterion("balance_history_id =", value, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdNotEqualTo(Long value) {
            addCriterion("balance_history_id <>", value, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdGreaterThan(Long value) {
            addCriterion("balance_history_id >", value, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("balance_history_id >=", value, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdLessThan(Long value) {
            addCriterion("balance_history_id <", value, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdLessThanOrEqualTo(Long value) {
            addCriterion("balance_history_id <=", value, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdIn(List<Long> values) {
            addCriterion("balance_history_id in", values, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdNotIn(List<Long> values) {
            addCriterion("balance_history_id not in", values, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdBetween(Long value1, Long value2) {
            addCriterion("balance_history_id between", value1, value2, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andBalanceHistoryIdNotBetween(Long value1, Long value2) {
            addCriterion("balance_history_id not between", value1, value2, "balanceHistoryId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNull() {
            addCriterion("publisher_id is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIsNotNull() {
            addCriterion("publisher_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherIdEqualTo(Long value) {
            addCriterion("publisher_id =", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotEqualTo(Long value) {
            addCriterion("publisher_id <>", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThan(Long value) {
            addCriterion("publisher_id >", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdGreaterThanOrEqualTo(Long value) {
            addCriterion("publisher_id >=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThan(Long value) {
            addCriterion("publisher_id <", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdLessThanOrEqualTo(Long value) {
            addCriterion("publisher_id <=", value, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdIn(List<Long> values) {
            addCriterion("publisher_id in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotIn(List<Long> values) {
            addCriterion("publisher_id not in", values, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdBetween(Long value1, Long value2) {
            addCriterion("publisher_id between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andPublisherIdNotBetween(Long value1, Long value2) {
            addCriterion("publisher_id not between", value1, value2, "publisherId");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalIsNull() {
            addCriterion("income_vendor_total is null");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalIsNotNull() {
            addCriterion("income_vendor_total is not null");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalEqualTo(BigDecimal value) {
            addCriterion("income_vendor_total =", value, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalNotEqualTo(BigDecimal value) {
            addCriterion("income_vendor_total <>", value, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalGreaterThan(BigDecimal value) {
            addCriterion("income_vendor_total >", value, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("income_vendor_total >=", value, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalLessThan(BigDecimal value) {
            addCriterion("income_vendor_total <", value, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("income_vendor_total <=", value, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalIn(List<BigDecimal> values) {
            addCriterion("income_vendor_total in", values, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalNotIn(List<BigDecimal> values) {
            addCriterion("income_vendor_total not in", values, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_vendor_total between", value1, value2, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andIncomeVendorTotalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("income_vendor_total not between", value1, value2, "incomeVendorTotal");
            return (Criteria) this;
        }

        public Criteria andBalanceDayIsNull() {
            addCriterion("balance_day is null");
            return (Criteria) this;
        }

        public Criteria andBalanceDayIsNotNull() {
            addCriterion("balance_day is not null");
            return (Criteria) this;
        }

        public Criteria andBalanceDayEqualTo(Integer value) {
            addCriterion("balance_day =", value, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayNotEqualTo(Integer value) {
            addCriterion("balance_day <>", value, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayGreaterThan(Integer value) {
            addCriterion("balance_day >", value, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("balance_day >=", value, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayLessThan(Integer value) {
            addCriterion("balance_day <", value, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayLessThanOrEqualTo(Integer value) {
            addCriterion("balance_day <=", value, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayIn(List<Integer> values) {
            addCriterion("balance_day in", values, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayNotIn(List<Integer> values) {
            addCriterion("balance_day not in", values, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayBetween(Integer value1, Integer value2) {
            addCriterion("balance_day between", value1, value2, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceDayNotBetween(Integer value1, Integer value2) {
            addCriterion("balance_day not between", value1, value2, "balanceDay");
            return (Criteria) this;
        }

        public Criteria andBalanceAtIsNull() {
            addCriterion("balance_at is null");
            return (Criteria) this;
        }

        public Criteria andBalanceAtIsNotNull() {
            addCriterion("balance_at is not null");
            return (Criteria) this;
        }

        public Criteria andBalanceAtEqualTo(Date value) {
            addCriterion("balance_at =", value, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtNotEqualTo(Date value) {
            addCriterion("balance_at <>", value, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtGreaterThan(Date value) {
            addCriterion("balance_at >", value, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtGreaterThanOrEqualTo(Date value) {
            addCriterion("balance_at >=", value, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtLessThan(Date value) {
            addCriterion("balance_at <", value, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtLessThanOrEqualTo(Date value) {
            addCriterion("balance_at <=", value, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtIn(List<Date> values) {
            addCriterion("balance_at in", values, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtNotIn(List<Date> values) {
            addCriterion("balance_at not in", values, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtBetween(Date value1, Date value2) {
            addCriterion("balance_at between", value1, value2, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andBalanceAtNotBetween(Date value1, Date value2) {
            addCriterion("balance_at not between", value1, value2, "balanceAt");
            return (Criteria) this;
        }

        public Criteria andIsCountedIsNull() {
            addCriterion("is_counted is null");
            return (Criteria) this;
        }

        public Criteria andIsCountedIsNotNull() {
            addCriterion("is_counted is not null");
            return (Criteria) this;
        }

        public Criteria andIsCountedEqualTo(Byte value) {
            addCriterion("is_counted =", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedNotEqualTo(Byte value) {
            addCriterion("is_counted <>", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedGreaterThan(Byte value) {
            addCriterion("is_counted >", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_counted >=", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedLessThan(Byte value) {
            addCriterion("is_counted <", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedLessThanOrEqualTo(Byte value) {
            addCriterion("is_counted <=", value, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedIn(List<Byte> values) {
            addCriterion("is_counted in", values, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedNotIn(List<Byte> values) {
            addCriterion("is_counted not in", values, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedBetween(Byte value1, Byte value2) {
            addCriterion("is_counted between", value1, value2, "isCounted");
            return (Criteria) this;
        }

        public Criteria andIsCountedNotBetween(Byte value1, Byte value2) {
            addCriterion("is_counted not between", value1, value2, "isCounted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}