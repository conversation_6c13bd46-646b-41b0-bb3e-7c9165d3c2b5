package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.data.redis.core.index.Indexed;

//@RedisHash("payRate")
public class EnyanPayRate extends BaseDTO{

    private static final long serialVersionUID = 8229319427054909949L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private  Long payRateId;

    private  Integer rateDate;

    private Integer rateTime;

    private Integer rateType;

    private String rateValue;

    public Long getPayRateId() {
        return payRateId;
    }

    public void setPayRateId(Long payRateId) {
        this.payRateId = payRateId;
    }

    public Integer getRateDate() {
        return rateDate;
    }

    public void setRateDate(Integer rateDate) {
        this.rateDate = rateDate;
    }

    public Integer getRateTime() {
        return rateTime;
    }

    public void setRateTime(Integer rateTime) {
        this.rateTime = rateTime;
    }

    public Integer getRateType() {
        return rateType;
    }

    public void setRateType(Integer rateType) {
        this.rateType = rateType;
    }

    public String getRateValue() {
        return rateValue;
    }

    public void setRateValue(String rateValue) {
        this.rateValue = rateValue == null ? null : rateValue.trim();
    }
}