package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.util.Date;

public class EnyanAcsm extends BaseDTO implements Serializable{
    private static final long serialVersionUID = 8112190106205939456L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long acsmId;

    private String orderNum;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long bookId;

    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long userId;

    private String transactionId;

    private Byte isFulfilled;

    private String acsmInfo;

    private Date createAt;

    public Long getAcsmId() {
        return acsmId;
    }

    public void setAcsmId(Long acsmId) {
        this.acsmId = acsmId;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum == null ? null : orderNum.trim();
    }

    public Long getBookId() {
        return bookId;
    }

    public void setBookId(Long bookId) {
        this.bookId = bookId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId == null ? null : transactionId.trim();
    }

    public Byte getIsFulfilled() {
        return isFulfilled;
    }

    public void setIsFulfilled(Byte isFulfilled) {
        this.isFulfilled = isFulfilled;
    }

    public String getAcsmInfo() {
        return acsmInfo;
    }

    public void setAcsmInfo(String acsmInfo) {
        this.acsmInfo = acsmInfo == null ? null : acsmInfo.trim();
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }
}