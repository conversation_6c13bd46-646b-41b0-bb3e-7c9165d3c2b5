package com.aaron.spring.model;

import com.alibaba.fastjson2.JSONObject;

import java.io.Serializable;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2020-03-28
 * @Modified By:
 */
public class SlackMsg implements Serializable {

    private static final long serialVersionUID = 631917855389214003L;

    private String text;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String jsonString() {
        return JSONObject.toJSONString(this);
    }
}
