package com.aaron.spring.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/6/7
 * @Modified By:
 */
public class BalanceDetail implements Serializable{
    private static final long serialVersionUID = 805452710031923790L;

    private EnyanBalance balanceBottom;
    private List<EnyanBalance> balanceList;
    private Integer bookCostTotal;//电子书制作费
    private List<EnyanBook> bookCostList;
    private BigDecimal transferFee;//转账费
    private BigDecimal totalFee;//总共费用
    private EnyanPublisher publisher;

    public EnyanBalance getBalanceBottom() {
        return balanceBottom;
    }

    public void setBalanceBottom(EnyanBalance balanceBottom) {
        this.balanceBottom = balanceBottom;
    }

    public List<EnyanBalance> getBalanceList() {
        return balanceList;
    }

    public void setBalanceList(List<EnyanBalance> balanceList) {
        this.balanceList = balanceList;
    }

    public Integer getBookCostTotal() {
        return bookCostTotal;
    }

    public void setBookCostTotal(Integer bookCostTotal) {
        this.bookCostTotal = bookCostTotal;
    }

    public List<EnyanBook> getBookCostList() {
        return bookCostList;
    }

    public void setBookCostList(List<EnyanBook> bookCostList) {
        this.bookCostList = bookCostList;
    }

    public BigDecimal getTransferFee() {
        return transferFee;
    }

    public void setTransferFee(BigDecimal transferFee) {
        this.transferFee = transferFee;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public EnyanPublisher getPublisher() {
        return publisher;
    }

    public void setPublisher(EnyanPublisher publisher) {
        this.publisher = publisher;
    }
}
