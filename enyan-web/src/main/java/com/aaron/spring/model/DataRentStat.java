package com.aaron.spring.model;

import com.aaron.repair.Long2String;
import com.aaron.repair.String2Long;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serial;
import java.util.Date;

public class DataRentStat extends BaseDTO{
    @Serial
    private static final long serialVersionUID = -2516767307737732288L;
    @JsonSerialize(using=Long2String.class)
    @JsonDeserialize(using=String2Long.class)
    private Long dataId;

    private Integer rentScAllSum;

    private Integer rentScOtSum;

    private Integer rentScNtSum;

    private Integer rentScAllNew;

    private Integer rentScOtNew;

    private Integer rentScNtNew;

    private Integer rentScAllLeave;

    private Integer rentScOtLeave;

    private Integer rentScNtLeave;

    private Integer rentScAllBuy;

    private Integer rentScOtBuy;

    private Integer rentScNtBuy;

    private Integer rentScAllActive;

    private Integer rentScOtActive;

    private Integer rentScNtActive;

    private String rentScAllTime;

    private String rentScOtTime;

    private String rentScNtTime;

    private Integer rentTcAllSum;

    private Integer rentTcOtSum;

    private Integer rentTcNtSum;

    private Integer rentTcAllNew;

    private Integer rentTcOtNew;

    private Integer rentTcNtNew;

    private Integer rentTcAllLeave;

    private Integer rentTcOtLeave;

    private Integer rentTcNtLeave;

    private Integer rentTcAllBuy;

    private Integer rentTcOtBuy;

    private Integer rentTcNtBuy;

    private Integer rentTcAllActive;

    private Integer rentTcOtActive;

    private Integer rentTcNtActive;

    private String rentTcAllTime;

    private String rentTcOtTime;

    private String rentTcNtTime;

    private Date dataAt;

    private Date createTime;

    private String dateString;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public Integer getRentScAllSum() {
        return rentScAllSum;
    }

    public void setRentScAllSum(Integer rentScAllSum) {
        this.rentScAllSum = rentScAllSum;
    }

    public Integer getRentScOtSum() {
        return rentScOtSum;
    }

    public void setRentScOtSum(Integer rentScOtSum) {
        this.rentScOtSum = rentScOtSum;
    }

    public Integer getRentScNtSum() {
        return rentScNtSum;
    }

    public void setRentScNtSum(Integer rentScNtSum) {
        this.rentScNtSum = rentScNtSum;
    }

    public Integer getRentScAllNew() {
        return rentScAllNew;
    }

    public void setRentScAllNew(Integer rentScAllNew) {
        this.rentScAllNew = rentScAllNew;
    }

    public Integer getRentScOtNew() {
        return rentScOtNew;
    }

    public void setRentScOtNew(Integer rentScOtNew) {
        this.rentScOtNew = rentScOtNew;
    }

    public Integer getRentScNtNew() {
        return rentScNtNew;
    }

    public void setRentScNtNew(Integer rentScNtNew) {
        this.rentScNtNew = rentScNtNew;
    }

    public Integer getRentScAllLeave() {
        return rentScAllLeave;
    }

    public void setRentScAllLeave(Integer rentScAllLeave) {
        this.rentScAllLeave = rentScAllLeave;
    }

    public Integer getRentScOtLeave() {
        return rentScOtLeave;
    }

    public void setRentScOtLeave(Integer rentScOtLeave) {
        this.rentScOtLeave = rentScOtLeave;
    }

    public Integer getRentScNtLeave() {
        return rentScNtLeave;
    }

    public void setRentScNtLeave(Integer rentScNtLeave) {
        this.rentScNtLeave = rentScNtLeave;
    }

    public Integer getRentScAllBuy() {
        return rentScAllBuy;
    }

    public void setRentScAllBuy(Integer rentScAllBuy) {
        this.rentScAllBuy = rentScAllBuy;
    }

    public Integer getRentScOtBuy() {
        return rentScOtBuy;
    }

    public void setRentScOtBuy(Integer rentScOtBuy) {
        this.rentScOtBuy = rentScOtBuy;
    }

    public Integer getRentScNtBuy() {
        return rentScNtBuy;
    }

    public void setRentScNtBuy(Integer rentScNtBuy) {
        this.rentScNtBuy = rentScNtBuy;
    }

    public Integer getRentScAllActive() {
        return rentScAllActive;
    }

    public void setRentScAllActive(Integer rentScAllActive) {
        this.rentScAllActive = rentScAllActive;
    }

    public Integer getRentScOtActive() {
        return rentScOtActive;
    }

    public void setRentScOtActive(Integer rentScOtActive) {
        this.rentScOtActive = rentScOtActive;
    }

    public Integer getRentScNtActive() {
        return rentScNtActive;
    }

    public void setRentScNtActive(Integer rentScNtActive) {
        this.rentScNtActive = rentScNtActive;
    }

    public String getRentScAllTime() {
        return rentScAllTime;
    }

    public void setRentScAllTime(String rentScAllTime) {
        this.rentScAllTime = rentScAllTime == null ? null : rentScAllTime.trim();
    }

    public String getRentScOtTime() {
        return rentScOtTime;
    }

    public void setRentScOtTime(String rentScOtTime) {
        this.rentScOtTime = rentScOtTime == null ? null : rentScOtTime.trim();
    }

    public String getRentScNtTime() {
        return rentScNtTime;
    }

    public void setRentScNtTime(String rentScNtTime) {
        this.rentScNtTime = rentScNtTime == null ? null : rentScNtTime.trim();
    }

    public Integer getRentTcAllSum() {
        return rentTcAllSum;
    }

    public void setRentTcAllSum(Integer rentTcAllSum) {
        this.rentTcAllSum = rentTcAllSum;
    }

    public Integer getRentTcOtSum() {
        return rentTcOtSum;
    }

    public void setRentTcOtSum(Integer rentTcOtSum) {
        this.rentTcOtSum = rentTcOtSum;
    }

    public Integer getRentTcNtSum() {
        return rentTcNtSum;
    }

    public void setRentTcNtSum(Integer rentTcNtSum) {
        this.rentTcNtSum = rentTcNtSum;
    }

    public Integer getRentTcAllNew() {
        return rentTcAllNew;
    }

    public void setRentTcAllNew(Integer rentTcAllNew) {
        this.rentTcAllNew = rentTcAllNew;
    }

    public Integer getRentTcOtNew() {
        return rentTcOtNew;
    }

    public void setRentTcOtNew(Integer rentTcOtNew) {
        this.rentTcOtNew = rentTcOtNew;
    }

    public Integer getRentTcNtNew() {
        return rentTcNtNew;
    }

    public void setRentTcNtNew(Integer rentTcNtNew) {
        this.rentTcNtNew = rentTcNtNew;
    }

    public Integer getRentTcAllLeave() {
        return rentTcAllLeave;
    }

    public void setRentTcAllLeave(Integer rentTcAllLeave) {
        this.rentTcAllLeave = rentTcAllLeave;
    }

    public Integer getRentTcOtLeave() {
        return rentTcOtLeave;
    }

    public void setRentTcOtLeave(Integer rentTcOtLeave) {
        this.rentTcOtLeave = rentTcOtLeave;
    }

    public Integer getRentTcNtLeave() {
        return rentTcNtLeave;
    }

    public void setRentTcNtLeave(Integer rentTcNtLeave) {
        this.rentTcNtLeave = rentTcNtLeave;
    }

    public Integer getRentTcAllBuy() {
        return rentTcAllBuy;
    }

    public void setRentTcAllBuy(Integer rentTcAllBuy) {
        this.rentTcAllBuy = rentTcAllBuy;
    }

    public Integer getRentTcOtBuy() {
        return rentTcOtBuy;
    }

    public void setRentTcOtBuy(Integer rentTcOtBuy) {
        this.rentTcOtBuy = rentTcOtBuy;
    }

    public Integer getRentTcNtBuy() {
        return rentTcNtBuy;
    }

    public void setRentTcNtBuy(Integer rentTcNtBuy) {
        this.rentTcNtBuy = rentTcNtBuy;
    }

    public Integer getRentTcAllActive() {
        return rentTcAllActive;
    }

    public void setRentTcAllActive(Integer rentTcAllActive) {
        this.rentTcAllActive = rentTcAllActive;
    }

    public Integer getRentTcOtActive() {
        return rentTcOtActive;
    }

    public void setRentTcOtActive(Integer rentTcOtActive) {
        this.rentTcOtActive = rentTcOtActive;
    }

    public Integer getRentTcNtActive() {
        return rentTcNtActive;
    }

    public void setRentTcNtActive(Integer rentTcNtActive) {
        this.rentTcNtActive = rentTcNtActive;
    }

    public String getRentTcAllTime() {
        return rentTcAllTime;
    }

    public void setRentTcAllTime(String rentTcAllTime) {
        this.rentTcAllTime = rentTcAllTime == null ? null : rentTcAllTime.trim();
    }

    public String getRentTcOtTime() {
        return rentTcOtTime;
    }

    public void setRentTcOtTime(String rentTcOtTime) {
        this.rentTcOtTime = rentTcOtTime == null ? null : rentTcOtTime.trim();
    }

    public String getRentTcNtTime() {
        return rentTcNtTime;
    }

    public void setRentTcNtTime(String rentTcNtTime) {
        this.rentTcNtTime = rentTcNtTime == null ? null : rentTcNtTime.trim();
    }

    public Date getDataAt() {
        return dataAt;
    }

    public void setDataAt(Date dataAt) {
        this.dataAt = dataAt;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    @Override
    public String toString() {
        return "先租后买" +
                       "数据Id=" + dataId +
                       ":简体{全套历史订阅量=" + rentScAllSum +
                       ", 旧约历史订阅量=" + rentScOtSum +
                       ", 新约历史订阅量=" + rentScNtSum +
                       ", 昨日全套新增订阅=" + rentScAllNew +
                       ", 昨日旧约新增订阅=" + rentScOtNew +
                       ", 昨日新约新增订阅=" + rentScNtNew +
                       ", 昨日全套退订=" + rentScAllLeave +
                       ", 昨日旧约退订=" + rentScOtLeave +
                       ", 昨日新约退订=" + rentScNtLeave +
                       ", 昨日全套新增退订购买=" + rentScAllBuy +
                       ", 昨日旧约新增退订购买=" + rentScOtBuy +
                       ", 昨日新约新增退订购买=" + rentScNtBuy +
                       ", 全套订阅中=" + rentScAllActive +
                       ", 旧约订阅中=" + rentScOtActive +
                       ", 新约订阅中=" + rentScNtActive +
                       ", 全套平均订阅时长(月)='" + rentScAllTime + '\'' +
                       ", 旧约退订平均订阅时长(月)='" + rentScOtTime + '\'' +
                       ", 新约退订平均订阅时长(月)='" + rentScNtTime + '\'' +
                       "};繁体{全套历史订阅量=" + rentTcAllSum +
                       ", 旧约历史订阅量=" + rentTcOtSum +
                       ", 新约历史订阅量=" + rentTcNtSum +
                       ", 昨日全套新增订阅=" + rentTcAllNew +
                       ", 昨日旧约新增订阅=" + rentTcOtNew +
                       ", 昨日新约新增订阅=" + rentTcNtNew +
                       ", 昨日全套退订=" + rentTcAllLeave +
                       ", 昨日旧约退订=" + rentTcOtLeave +
                       ", 昨日新约退订=" + rentTcNtLeave +
                       ", 昨日全套新增退订购买=" + rentTcAllBuy +
                       ", 昨日旧约新增退订购买=" + rentTcOtBuy +
                       ", 昨日新约新增退订购买=" + rentTcNtBuy +
                       ", 全套订阅中=" + rentTcAllActive +
                       ", 旧约订阅中=" + rentTcOtActive +
                       ", 新约订阅中=" + rentTcNtActive +
                       ", 全套平均订阅时长(月)='" + rentTcAllTime + '\'' +
                       ", 旧约平均订阅时长(月)='" + rentTcOtTime + '\'' +
                       ", 新约平均订阅时长(月)='" + rentTcNtTime + '\'' +
                       //", dataAt=" + dataAt +
                       //", createTime=" + createTime +
                       '}';
    }
}