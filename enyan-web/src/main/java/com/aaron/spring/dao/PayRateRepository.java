package com.aaron.spring.dao;

import com.aaron.spring.model.EnyanPayRate;
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * @Author: <PERSON>
 * @Date: Created in  2018/5/28
 * @Modified By:
 */
//@Repository
public interface PayRateRepository extends BaseRepository<EnyanPayRate>{

    List<EnyanPayRate> findByRateDate(int rateDate);
}
