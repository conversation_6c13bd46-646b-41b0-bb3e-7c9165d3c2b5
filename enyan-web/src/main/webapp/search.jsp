<%@ page import="com.aaron.util.UserUtils" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>jQuery UI Autocomplete - Remote JSONP datasource</title>
    <link rel="stylesheet" href="<c:url value='/statics/js/ui/jquery-ui.css'/>"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper em {
            color: red;
        }
        .ui-menu .ui-menu-item-wrapper b {
            color: red;
        }

        .ui-menu .ui-menu-item em{
            color: #96f226;
            border-radius: 0px;
            border: 1px solid #454545;
        }

        .ui-autocomplete-term { font-weight: bold; color: red; }

        .ui-autocomplete-highlight {
            font-weight: bold;
            color: red;
        }

    </style>

    <script src="<c:url value='/statics/js/jquery-1.10.2.min.js'/>"></script>
    <script src="<c:url value='/statics/js/ui/jquery-ui.js'/>"></script>
    <script>
        $( function() {
            function log( message ) {
                $( "<div>" ).text( message ).prependTo( "#log" );
                $( "#log" ).scrollTop( 0 );
            }
            function selected( message ) {
                $( "<div>" ).text( message ).prependTo( "#log" );
                $( "#log" ).scrollTop( 0 );
            }
            var cache = {};
            var termTemplate = "<span class='ui-autocomplete-term'>%s</span>";
            $( "#birds" ).autocomplete({
                source: function( request, response ) {
                    var term = request.term;
                    if ( term in cache ) {
                        response( cache[ term ] );
                        return;
                    }
                    $.ajax( {
                        type: 'POST',
                        url: "/info/search",
                        dataType: "json",
                        contentType: "application/json",
                        data: JSON.stringify({
                            term: term
                        }),
                        success: function( data ) {
                            //console.log(request.term);
                            //console.log(data);
                            cache[ term ] = data.result;
                            response( data.result );
                        }
                    } );
                },
                minLength: 2,
                select: function( event, ui ) {
                    selected( "Selected: " + ui.item.value + " aka " + ui.item.id );
                }
            } );
        } );

        /*
        $.selected = function(message){//重载$.attr()方法
            alert(message);
        }
        $.attr = function(){//重载$.attr()方法
            alert("1");
        }
        $.attr()//执行$.attr()方法, 弹出值"1"  方法已经被重载
        * */

    </script>
</head>
<body>

<div class="ui-widget">
    <label for="birds">Birds: </label>
    <input id="birds">
</div>

<div class="ui-widget" style="margin-top:2em; font-family:Arial">
    Result:
    <div id="log" style="height: 200px; width: 300px; overflow: auto;" class="ui-widget-content"></div>
</div>


</body>
</html>