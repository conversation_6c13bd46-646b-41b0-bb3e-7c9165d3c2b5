{"alert.exit": "Do you want to sign out?", "alert.bookShelf.remove.title": "Remove the selected book from the Library?", "alert.bookShelf.remove.title.muti": "Remove the selected books from the Library?", "alert.bookShelf.remove.msg": "This operation will also delete local file", "alert.bookShelf.remove.msg.muti": "This operation will also delete local files", "alert.bookStore.go": "You will leave the app and go to the browser\nVisit \"Inspirata eBooks\" platform", "alert.reg.go": "You will leave the app and go to the browser\nSign up for an Inspirata account", "alert.highlight.remove.title": "Delete the selected note?", "alert.highlight.remove.title.muti": "Delete the selected notes?", "alert.highlight.remove.all.title": "Delete the selected note？", "alert.highlight.remove.all.title.muti": "Delete the selected notes？", "alert.login.email.invalidate": "Login account verification failed, please re-enter", "alert.copy.right.title": "Warning", "alert.copy.right.msg": "You have reached the limit (300 characters) you can copy in a single copy.", "alert.copy.right.all.title": "Warning", "alert.copy.right.all.msg": "You have reached the limit (5,000 characters) you can copy in a single book.", "alert.share.right.title": "Warning", "alert.share.right.msg": "You have reached the limit (300 characters) you can share in a single session.", "alert.share.success": "Shared successfully", "alert.share.fail": "Failed to share", "alert.export.success": "Exported successfully", "alert.export.fail": "Failed to export", "alert.save.success": "Saved successfully", "alert.save.fail": "Failed to save", "alert.spirit.add.success": "Subscribed successfully! You can start this devotional plan in \"My Devotionals\" now!", "alert.spirit.remove.success": "This devotional plan has been removed successfully.", "alert.spirit.remove.confirm": "Remove the devotional plan？", "alert.spirit.remove.msg": "Once you remove the plan, the punch list will be cleared, Devotional notes remain, You can re-susbcribe the plan in \"All Devotions\" ", "alert.spirit.notbuy.read": "You can access to contents on other dates after purchasing the book.", "alert.spirit.mark.outstrip": "Please don't punch in ahead of schedule!", "alert.spirit.mark.replenish": "Sorry, you can't make up the punch in！", "alert.spirit.read.isfirst": "It's already the first article!", "alert.spirit.read.islast": "It's already the last article!", "alert.spirit.plan.adjust": "You can adjust schedule after purchasing the book", "alert.spirit.plan.finished.notadjust": "This schedule is unadjustable since it’s finished.", "alert.plan.restart.confirm": "Do you want to restart?", "alert.plan.restart.msg": "After restarting, the existing punch list will be cleared, the devotional notes will remain.", "alert.plan.enter.number": "Please enter a number", "alert.plan.number.outofrange": "The number you entered is not within the range", "alert.edit.abandon": "Discard the draft?", "alert.version.update.title": "Version Update", "alert.version.update.warn": "Warm Reminder: There is an important update, please update to the latest version.", "alert.version.update.not": "Not now", "alert.version.update.now": "Update", "alert.email.confirm": "Please confirm that this email address is correct.\n%@", "alert.email.check.exist": "Please check the spam/trash/promotions box.", "alert.login.success": "Sign in success", "alert.wish.del.msg": "Remove this book from the wishlist?", "alert.wish.del.do": "Remove", "alert.wish.del.cancel": "Cancel", "alert.book.sample.none": "The reading sample is currently not available.", "alert.sample.bookshelf.msg": "Add to your library if you like it", "alert.sample.bookshelf.do": "Ok", "alert.sample.bookshelf.cancel": "Close", "alert.sample.wish.msg": "You have reached the end of the book sample. Add it to your wishlist if you like it.", "alert.sample.wish.do": "Add to wishlist", "alert.sample.wish.cancel": "Close", "alert.sample.wish.success": "Added to wishlist successfully.", "alert.access.photo.title": "This lets you share from your camera roll and enables other features for photos. Go to your settings and tap \"Photos\".", "alert.access.not.now": "Not Now", "alert.access.open.setting": "Open Settings", "alert.read.sync.title": "Continue reading", "alert.read.sync.msg": "Go to the most resent location on other device: %@ %@", "alert.read.sync.yes": "Yes", "alert.read.sync.no": "No", "alert.groupname.sync.title": "Upload current book grouping information to the cloud?", "alert.groupname.sync.msg": "Other devices can sync the grouping information in the cloud after updating the app.", "alert.groupname.sync.yes": "Upload", "alert.groupname.sync.no": "No", "alert.reader.tts.redownload": "Please remove the book from your library, then go to \"Me>Books\" and add it to library again, if you still can't listen, it means the book is not authorised by the copyright holder to use TTS function.", "alert.reader.tts.not": "At the request of the copyright holder, this book is not allowed to use TTS function.", "alert.reader.tts.finish": "This chapter is finished.", "alert.reader.tts.sample": "Sorry, <PERSON><PERSON> can't be read by using TTS function.", "btnLogin": "login", "error.email.null": "Account number is not allowed to be empty！", "error.passwd.null": "Password is not allowed to be empty！", "error.email.validate": "The email address is in wrong format, please re-enter.", "error.email.exist": "An account is already registered with that email address, please sign in.", "error.nickname.null": "<PERSON><PERSON><PERSON> can't be empty!", "error.nickname.invalid": "Username length should be within 14 characters.", "error.email.invalid": "The email address you entered is incorrect! Please re-enter.", "success.email.send": "We have sent a verification email to your email address.", "error.passwd.format": "Password must be composed of 6-20 English letters, numbers or symbols.", "error.passwd.again": "The password entered twice does not match, please re-enter!", "error.http.request": "Network connection failure！", "error.book.fail": "Book opening failure", "error.email.not.exist": "Account number does not exist, please re-enter", "error.email.toomuch": "You can request a resend after 10 minutes", "error.search.empty": "Search content cannot be empty", "error.param.invalid": "Parameter error", "error.login.invalid": "Wrong account or password, please re-enter", "error.request.invalid": "Illegal requests", "error.active.exceed": "You can sign in to a maximum of 5 devices and you have exceeded this limit. Please unlink the device that is not in use on the website.", "error.login.not.active": "This account has not verified email, please click the verification link in the email.", "error.highlight.book.null": "Download the book to view notes details?", "error_title": "Warning", "error.note.exceed": "Notes should not exceed 2000 characters", "error.download.fail": "Download Failure", "error.book.presale": "This book is on pre-order.", "error.book.page.number.no": "This book does not support the display of paperback page numbers", "error.groupname.exist": "This name already exists", "error.groupname.toolong": "Collection name up to 20 characters", "error.spirit.note.exceed": "Notes for a single module should not exceed 500 characters", "book.unread": "Unread", "book.read.to": "Read unto%.1f", "book.buy": "Purchased on %@", "book.author": "Author", "book.publishName": "Publisher", "bookShelf.checked": "Selected (%i) ", "bookShelf.remove.success": "The selected book(s) has been successfully removed", "bookShelf.base": "Library", "btn.bookShelf.remove": "Remove Download", "btn.bookShelf.move": "Move to Collection", "btn.bookShelf.add": "Download", "btn.all.none": "Unselect All", "btn.all.do": "Select All", "btn.abandon": "Discard", "btn.book.add": "Download", "btn.book.presale": "Pre-order", "btn.book.suspend": "Download Paused", "btn.book.resume": "Continue", "btn.book.downloading": "Downloading", "btn.del": "Delete", "btn.book.open": "Open", "btn.book.update": "Update", "btn.confirm": "Confirm", "btn.cancel": "Cancel", "btn.chapter.next": "Next", "btn.chapter.prev": "Previous", "btn.empty": "Clear", "btn.exit": "Sign out", "btn.go": "go", "btn.remove": "Remove", "btn.shift.out": "Remove", "ok_button": "OK", "btn.allow.all": "Always Allow", "btn.just.read": "No login for now，Take a look around >", "btn.restart": "<PERSON><PERSON>", "btn.spirit.add.success": "Adding Devotional Program Success！", "btn.skip": "<PERSON><PERSON>", "btn.continue": "Continue", "btn.punch.in": "Punch in", "btn.email.resend": "Resend", "btn.correct": "OK", "btn.edit": "Edit", "btn.wish.remove": "Remove from wishlist", "btn.wish.add": "Add to wishlist", "btn.read": "Read", "btn.preview": "<PERSON><PERSON>", "btn.get": "Get", "btn.jump": "Yes", "export.header": "My devotional notes", "export.from": "From Inspirata eBooks", "export.date": "Exporting Date", "export.update": "Update at", "export.book.header": "My Book Notes", "export.highlight": "Original text", "export.annotation": "Notes", "export.note": "Note: Export up to 20 characters from the underlined portion of the original text for each note.", "export.file": "Inspirata eBooks Notes", "Highlights": "Highlight", "Highlight": "Underline", "Define": "Define", "Note": "Note", "Play": "Play", "Save": "Save", "Close": "Close", "Cancel": "Cancel", "Copy": "Copy", "Share": "Share", "Take Photo": "Take photo", "Choose existing": "Choose from gallery", "Gallery": "Gallery", "Next": "Next", "Done": "done", "save.to.album": "Save to album", "label.searchHistoryHeader": "Search History", "label.hotSearchHeader": "Popular Searches", "label.terms.of.use": "Terms of Use", "label.privacy.policy": "Privacy Policy", "language.set": "Set up Success", "login.email": "Email", "login.passwd": "Password", "loading.footer.idle": "Click or scroll up to load more", "loading.footer.pulling": "Loose immediately to load more", "loading.footer.refresh": "Loading more data", "loading.footer.noMoreData": "No more data", "loading.header.pulling": "Release for immediate refresh", "loading.header.idle": "Scroll down to refresh", "loading.header.refresh": "Refreshing data in progress", "msg.bookshelf.none": "Library is empty", "msg.bookshelf.none.red": "My Library", "msg.book.none": "Here is empty~\nPlease go to discovery", "msg.book.none.red": "discovery", "msg.bookstore.none": "Go to Inspirata eBooks platform to purchase books", "msg.bookstore.none.red": "Inspirata eBooks platform", "msg.shop.category.none": "This category is empty at the moment\nPlease check other categories", "msg.shop.category.none.red": "我的图书", "msg.cart.none": "This function is not open yet", "msg.note.none": "No notes available for now", "msg.reader.marklist.none.title": "No bookmarks added yet", "msg.reader.marklist.none.description": "Click on\"Bookmark\"icon on the reading page to add", "msg.reader.notelist.none.title": "No notes or underlines added yet", "msg.reader.notelist.none.description": "Long click on the text on the reading page to add notes or underlines", "msg.wishes.none": "No books available for now", "msg.search.none": "No relevant results found", "msg.copy.done": "<PERSON>pied", "msg.clean.done": "Clear successfully", "msg.send.done": "Resent successfully!", "msg.function.none": "This function is not open yet", "msg.book.download": "“Download” will allow files to be download to local. Allow this operation to continue?", "msg.book.import.del": "Gift books do not support cloud sync function at the moment, underlines and notes are only saved in the current device and will be deleted after moving out of the library. Would you like to allow this operation?", "msg.name.not.login": "Sign in or sign up", "msg.email.not.login": "Sign in for more features", "msg.spirit.not.login": "Sign in or sign up to view or subscribe a devotional plan!", "msg.spirit.not.login.red": "Sign in or sign up", "msg.spirit.none": "You have not subscribe any devotional plan yet.\n\nGo to \"All Devotionals\" \n\nto subscribe a devotional plan!", "msg.spirit.none.red": "All Devotionals", "msg.explanatory": "Explanations", "msg.verify.email": "The verification email has been sent to\n%@\nPlease click the verification link in the \nemail in 1 hour.", "msg.verify.email.red": "1 hour", "msg.wishlist.none": "Wishlist is empty\nPlease go to discovery", "msg.wishlist.none.red": "discovery", "msg.book.download.success": "Download success", "note.update": "update at %@", "note.total": "%i in total", "note.from": "Quote from", "quote.from": "Quote from / %@", "quote.bottom": "Inspirata eBooks\nendao.co", "R2Navigator.NavigatorError.EditingAction.share": "Share", "operation.success": "Success", "OK": "OK", "passwd.forget.title": "Verify email", "passwd.forget.msg": "\nThe verification email has been sent to %@, please go to mailbox for following-up.\nNote: Verification email could be filtered as promotional or spam, and is only valid for 1 hour. You can request to resend a verification email after 10 minutes.", "PYSearchSearchHistoryText": "Recent Searches", "PYSearchHotSearchText": "popular search", "reader.bookmarker": "bookmark", "reader.catalog": "catalog", "reader.note": "note", "reader.note.edit": "Edit Notes", "reader.light": "light", "reader.background": "background", "reader.font": "font", "reader.page": "%ipage", "reader.read.speed": "Speed", "reader.read.voice": "Voice", "reader.read.voice.1": "<PERSON><PERSON>(M)", "reader.read.voice.2": "<PERSON><PERSON>(F1)", "reader.read.voice.3": "<PERSON><PERSON>(F2)", "reader.read.voice.4": "<PERSON><PERSON>(HK)", "reader.read.skip": "<PERSON><PERSON>", "reader.read.skip.1": "Brackets", "reader.read.skip.2": "Tables", "reader.read.exit": "Exit", "result.none": "No relevant results found", "S01": "This request is invalid.", "S02": "This data is invalid.", "S03": "Parameter error", "S08": "Parameter error", "S09": "System anomaly", "S10": "Please sign in again.", "S11": "Account status is abnormal, please sign in again!", "S12": "You App is expired, please update as soon as possible.", "S13": "Download failed, please try again!", "search.placeholder": "Search: book title/author", "search.book.text.placeholder": "Search for a word or phrase", "searchHistory.empty.confirm": "Clear the search history？", "search.result": "%i result(s) found", "setting.version": "Current Version %@ ", "setting.language": "Language", "setting.aboutUs": "Inspirata eBook is the Christian eBook reading platform developed by Inspirata Publishing (Hong Kong) Limited, which aims to promote the digitalization of Chinese Christian resources through cooperation with Christian publishers, and to help Chinese Christians around the world to access and read Christian books more conveniently.", "shop.nav.home": "Home", "shop.nav.all": "All Titles", "shop.nav.inspirata": "Inspirata", "shop.nav.free": "Free", "shop.nav.sc": "CHS", "shop.nav.tc": "CHT", "shop.nav.eng": "English", "shop.nav.presale": "Pre-order", "shop.category.editor": "Editors' Picks", "shop.category.all": "All", "shop.category.theology": "Theology", "shop.category.bible.study": "Bible Study", "shop.category.bible.ot": "Bible Commentaries-OT", "shop.category.bible.nt": "Bible Commentaries-NT", "shop.category.church.ministry": "Church Ministry", "shop.category.mission": "Mission/Evangelism", "shop.category.devotionals": "Devotionals", "shop.category.christian.life": "Christian Life", "shop.category.marriage": "Marriage/Parenting", "shop.category.history": "History", "shop.category.biography": "Biography", "shop.category.religion": "Religion & Other Disciplines", "spirit.days": "# %i day", "spirit.days.finished": "Punched in for %i day", "spirit.days.finished.muti": "Punched in for %i days", "spirit.read": "%@/%i", "spirit.addToPlan": "Subscribe", "spirit.hasAdd": "Subscribed", "spirit.bookDescription": "Introduction", "spirit.authorDescription": "Author", "spirit.copyright": "Copyright Information", "spirit.think.placeholder": "Pay attention to and chew on the phrases, passages, or images that bring you the most insight, and honestly record your reflections on the scriptures.", "spirit.pray.placeholder": "Bring your own reflection of the passage to <PERSON> as you worship, confess, renew, plead, intercede, and give thanks through prayer.", "spirit.apply.placeholder": "Be silent and obedient before <PERSON>, focusing on the \"essence\" of the passage and recording your insights.", "spirit.think": "Reflection", "spirit.pray": "Prayer", "spirit.apply": "Contemplation", "title.endao": "Inspirata eBooks", "title.auth": "Verify your Account", "title.groupName.input": "Create new collection", "title.name.input": "Please enter a name", "title.spirit.write": "Write Devotional Notes", "user_settings_appearance_default_a11y_label": "<PERSON><PERSON><PERSON>", "user_settings_appearance_sepia_a11y_label": "Eye Rest Mode", "user_settings_appearance_night_a11y_label": "Dark Mode", "ReadiumLCP.LCPError.licenseIsBusy": "Temporarily unable to authenticate", "ReadiumLCP.LCPError.licenseIntegrity": "Authentication failed, please download again after deleting the book: %@", "ReadiumLCP.LCPError.licenseContainer": "Cannot authenticate properly", "ReadiumLCP.LCPError.licenseInteractionNotAvailable": "Temporarily unable to authenticate", "ReadiumLCP.LCPError.licenseProfileNotSupported": "Authentication file is illegal", "ReadiumLCP.LCPError.crlFetching": "Cannot get the authentication file information normally", "ReadiumLCP.LCPError.parsing": "Authentication parsing failed", "ReadiumLCP.LCPError.network": "Authentication network abnormal", "ReadiumLCP.LCPClientError.licenseOutOfDate": "Authentication expired", "ReadiumLCP.LCPClientError.certificateRevoked": "Authentication has expired", "ReadiumLCP.LCPClientError.certificateSignatureInvalid": "Illegal authentication", "ReadiumLCP.LCPClientError.licenseSignatureDateInvalid": "Authentication has expired", "ReadiumLCP.LCPClientError.licenseSignatureInvalid": "Authentication mismatch", "ReadiumLCP.LCPClientError.contextInvalid": "Authentication Exception", "ReadiumLCP.LCPClientError.contentKeyDecryptError": "Authentication Match Exception", "ReadiumLCP.LCPClientError.userKeyCheckInvalid": "Authentication key value is illegal", "ReadiumLCP.LCPClientError.contentDecryptError": "Authentication content cannot be parsed properly", "ReadiumLCP.LCPClientError.unknown": "Other errors in authentication", "ReadiumLCP.StatusError.cancelled": "Authentication has been cancelled %@.", "ReadiumLCP.StatusError.returned": "Authentication has been fed back %@.", "ReadiumLCP.StatusError.expired.start": "Authentication status %@.", "ReadiumLCP.StatusError.expired.end": "Book has expired", "ReadiumLCP.StatusError.revoked": "Authentication cancelled on %1$@. \n was registered on %2$d devices", "ReadiumLCP.RenewError.renewFailed": "Book renewal failed.", "ReadiumLCP.RenewError.invalidRenewalPeriod": "Illegal renewal period, renewal failed", "ReadiumLCP.RenewError.unexpectedServerError": "Renewal error", "ReadiumLCP.ReturnError.returnFailed": "Book does not feedback properly", "ReadiumLCP.ReturnError.alreadyReturnedOrExpired": "Book has been fed back or has expired", "ReadiumLCP.ReturnError.unexpectedServerError": "Book feedback failed", "R2Navigator.NavigatorError.copyForbidden": "You exceeded the amount of characters allowed to be copied.", "R2Navigator.EditingAction.share": "Share", "3dtouch.share": "Share", "3dtouch.me": "My", "3dtouch.bookShelf": "Bookshelf", "3dtouch.discovery": "Discovery", "3dtouch.admin": "Admin", "3dtouch.spirit": "Devotion", "error.rent.order.exist": "您已訂閱當前套裝，請勿重複訂閱", "error.rent.order.not.pay": "您有待支付訂閱，請前往“我的>我的訂閱”頁面中處理", "error.pay.fail": "⽀付失敗", "error.card.invalid": "⾮法卡號", "msg.pay.success": "⽀付成功", "error.rent.expired": "訂閱已經到期"}