.endao-el-loading-parent--relative {
    position: relative !important
}

.endao-el-loading-parent--hidden {
    overflow: hidden !important
}

.endao-el-loading-mask {
    position          : absolute;
    z-index           : 2000;
    background-color  : rgba(255, 255, 255, .9);
    margin            : 0;
    top               : 0;
    right             : 0;
    bottom            : 0;
    left              : 0;
    -webkit-transition: opacity .3s;
    transition        : opacity .3s
}

.endao-el-loading-mask.is-fullscreen {
    position: fixed
}

.endao-el-loading-mask.is-fullscreen .endao-el-loading-spinner {
    margin-top: -25px
}

.endao-el-loading-mask.is-fullscreen .endao-el-loading-spinner .circular {
    height: 50px;
    width : 50px
}

.endao-el-loading-spinner {
    top       : 50%;
    margin-top: -21px;
    width     : 100%;
    text-align: center;
    position  : absolute
}

.endao-el-loading-spinner .endao-el-loading-text {
    color    : #cc4646;
    margin   : 3px 0;
    font-size: 14px
}

.endao-el-loading-spinner .circular {
    height           : 42px;
    width            : 42px;
    -webkit-animation: loading-rotate 2s linear infinite;
    animation        : loading-rotate 2s linear infinite
}

.endao-el-loading-spinner .path {
    -webkit-animation: loading-dash 1.5s ease-in-out infinite;
    animation        : loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray : 90, 150;
    stroke-dashoffset: 0;
    stroke-width     : 2;
    stroke           : #cc4646;
    stroke-linecap   : round
}

.endao-el-loading-spinner i {
    color: #cc4646
}

.endao-el-loading-fade-enter,
.endao-el-loading-fade-leave-active {
    opacity: 0
}

@-webkit-keyframes loading-rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform        : rotate(360deg)
    }
}

@keyframes loading-rotate {
    100% {
        -webkit-transform: rotate(360deg);
        transform        : rotate(360deg)
    }
}

@-webkit-keyframes loading-dash {
    0% {
        stroke-dasharray : 1, 200;
        stroke-dashoffset: 0
    }

    50% {
        stroke-dasharray : 90, 150;
        stroke-dashoffset: -40px
    }

    100% {
        stroke-dasharray : 90, 150;
        stroke-dashoffset: -120px
    }
}

@keyframes loading-dash {
    0% {
        stroke-dasharray : 1, 200;
        stroke-dashoffset: 0
    }

    50% {
        stroke-dasharray : 90, 150;
        stroke-dashoffset: -40px
    }

    100% {
        stroke-dasharray : 90, 150;
        stroke-dashoffset: -120px
    }
}

