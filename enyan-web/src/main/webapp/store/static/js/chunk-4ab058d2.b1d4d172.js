(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4ab058d2"],{"7cb1":function(t,s,e){"use strict";e("b961")},"80fc":function(t,s,e){"use strict";e.r(s);var a=function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",{staticClass:"container padding-bottom-3x mb-2 padding-top-2x",staticStyle:{"padding-left":"0","padding-right":"0"}},[e("div",{staticClass:"card text-center",staticStyle:{"border-radius":"7px","border-color":"#e1e7ec"}},[e("div",{staticClass:"card-block padding-top-2x"},[e("h3",{directives:[{name:"show",rawName:"v-show",value:"add"===t.type,expression:"type === 'add'"}],staticClass:"card-title"},[t._v(t._s(t.$t("message.paySuccess.rentSuccess")))]),t._v(" "),e("h3",{directives:[{name:"show",rawName:"v-show",value:"goOn"===t.type,expression:"type === 'goOn'"}],staticClass:"card-title"},[t._v(t._s(t.$t("message.paySuccess.hasPaid")))]),t._v(" "),e("p",{staticClass:"card-text mb-10"},[t._v(t._s(t.$t("message.paySuccess.downOrOpen"))),e("span",{staticClass:"text-medium"},[t._v(" "),e("a",{attrs:{href:"index-Reader"}},[t._v(t._s(t.$t("message.appName")))]),t._v(" ")]),t._v(t._s(t.$t("message.paySuccess.toRead")))]),t._v(" "),e("p",{staticClass:"card-text"},[t._v("\n        "+t._s(t.$t("message.paySuccess.manageRent1"))+"\n        "),e("span",{staticClass:"myRentUrl",on:{click:function(s){return t.$router.push({path:"/rent/list"})}}},[t._v(t._s(t.$t("message.myRentList")))]),t._v("\n        "+t._s(t.$t("message.paySuccess.manageRent2"))+"\n      ")]),t._v(" "),e("p",[t._v(" ")])])])])},c=[],n=e("f435"),i=n["a"],r=(e("7cb1"),e("2877")),o=Object(r["a"])(i,a,c,!1,null,"14313c78",null);s["default"]=o.exports},b961:function(t,s,e){},f435:function(t,s,e){"use strict";(function(t){var a=e("ed08");s["a"]={data:function(){return{type:""}},watch:{"$store.state.app.lang":{handler:function(){Object(a["a"])(this.$t("message.metaTitle.paySuccess"))}}},created:function(){Object(a["a"])(this.$t("message.metaTitle.paySuccess")),t("footer").css("display","block"),t(".scroll-to-top-btn").css("display","block"),this.type=this.$route.params.type},methods:{}}}).call(this,e("1157"))}}]);