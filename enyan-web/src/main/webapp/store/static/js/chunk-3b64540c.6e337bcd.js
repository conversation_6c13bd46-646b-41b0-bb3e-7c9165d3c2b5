(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3b64540c"],{6411:function(t,a,e){"use strict";e("dd74")},a36b:function(t,a,e){t.exports=e.p+"static/img/404.fae582ec.png"},dd74:function(t,a,e){},f207:function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"container padding-top-3x padding-bottom-3x mb-1 page_404"},[s("img",{staticClass:"d-block m-auto",staticStyle:{width:"100%","max-width":"350px"},attrs:{src:e("a36b"),alt:"404"}}),t._v(" "),s("div",{staticClass:"padding-top-2x mt-2 text-center"},[s("h6",{staticClass:"text-muted"},[t._v(t._s(t.$t("message.nav.pageLost")))])]),t._v(" "),s("div",{staticClass:"text-center"},[s("div",{staticClass:"column thisColumn"},[s("el-button",{staticStyle:{"margin-top":"8px","margin-right":"5px",padding:"12px 31px",width:"auto",height:"44px","line-height":"10px","text-transform":"uppercase"},attrs:{type:"danger",round:""},on:{click:t.goBackHome}},[t._v(t._s(t.$t("message.nav.backHomePage")))]),t._v(" "),s("el-button",{staticClass:"backBtn2",staticStyle:{"margin-top":"8px",padding:"12px 31px",width:"auto",height:"44px","line-height":"10px","text-transform":"uppercase"},attrs:{type:"danger",round:""},on:{click:t.goBackPrevious}},[t._v(t._s(t.$t("message.nav.backPreviousPage")))])],1)])])},i=[],n={name:"Page404",methods:{goBackHome:function(){window.location.href="/"},goBackPrevious:function(){window.history.go(-1)}}},o=n,c=(e("6411"),e("2877")),p=Object(c["a"])(o,s,i,!1,null,"b6b6e44e",null);a["default"]=p.exports}}]);