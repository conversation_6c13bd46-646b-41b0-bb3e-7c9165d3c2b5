(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-95349ce0"],{"533e":function(t,e,a){},5944:function(t,e,a){"use strict";var s=a("533e"),n=a.n(s);n.a},d306:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container continuePay-container"},[a("ol",{staticClass:"list-unstyled"},[a("li",{staticClass:"li-item"},[a("span",{staticClass:"text-muted"},[t._v(t._s(t.$t("message.orderNum"))+": ")]),t._v(t._s(t.payData.orderNum))])]),t._v(" "),a("hr"),t._v(" "),a("p",{staticClass:"title mb-10"},[t._v(t._s(t.$t("message.rentToBuy"))+"·"+t._s(t.$t("message.fangjiaoshi"))+t._s(t._f("rentTypeName")(t.payData.rentType))+t._s(t.$t("message.fullPackage"))+"("+t._s(t._f("rentLangName")(t.payData.rentLang))+")·"+t._s(t._f("autoName")(t.payData.isAuto)))]),t._v(" "),a("p",{staticClass:"autoInfo"},[t._v(t._s(t.$t("message.pay.rangeTime1"))+"："+t._s(t.months)+t._s(t.$t("message.pay.rangeTime2")))]),t._v(" "),0===t.payData.isAuto?a("div",[a("p",{staticClass:"autoInfo mb-10"},[t._v(t._s(t.$t("message.expiredTime"))+"："+t._s(t.expiredDate))]),t._v(" "),a("p",{staticClass:"autoInfo endaoRed"},[t._v(t._s(t.$t("message.pay.beforeExpiredToGoOn")))])]):t._e(),t._v(" "),1===t.payData.isAuto?a("div",[a("p",{staticClass:"autoInfo mb-10"},[t._v(t._s(t.$t("message.autoDeductTime"))+"："+t._s(t.feeDeductionDate))]),t._v(" "),a("p",{staticClass:"autoInfo endaoRed"},[t._v(t._s(t.$t("message.pay.unAutoRentAlert")))])]):t._e(),t._v(" "),a("hr"),t._v(" "),a("div",{staticClass:"cost"},[a("p",[t._v(t._s(t.$t("message.shouldPay"))+"：HK$"+t._s(t._f("twoDecimal")(t.shouldPayHK(t.payData.isAuto,t.payData.rentType))))]),t._v(" "),a("p",[t._v("("+t._s(t.$store.state.app.currencySign)+t._s(t._f("twoDecimal")(t.shouldPayCurrency(t.payData.isAuto,t.payData.rentType)))+")")])]),t._v(" "),a("pay-type",{attrs:{"is-auto":t.payData.isAuto,"order-num":t.payData.orderNum,"rent-months":t.months,"cost-hk":t.shouldPayHK(t.payData.isAuto,t.payData.rentType),type:"goOn","back-name":t.$t("message.pay.cancelGoOnRent")}})],1)},n=[],o=a("de17"),r=o["a"],c=(a("5944"),a("2877")),i=Object(c["a"])(r,s,n,!1,null,"5caae411",null);e["default"]=i.exports},de17:function(t,e,a){"use strict";(function(t){a("28a5");var s=a("c1df"),n=a.n(s),o=a("cff8"),r=a("3de4");e["a"]={components:{PayType:o["a"]},data:function(){return{currency:"CNY",payData:null,months:0,expiredDate:"",feeDeductionDate:"",costAuto:[{rentType:1,costHK:60,currency:55},{rentType:2,costHK:35,currency:30},{rentType:3,costHK:25,currency:20}],costNotAuto:[{rentType:1,costHK:65,currency:60},{rentType:2,costHK:40,currency:35},{rentType:3,costHK:30,currency:25}]}},computed:{shouldPayHK:function(){return function(t,e){return 1===t?this.months*this.costAuto[e-1].costHK:this.months*this.costNotAuto[e-1].costHK}},shouldPayCurrency:function(){return function(t,e){return 1===t?this.months*this.costAuto[e-1].currency:this.months*this.costNotAuto[e-1].currency}}},watch:{"$store.state.app.currency":{immediate:!1,handler:function(t){this.currency=t,this.getCurrencyData()}}},created:function(){t("footer").css("display","block");var e=sessionStorage.getItem("continuePayData");e?this.payData=JSON.parse(e):(this.payData=this.$route.params.item,sessionStorage.setItem("continuePayData",JSON.stringify(this.$route.params.item))),this.months=this.payData.months,this.expiredDate=n()(this.payData.expiredAt).add(this.months,"months").format("YYYY-MM-DD"),this.feeDeductionDate=n()(this.expiredDate).subtract(1,"days").format("YYYY-MM-DD"),this.$cookies.get("Cookie_Currency")&&(this.currency=this.$cookies.get("Cookie_Currency").split(/[(（]/)[0]),this.getCurrencyData()},beforeDestroy:function(){sessionStorage.removeItem("continuePayData")},methods:{getCurrencyData:function(){var t=this;Object(r["e"])(this.currency).then(function(e){e.success&&(t.costAuto=t.costAuto.map(function(t){return e.result.autoList.map(function(e){return t.rentType===e.rentType&&(t.costHK=e.totalFee,t.currency=e.totalFeeCurrency),e}),t}),t.costNotAuto=t.costNotAuto.map(function(t){return e.result.manualList.map(function(e){return t.rentType===e.rentType&&(t.costHK=e.totalFee,t.currency=e.totalFeeCurrency),e}),t}))})}}}}).call(this,a("1157"))}}]);