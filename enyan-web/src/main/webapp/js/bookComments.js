function loadAllComments(api, bookId, loginStatus, loginSex, lang) {
    let isLogin = !!Number(loginStatus)
    let loginNickName = ''
    if (isLogin) {
		$('.hasLogined').css('display', 'block')
        $('#loginSexImg').attr('src', `img/${loginSex == 0 ? 'defaultAvatar.svg' : loginSex == 1 ? 'male.svg': 'female.svg'}`)
        // loginNickName = JSON.parse(localStorage.getItem('user'))?.name || ''
        // $('#cur_username').text(loginNickName)
	} else {
		$('.hasNotLogined').css('display', 'block')
        $('#cur_username').text('')
    }
    let langData = {}
    $.ajax({
		url: `js/${lang}.json`,
		dataType: 'json',
		async: false,
		success: function(data) {
            langData = { ...data }
            window.langData = langData
            setLanguage(data)
		},
    })
    const isEnglishLang = lang === 'en_US' ? true : false;
    isEnglishLang ? $('#text-count').text(`0/2000`) : void 0;
    const userEmail = JSON.parse(localStorage.getItem('user'))?.email || ''
	let totalPage = 1
    let localDelNum = 0
    // 点击跳转到评论标签页
    $(document).on('click', '.turnReviews', function() {
        $('#navTabContainer a[href="#reviews"]').tab('show')
        let targetOffset = $('#navTabContainer').offset().top
        let headerHeight = $('header').height()
        $('html, body').animate({
          scrollTop: targetOffset - headerHeight
        }, 500)
	})
	// 获取评论列表
	getCommentsList(1, 0)
	async function getCommentsList(page = 1, parentId) {
		const listRes = await getData(`${api}/commentPage`, {
			page,
			bookId,
			parentId,
		}, userEmail)
        listRes.totalPage > totalPage ? $('.viewMore').css('display', 'block') : $('.viewMore').css('display', 'none')
        $('#allCommentsCount').text(`(${listRes.totalRecord - localDelNum})`)
        const commentsListData = listRes.result // app会提供数据
		if (!listRes.totalRecord) {
			$('.noComment').css('display', 'block')
        } else {
            $('.noComment').css('display', 'none')
			$.each(commentsListData, function(index, comment) {
				let li = `
                        <li>
                            <div class="comment" style="margin-bottom: 0;padding-bottom: 16px;">
                                <div class="comment-author-ava"><img src="img/${comment.sex == 0 ? 'defaultAvatar.svg' : comment.sex == 1 ? 'male.svg' : 'female.svg'}" alt="gender"></div>
                                <div class="comment-body">
                                  <div class="comment-header d-flex flex-wrap justify-content-between">
                                    <div class="comment-title parentCommentTitle"><span class="nickName">${comment.nickName}</span>
                                        ${
											comment.star > 0 && !comment.isDeleted
												? Array(Number(comment.star))
														.fill()
														.map(() => '<svg t="1685069782568" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1342" width="20" height="20"><path d="M512 734.423579l249.802105 150.743579-66.290526-284.133053L916.210526 409.815579l-290.654315-24.68379L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579z" fill="#EB4742" p-id="1343"></path></svg>')
														.join('')
												: ''
										}${
											comment.star > 0 && comment.star < 5 && !comment.isDeleted
												? Array(5 - Number(comment.star))
														.fill()
														.map(() => '<svg t="1685069843133" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1477" width="20" height="20"><path d="M916.210526 409.815579l-290.654315-25.061053L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579l249.802105 150.743579-65.913263-284.133053L916.210526 409.815579zM512 658.809263l-151.983158 91.782737 40.421053-173.002105-134.197895-116.412632 177.044211-15.413895L512 282.947368l69.146947 163.301053 177.044211 15.36-134.197895 116.412632 40.421053 173.002105L512 658.809263z" fill="#EB4742" p-id="1478"></path></svg>')
														.join('')
												: ''
										}
                                        ${comment.title && !comment.isDeleted ? `<span class="text-muted commentTitleText" style="color: #CFCFCF !important"> | </span><span class="commentTitleText commentTitleContent">${comment.title}</span>` : ''}
                                    </div>
                                    <div class="mb-2">
                                        <div class="text-muted text7A" style="font-size: 12px;">${formatDate(comment.createAt)}</div>
                                    </div>
                                  </div>
                                  <p class="comment-text parentCommentText">${comment.isDeleted ? `<span class="text7A">${langData.reviewDeleted}</span>` : comment.content}</p>
                                </div>
                                <div class="comment-footer mt-2">
                                    <div class="column">
                                        <span class="comment-meta">
                                        ${ true ? 
                                            `<span>    
                                              <img src="./img/favorite.svg" class="like" alt="favorite" data-dataid="${comment.dataId}">
                                              ${comment.likeCount ? `<span class="likeCount-${comment.dataId} text7A">${comment.likeCount}</span>` : ''}
                                            </span>
                                            <span class="commentMarkWrapper-${comment.dataId}">
                                              <img src="./img/comment.svg" class="ml-4 commentMark" alt="review" data-dataid="${comment.dataId}"
                                                data-nickname="${comment.nickName}">${
								                	comment.commentCount && comment.commentCount > 0 ? `<span class="commentCount-${comment.dataId} text7A">${comment.commentCount}</span>` : ''
								                }
                                            </span>`
                                            : ''}
                                        </span>
                                    </div>
                                    <div class="column">
                                        <span class="comment-meta">
                                            ${
												comment.isMine && isLogin
													? `<a href="javascript:;" data-toggle="modal" data-target="#deleteCommentModal">
                                                        <img src="./img/delete.svg" alt="delete" class="delComment" data-dataid="${comment.dataId}"></a>`
													: ''
											}
                                        </span>
                                    </div>
                                </div>
                                <div class="subComments-${comment.dataId}" style="display: none;"></div>
                            </div>
                        </li>
                    `
				$('#comments-list').append(li)
			})
		}
	}

	// 获取子评论列表
	async function getSubList(dom, page = 1, parentId, parentNickName) {
		const subListRes = await getData(`${api}/commentPage`, {
			page,
			bookId,
			parentId,
		}, userEmail)
        const subListData = subListRes.result
        if (subListData.length) { // 更新评论回复条数
            if (page === 1) { // 处理本地删除回复条数
                const parentEl = $(`.commentMarkWrapper-${parentId}`)
                if (!parentEl.has('span').length) { // 无回复时新增第一条回复
                    $('<span>').appendTo(parentEl).text(subListRes.totalRecord).addClass(`commentCount-${parentId}`);
                } else { // 已有回复条数
                    $(`.commentCount-${parentId}`).text(subListRes.totalRecord)
                }
            }
        }
		$.each(subListData, function(index, subItem) {
			let li = `
                    <li>
                        <div class="comment comment-reply">
                          <div class="comment-author-ava comment-author-ava-reply"><img src="img/${subItem.sex == 0 ? 'defaultAvatar.svg' : subItem.sex == 1 ? 'male.svg' : 'female.svg'}" alt="gender"></div>
                          <div class="comment-body">
                            <div class="comment-header d-flex flex-wrap justify-content-between" style="margin-bottom: -4px;">
                              <h4 class="comment-title">${subItem.nickName}<span class="text-muted text7A">&nbsp;${langData.reply}&nbsp;</span>${parentNickName}：</h4>
                              <div class="mb-2">
                                <div class="text-muted text7A" style="font-size: 12px;">${formatDate(subItem.createAt)}</div>
                              </div>
                            </div>
                            <p class="comment-text commentText">${subItem.content}</p>
                          </div>
                          <div class="comment-footer mt-1">
                            <div class="column">
                              <span class="comment-meta">
                                <img src="./img/favorite.svg" class="subLike" alt="favorite" data-dataid="${subItem.dataId}">
                                ${subItem.likeCount ? `<span class="subLikeCount-${subItem.dataId}">${subItem.likeCount}</span>` : ''}
                              </span>
                            </div>
                            <div class="column">
                              <span class="comment-meta">
                                ${
									subItem.isMine && isLogin
										? `<a href="javascript:;" data-toggle="modal" data-target="#deleteCommentModal"><img src="./img/delete.svg" alt="delete" class="subDelComment"
                                        data-dataid="${subItem.dataId}" data-parentid="${parentId}" data-parentnickname="${parentNickName}"></a>`
										: ''
								}
                              </span>
                            </div>
                          </div>
                          <hr>
                        </div>
                    </li>
            `
			dom.append(li)
		})
		if (subListRes.totalPage > page) {
			if (!$(`.sub-arrow-down-${parentId}`).length) {
				// 元素不存在
				dom.append(
					`<p class="sub-arrow-down-${parentId}" data-parentid="${parentId}" data-parentnickname="${parentNickName}" data-activepage="${page}">
                        <span class="viewMoreSub">${langData.viewMore2}</span>
                    </p>`
				)
			} else {
				$(`.sub-arrow-down-${parentId}`).data('activepage', page)
				$(`.sub-arrow-down-${parentId}`).appendTo(dom)
				$(`.sub-arrow-down-${parentId}`).css('display', 'block')
			}
		} else {
			$(`.sub-arrow-down-${parentId}`).css('display', 'none')
		}
	}

	// 点击查看更多评论
	$(document).on('click', '.viewMore', function() {
		totalPage += 1
		getCommentsList(totalPage, 0)
	})

	// 点击查看更多回复
	$('#comments-list').on('click', '[class^="sub-arrow-down-"]', function() {
		const parentId = $(this).data('parentid')
		const parentNickName = $(this).data('parentnickname')
		const activePage = $(this).data('activepage')
		getSubList($(`.subComments-${parentId} ul`), activePage + 1, parentId, parentNickName)
	})

	// 点击星形评分
	var starScore = 0
	$('#starMark').on('click', 'p', function() {
		let index = $(this).index()
        $(this).find('span').css('visibility', 'visible')
        $(this).siblings().find('span').css('visibility', 'hidden')
		starScore = index
		$('#starMark p:lt(' + (index + 1) + ') svg').html(
			'<path d="M512 734.423579l249.802105 150.743579-66.290526-284.133053L916.210526 409.815579l-290.654315-24.68379L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579z" fill="#EB4742" p-id="1208"></path>'
		)
		$('#starMark p:gt(' + index + ') svg').html(
			'<path d="M916.210526 409.815579l-290.654315-25.061053L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579l249.802105 150.743579-65.913263-284.133053L916.210526 409.815579zM512 658.809263l-151.983158 91.782737 40.421053-173.002105-134.197895-116.412632 177.044211-15.413895L512 282.947368l69.146947 163.301053 177.044211 15.36-134.197895 116.412632 40.421053 173.002105L512 658.809263z" fill="#EB4742" p-id="1073"></path>'
		)
	})

	// 监听发表内容输入
	$('#commentContents').on('input propertychange', function() {
        let contents = $(this).val()
        if (isEnglishLang) {
			$('#text-count').text(`${contents.length}/2000`)
        } else { 
            let remainNum = 2000 - contents.length
			$('#text-count').text(remainNum)
        }
	})

	// 点击发表
	$(document).on('click', '#submitComment', async function(event) {
        event.preventDefault()
		const title = $('#commentTitle').val()
        const content = $('#commentContents').val()
        if (!title && !content && !starScore) {
            return
        } 
		const addRes = await getData(`${api}/commentAdd`, {
			bookId,
			nickName: loginNickName,
			sex: loginSex,
			title,
			content,
			star: starScore,
			parentId: 0,
		}, userEmail)
        if (addRes.success) {
            showMessage(langData.releaseSuccessfully)
			starScore = 0 // 重置评分
			$('#starMark svg').html('<path d="M916.210526 409.815579l-290.654315-25.061053L512 117.167158 398.443789 385.131789 107.789474 409.815579l220.698947 191.218526-66.290526 284.133053L512 734.423579l249.802105 150.743579-65.913263-284.133053L916.210526 409.815579zM512 658.809263l-151.983158 91.782737 40.421053-173.002105-134.197895-116.412632 177.044211-15.413895L512 282.947368l69.146947 163.301053 177.044211 15.36-134.197895 116.412632 40.421053 173.002105L512 658.809263z" fill="#EB4742" p-id="1073"></path>') // 重置星形
			$('#starMark p span').css('visibility', '') // 重置星形文字提示
            $('#comment-form')[0].reset() // 重置表单
            $('#text-count').text(0) // 重置字数提示
			$('#comments-list').html('') 
			totalPage = 1 // 重置评论页数
			localDelNum = 0 // 重置本地删除数
			getCommentsList(1, 0)
		}
	})

	// 点击查看回复
	$('#comments-list').on('click', '.commentMark', async function() {
		const parentId = $(this).data('dataid')
		const nickName = $(this).data('nickname')
		let subEv = $(`.subComments-${parentId}`)
		if (subEv.css('display') === 'block') {
			subEv.html('')
			subEv.css('display', 'none')
		} else {
			subEv.css('display', 'block')
            const textareaEV = `
                        <div class="comment" style="margin-top: 16px;padding-left: 0;">
                          <form class="row" method="post" style="margin-bottom: -23px;">
                            <div class="col-12" style="margin-bottom: -6px;">
                              <div class="form-group">
                                <textarea style="padding-top: 5px;" class="form-control form-control-square form-control-sm ${'subArea-' +
									parentId}" data-parentid="${parentId}" rows="3" maxlength="500" placeholder="${langData.reviewFrom} ${nickName}：" required></textarea>
                                <div class="textarea-counter text-muted-number text-right text-sm"><span class="sub-text-count-${parentId}">0</span> / 500</div>
                              </div>
                            </div>
                            <div class="col-8 text-left">
                              <div class="comment-footer-info"><span class="comment-meta text-muted info-sm text7A"><a href="javascript:;"><img src="./img/info.svg" class="infoIcon" style="bottom: 1px;"></a>${langData.replyAll}</span></div>
                            </div> 
                            <div class="col-4 text-right">
                              <button type="submit" class="btn btn-sm btn-primary sub-comment-form subBtn-${parentId}" data-parentid="${parentId}" data-parentnickname="${nickName}">${langData.repliesTo}</button>
                            </div>
                          </form>
                        </div>
                        <ul></ul>
            `
			if (isLogin) {
				subEv.append(textareaEV)
			} else {
				const ul = $('<ul></ul>')
				subEv.append(ul)
			}
			getSubList($(`.subComments-${parentId} ul`), 1, parentId, nickName)
		}
	})

    // 监听回复内容输入
    $('#comments-list').on('input', 'textarea', function() {
        const parentId = $(this).data('parentid')
        if (parentId) { // 回复输入框
            let contents = $(this).val()
			$(`.sub-text-count-${parentId}`).text(contents.length)
        }
	})

	// 点击回复
	$('#comments-list').on('click', '.sub-comment-form', async function(event) {
        event.preventDefault()
        event.stopPropagation()
		const parentId = $(this).data('parentid')
		const parentNickName = $(this).data('parentnickname')
        const content = $(`.subArea-${parentId}`).val()
        if (!content) return;
		const addRes = await getData(`${api}/commentAdd`, {
			bookId,
			nickName: loginNickName,
			sex: loginSex,
			title: '',
			content,
			star: '0',
			parentId,
		}, userEmail)
        if (addRes.success) {
            showMessage(langData.replySuccessfully)
            $(`.subArea-${parentId}`).val('') // 重置textarea
            $(`.sub-text-count-${parentId}`).text(0) // 重置字数提示
			$(`.subComments-${parentId} ul`).html('')
			// 更新子回复列表
			getSubList($(`.subComments-${parentId} ul`), 1, parentId, parentNickName)
		}
	})

	// 点赞评论
	$('#comments-list').on('click', '.like', function(event) {
        $(this).attr('src', './img/favorite-red.svg')
        const dataId = $(event.target).data('dataid')
        const parentEl = $(this).parent()
        if (!parentEl.has('span').length) { // 首次点赞 span不存在
            $('<span>').appendTo(parentEl).text('1').addClass(`likeCount-${dataId}`);
            $(this).addClass('hasLike')
            updateList('like', { dataId })
        } else { 
            if (!$(this).hasClass('hasLike')) {
				let curLikeCount = Number($(`.likeCount-${dataId}`).text())
				$(`.likeCount-${dataId}`).text(curLikeCount + 1)
				$(this).addClass('hasLike')
				updateList('like', { dataId })
			}
        }
    })

	// 点赞回复
    $('#comments-list').on('click', '.subLike', function (event) {
        $(this).attr('src', './img/favorite-red.svg')
        const dataId = $(event.target).data('dataid')
        const parentEl = $(this).parent()
        if (!parentEl.has('span').length) { // 首次点赞 span不存在
            $('<span>').appendTo(parentEl).text('1').addClass(`subLikeCount-${dataId}`);
            $(this).addClass('hasSubLike')
            updateList('like', { dataId })
        } else { 
            if (!$(this).hasClass('hasSubLike')) {
				let curSubLikeCount = Number($(`.subLikeCount-${dataId}`).text())
				$(`.subLikeCount-${dataId}`).text(curSubLikeCount + 1)
				$(this).addClass('hasSubLike')
				updateList('like', { dataId })
			}
        }
    })

	// 删除评论弹出框
	$(document).on('click', '.delComment', function(event) {
        let liIndex = $(this).closest("li").index();
        let dataId = $(event.target).data('dataid')
		$('#deleteCommentModal').data('type', 'del')
		$('#deleteCommentModal').data('delIndex', liIndex)
		$('#deleteCommentModal').data('dataid', dataId).modal('show')
    })

	// 删除回复弹出框
    $(document).on('click', '.subDelComment', function (event) {
        let liIndex = $(this).closest("li").index();
		let dataId = $(event.target).data('dataid')
		let parentId = $(event.target).data('parentid')
		let parentNickName = $(event.target).data('parentnickname')
        $('#deleteCommentModal').data('type', 'subDel')
        $('#deleteCommentModal').data('delIndex', liIndex)
		$('#deleteCommentModal').data('parentid', parentId)
		$('#deleteCommentModal').data('parentnickname', parentNickName)
		$('#deleteCommentModal').data('dataid', dataId).modal('show')
	})
	// 点击删除
    $(document).on('click', '#deleteCommentModal .delCommentBtn', function(event) {
		let delIndex = $('#deleteCommentModal').data('delIndex')
		let dataId = $('#deleteCommentModal').data('dataid')
		let parentId = $('#deleteCommentModal').data('parentid')
		let parentNickName = $('#deleteCommentModal').data('parentnickname')
		let delType = $('#deleteCommentModal').data('type')
		delType === 'del' ? updateList('del', { dataId, delIndex }, delType) : updateList('del', { dataId, parentId, parentNickName, delIndex }, delType)
		$('#deleteCommentModal').modal('hide')
	})

	// 更新评论/回复列表（点赞或删除）
    async function updateList(updateType, data, delType) {
		let url = ''
		updateType === 'like' ? (url = `${api}/commentLike`) : (url = `${api}/commentDel`)
        if (updateType === 'del') { // 删除
            const subDelEls = document.querySelectorAll('.comment-meta');
            subDelEls.forEach(el => {
                el.style.pointerEvents = 'none'; // 防止连续点击造成数据错乱
            });
        }
		const updateRes = await getData(url, {
			dataId: data.dataId,
		}, userEmail)
		if (updateRes.success && updateType === 'del') {
		    showMessage(langData.deleteSuccess)
            if (delType === 'del') { // 删除评论
                if (Number($(`.commentCount-${data.dataId}`).text())) { // 有回复数时
                    $(`#comments-list > li:nth-child(${data.delIndex + 1}) .parentCommentText`).html(`<span class="text7A">${langData.reviewDeleted}</span>`)
                    $(`#comments-list > li:nth-child(${data.delIndex + 1}) .parentCommentTitle svg`).css('display', 'none')
                    $(`#comments-list > li:nth-child(${data.delIndex + 1}) .commentTitleText`).css('display', 'none')
                } else {
                    $(`#comments-list > li:nth-child(${data.delIndex + 1})`).remove() // 本地删除li元素
                    let curAllNum = parseInt($('#allCommentsCount').text().match(/\d+/)[0])
                    $('#allCommentsCount').text(`(${curAllNum - 1})`)
                    localDelNum++
                }
		        // 请求接口删除： $('#comments-list').html('')
		        // totalPage = 1 // 重置评论页数
				// getCommentsList(1, 0)
		    } else if (delType === 'subDel') { // 删除回复
                $(`.subComments-${data.parentId} ul > li:nth-child(${data.delIndex + 1})`).remove() // 本地删除li元素
                let curSubNum = parseInt($(`.commentCount-${data.parentId}`).text().match(/\d+/)[0])
                $(`.commentCount-${data.parentId}`).text(`${curSubNum - 1 > 0 ? curSubNum - 1 : ''}`)
				// 请求接口删除：$(`.subComments-${data.parentId} ul`).html('')
				// getSubList($(`.subComments-${data.parentId} ul`), 1, data.parentId, data.parentNickName)
			}
		}
        if (updateType === 'del') { // 删除
            const subDelEls = document.querySelectorAll('.comment-meta');
            subDelEls.forEach(el => {
                el.style.pointerEvents = '';
            });
        }
	}

    // 发表/回复/删除成功
    function showMessage(msg) {
        $('body').append(`<div id="msg-success">${msg}</div>`)
        $('#msg-success').css({ // 显示
          'position': 'fixed',
          'top': '105px',
          'left': '50%',
          'padding': '0 15px',
          'z-index': '9999',
          'transform': 'translateX(-50%)',
          'min-width': '162px',
          'height': '68px',
          'line-height': '68px',
          'background-color': '#fff',
          'box-shadow': '0px 0px 12px rgba(0, 0, 0, 0.2)',
          'border-radius': '15px',
          'font-size': '16px',
          'color': '#374250',
          'text-align': 'center',
        }).animate({
          'transform': 'translateX(-50%)',
        }, 1500, 'swing');

        $('#msg-success').animate({ // 隐藏
          'transform': 'translateX(-50%) translateY(-100%)',
        }, 1500, 'swing', function() {
          $(this).remove()
        });
    }

    function setLanguage(data) {
        $('#langReview').text(data.Reviews)
        $('#langLogin').text(data.signInToWrite1)
        $('#langReg').text(data.signInToWrite2)
        $('#langAfter').text(data.signInToWrite3)
        $('#langOthers').text(data.shareOthers)
        $('#langClickStar').text(data.clickStar)
        $('#langStar1').text(data.star1)
        $('#langStar2').text(data.star2)
        $('#langStar3').text(data.star3)
        $('#langStar4').text(data.star4)
        $('#langStar5').text(data.star5)
        $('#commentTitle').attr('placeholder', data.title)
        $('#commentContents').attr('placeholder', data.writeReview)
        $('#langToAll').text(data.toAll)
        $('#submitComment').text(data.release)
        $('#deleteReview').text(data.deleteReview)
        $('#cancelReviewBtn').text(data.cancel)
        $('#deleteReviewBtn').text(data.delete)
        $('#noReviewsForNow').text(data.noReviewsForNow)
        $('#toFirstReview').text(data.toFirstReview)
        $('#viewMore1').text(data.viewMore1)
        $('#remainInput').text(data.remainInput)
        $('#word').text(data.word)
    }

    function getData(url, data, email) {
		return new Promise((resolve, reject) => {
			$.ajax({
				url,
				type: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'access_token': 'access_token',
					'email': email,
					'token': 'b3a63115-6ade-4658-88d7-7f5cab8c2e2f',
					'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
				},
				data: JSON.stringify(data),
				success(res) {
                    if (!res.success) { 
                        showMessage(langData.errorParam)
                    }
					resolve(res)
				},
				error(err) {
					reject(err)
				},
			})
		})
    }
    
    function formatDate(timestamp) {
		let date = new Date(timestamp)
		let year = date.getFullYear()
		let month = ('0' + (date.getMonth() + 1)).slice(-2)
		let day = ('0' + date.getDate()).slice(-2)
		return year + '-' + month + '-' + day
    }
    
}
