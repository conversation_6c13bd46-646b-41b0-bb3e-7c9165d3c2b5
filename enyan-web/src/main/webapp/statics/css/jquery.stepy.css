@CHARSET "UTF-8";

.button-back { float: left; }
.button-next, .finish { float: right; }
.button-back, .button-next, .finish {
	 cursor: pointer;  text-decoration: none;
}

.step {
	clear: left; 
	-khtml-border-radius: 3px; -moz-border-radius: 3px; -opera-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px;
}
.step label { color: #444; display: block;}
.step legend { color: #4080BF;  padding: 0 2px 3px 2px; margin-top: 10px; }
.step input, .step textarea, .step select { margin-left: 7px; }

.error-image { background: #f8f8f8 url('../images/error.png') no-repeat right top !important; }

.stepy-titles { list-style: none; margin: 0; padding: 0; width: 100%; }
.stepy-titles li { color: #DDD; cursor: pointer;  float: left; padding: 10px 15px; }
.stepy-titles li span { display: block; }
.stepy-titles .current-step { color: #369; cursor: auto; }
.stepy-titles li div{ font-size:18px;}

/*** Optionaly (jQuery <PERSON>te) ***/

.error { background-color: #FAF4F4; }

label.error { background: url('../images/alert.png') no-repeat 0 5px; color: #DE5130; display: block; float: left; margin: 3px 3px 0 10px; padding-left: 21px; padding-top: 2px; }

.stepy-tab ul{ border-bottom:#ccc 1px solid; padding-left:10px;}
.stepy-tab ul li.current-step{ border-left:#ccc 1px solid; border-top:#ccc 1px solid; border-right:#ccc 1px solid; border-bottom:#fff 1px solid; position:relative; top:1px;  }

.block-tabby ul { padding-left:10px;}
.block-tabby ul li.current-step{ background:#f8f8f8; }
