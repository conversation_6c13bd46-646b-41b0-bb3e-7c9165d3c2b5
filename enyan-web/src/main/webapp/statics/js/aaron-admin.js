function createAcsm(code) {
    var url = "createAcsm-"+code;
    $.get(url, function (res) {
        //console.log(res);
        if(!res.success){
            alert(res.errorMessages[0]);
        }else {
            //$("#buyerCartQuantity").text(res.data.quantity)
            alert(res.data.result);

        }
    });
}
function delBookSample(code) {
    var url = "delSample-"+code;
    $.get(url, function (res) {
        //console.log(res);
        if(!res.success){
            alert(res.errorMessages[0]);
        }else {
            //$("#buyerCartQuantity").text(res.data.quantity)
            alert(res.data.result);

        }
    });
}
