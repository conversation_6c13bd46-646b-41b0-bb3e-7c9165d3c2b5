var EditableTable = function () {

    return {

        //main function to initiate the module
        init: function () {
            function restoreRow(oTable, nRow) {
                var aData = oTable.fnGetData(nRow);
                var jqTds = $('>td', nRow);

                for (var i = 0, iLen = jqTds.length; i < iLen; i++) {
                    oTable.fnUpdate(aData[i], nRow, i, false);
                }

                oTable.fnDraw();
            }

            function editRow(oTable, nRow) {
                var aData = oTable.fnGetData(nRow);
                var jqTds = $('>td', nRow);
                //jqTds[0].innerHTML = '<input type="text" class="form-control small" value="' + aData[0] + '">';
                //jqTds[1].innerHTML = '<input type="text" class="form-control small" value="' + aData[1] + '">';
                jqTds[2].innerHTML = '<input type="text" class="form-control small" value="' + aData[2] + '">';
                jqTds[3].innerHTML = '<a class="edit" href="">保存</a> <a class="cancel" href="">取消</a>';
                //jqTds[4].innerHTML = '<a class="cancel" href="">取消</a>';
            }

            function editRowLong(oTable, nRow) {
                var aData = oTable.fnGetData(nRow);
                var jqTds = $('>td', nRow);
                //jqTds[0].innerHTML = '<input type="text" class="form-control small" value="' + aData[0] + '">';
                //jqTds[1].innerHTML = '<input type="text" class="form-control small" value="' + aData[1] + '">';
                jqTds[2].innerHTML = '<input type="text" class="form-control " value="' + aData[2] + '">';
                jqTds[3].innerHTML = '<a class="edit" href="">保存</a> <a class="cancel" href="">取消</a>';
                //jqTds[4].innerHTML = '<a class="cancel" href="">取消</a>';
            }

            function editRefreshRow(oTable, nRow) {
                var aData = oTable.fnGetData(nRow);
                //console.log(aData);
                var jqTds = $('>td', nRow);
                //jqTds[0].innerHTML = '<input type="text" class="form-control small" value="' + aData[0] + '">';
                //jqTds[1].innerHTML = '<input type="text" class="form-control small" value="' + aData[1] + '">';
                //jqTds[2].innerHTML = '<input type="text" class="form-control small" value="' + aData[2] + '">';
                jqTds[3].innerHTML = '<a class="edit" href="">重新生成</a> <a class="cancel" href="">取消</a>';
                //jqTds[4].innerHTML = '<a class="cancel" href="">取消</a>';
            }

            function saveRow(oTable, nRow) {
                var jqInputs = $('input', nRow);
                //oTable.fnUpdate(jqInputs[0].value, nRow, 0, false);
                //oTable.fnUpdate(jqInputs[0].value, nRow, 1, false);
                //console.log(jqInputs[0].value);
                oTable.fnUpdate(jqInputs[0].value, nRow, 2, false);
                oTable.fnUpdate('<a class="edit" href="">编辑</a>', nRow, 3, false);
                //oTable.fnUpdate('', nRow, 4, false);
                //oTable.fnUpdate('<a class="delete" href="">Delete</a>', nRow, 5, false);
                oTable.fnDraw();
            }

            function saveRefreshRow(oTable, nRow, newValue) {
                console.log(newValue);
                oTable.fnUpdate(newValue.toString(), nRow, 2, false);
                oTable.fnUpdate('<a class="edit" href="">编辑</a>', nRow, 3, false);
                //oTable.fnUpdate('', nRow, 4, false);
                //oTable.fnUpdate('<a class="delete" href="">Delete</a>', nRow, 5, false);
                oTable.fnDraw();
            }

            function cancelEditRow(oTable, nRow) {
                var jqInputs = $('input', nRow);
                //oTable.fnUpdate(jqInputs[0].value, nRow, 0, false);
                //oTable.fnUpdate(jqInputs[0].value, nRow, 1, false);
                oTable.fnUpdate(jqInputs[0].value, nRow, 2, false);
                oTable.fnUpdate('<a class="edit" href="">编辑</a>', nRow, 3, false);
                oTable.fnDraw();
            }

            // 修改DT_bootstrap.js 进行国际化
            var oTable = $('#editable-sample').dataTable({
                "aLengthMenu": [
                    [5, 15, 20, -1],
                    [5, 15, 20, "All"] // change per page values here
                ],
                "sDom": "<'row'<'col-lg-6'l><'col-lg-6'f>r>t<'row'<'col-lg-6'i><'col-lg-6'p>>"

            });

            var oAdTable = $('#editable-ad').dataTable({
                "aLengthMenu": [
                    [5, 15, 20, -1],
                    [5, 15, 20, "All"] // change per page values here
                ],
                "sDom": "<'row'<'col-lg-6'l><'col-lg-6'f>r>t<'row'<'col-lg-6'i><'col-lg-6'p>>"

            });

            var oRefreshTable = $('#editable-refresh').dataTable({
                "aLengthMenu": [
                    [5, 15, 20, -1],
                    [5, 15, 20, "All"] // change per page values here
                ],
                "sDom": "<'row'<'col-lg-6'l><'col-lg-6'f>r>t<'row'<'col-lg-6'i><'col-lg-6'p>>"

            });

            var oCouponTable = $('#editable-coupon').dataTable({
                "aLengthMenu": [
                    [5, 15, 20, -1],
                    [5, 15, 20, "All"] // change per page values here
                ],
                "sDom": "<'row'<'col-lg-6'l><'col-lg-6'f>r>t<'row'<'col-lg-6'i><'col-lg-6'p>>"

            });

            var oSpiritTable = $('#editable-spirit').dataTable({
                "aLengthMenu": [
                    [5, 15, 20, -1],
                    [5, 15, 20, "All"] // change per page values here
                ],
                "sDom": "<'row'<'col-lg-6'l><'col-lg-6'f>r>t<'row'<'col-lg-6'i><'col-lg-6'p>>"

            });

            //jQuery('#editable-sample_wrapper .dataTables_filter ').remove();
            //jQuery('#editable-sample_wrapper .dataTables_length ').remove();
            // jQuery('#editable-sample_wrapper .editable-sample_info ').remove();
            // jQuery('#editable-sample_wrapper .dataTables_info ').remove();
            // jQuery('#editable-sample_wrapper .dataTables_paginate ').remove();
            //
            jQuery('#editable-sample_wrapper .dataTables_filter input').addClass("form-control medium"); // modify table search input
            jQuery('#editable-sample_wrapper .dataTables_length select').addClass("form-control medium"); // modify table per page dropdown
            jQuery('#editable-refresh_wrapper .dataTables_filter input').addClass("form-control medium"); // modify table search input
            jQuery('#editable-refresh_wrapper .dataTables_length select').addClass("form-control medium"); // modify table per page dropdown
            jQuery('#editable-ad_wrapper .dataTables_filter input').addClass("form-control medium"); // modify table search input
            jQuery('#editable-ad_wrapper .dataTables_length select').addClass("form-control medium"); // modify table per page dropdown
            jQuery('#editable-coupon_wrapper .dataTables_filter input').addClass("form-control medium"); // modify table search input
            jQuery('#editable-coupon_wrapper .dataTables_length select').addClass("form-control medium"); // modify table per page dropdown
            jQuery('#editable-spirit_wrapper .dataTables_filter input').addClass("form-control medium"); // modify table search input
            jQuery('#editable-spirit_wrapper .dataTables_length select').addClass("form-control medium"); // modify table per page dropdown
            var nEditing = null;

            $('#editable-sample_new').click(function (e) {
                e.preventDefault();
                var aiNew = oTable.fnAddData(['', '', '', '',
                        '<a class="edit" href="">编辑</a>', '<a class="cancel" data-mode="new" href="">取消</a>'
                ]);
                var nRow = oTable.fnGetNodes(aiNew[0]);
                editRow(oTable, nRow);
                nEditing = nRow;
            });

            $('#editable-sample a.delete').live('click', function (e) {
                e.preventDefault();

                if (confirm("Are you sure to delete this row ?") == false) {
                    return;
                }

                var nRow = $(this).parents('tr')[0];
                oTable.fnDeleteRow(nRow);
                alert("Deleted! Do not forget to do some ajax to sync with backend :)");
            });

            $('#editable-sample a.cancel').live('click', function (e) {
                e.preventDefault();
                if ($(this).attr("data-mode") == "new") {
                    var nRow = $(this).parents('tr')[0];
                    oTable.fnDeleteRow(nRow);
                } else {
                    restoreRow(oTable, nEditing);
                    nEditing = null;
                }
            });

            $('#editable-sample a.edit').live('click', function (e) {
                e.preventDefault();

                /* Get the row as a parent of the link that was clicked on */
                var nRow = $(this).parents('tr')[0];

                if (nEditing !== null && nEditing != nRow) {
                    /* Currently editing - but not this row - restore the old before continuing to edit mode */
                    restoreRow(oTable, nEditing);
                    editRow(oTable, nRow);
                    nEditing = nRow;
                } else if (nEditing == nRow && this.innerHTML == "保存") {
                    /* Editing this row and want to save it */
                    saveRow(oTable, nEditing);
                    nEditing = null;

                    var aData = oTable.fnGetData(nRow);
                    //alert("Updated! Do not forget to do some ajax to sync with backend :)");

                    //$.post("/config/saveConfig",)


                    /*
                    jQuery.post("/config/saveConfig",
                        {
                            configId:aData[0],
                            configValue:aData[2],
                            _csrf:csrf_token
                        },
                        function(data,status){
                            //jQuery.console
                            console.log(data);
                            alert("数据：" + data + "\n状态：" + status);
                            //var obj = jQuery.parseJSON(data);

                            //alert("数据：" + obj + "\n状态：" + status);
                        });
                    */
                    var url = "/config/saveConfig";

                    /*
                    $.ajax({
                        type: "POST",
                        async: false,
                        url:url,
                        dataType:"json",
                        contentType: "application/json",
                        data:JSON.stringify({
                            configId:aData[0],
                            configValue:aData[2],
                            _csrf:csrf_token
                        }),
                        success:function(data){
                            //console.log(data);
                            alert(data.successMessage)
                        }
                    });*/
                    //console.log(aData[0]+","+aData[2]);
                    $.jpost(url, {
                            configId:aData[0],
                            configValue:aData[2],
                            //_token:csrf_token
                        }).then(res => {
                        //console.log(res);
                        alert(res.successMessage);
                    });
                } else {
                    /* No edit in progress - let's start one */
                    editRow(oTable, nRow);
                    nEditing = nRow;
                }
            });

            $('#editable-refresh a.edit').live('click', function (e) {
                e.preventDefault();

                /* Get the row as a parent of the link that was clicked on */
                var nRow = $(this).parents('tr')[0];

                if (nEditing !== null && nEditing != nRow) {
                    /* Currently editing - but not this row - restore the old before continuing to edit mode */
                    restoreRow(oRefreshTable, nEditing);
                    editRefreshRow(oRefreshTable, nRow);
                    nEditing = nRow;
                } else if (nEditing == nRow && this.innerHTML == "重新生成") {
                    /* Editing this row and want to save it */
                    //nEditing = null;

                    var aData = oRefreshTable.fnGetData(nRow);

                    var url = "/config/saveRefreshConfig";
                    //console.log(aData[0]+","+aData[2]);
                    $.jpost(url, {
                        configId:aData[0],
                        //configValue:aData[2],
                        //_token:csrf_token
                    }).then(res => {
                        //console.log(res);
                        saveRefreshRow(oRefreshTable, nEditing, res.result.configValue);
                        alert(res.successMessage);
                });
                } else {
                    /* No edit in progress - let's start one */
                    editRefreshRow(oRefreshTable, nRow);
                    nEditing = nRow;
                }
            });


            $('#editable-refresh a.cancel').live('click', function (e) {
                e.preventDefault();
                if ($(this).attr("data-mode") == "new") {
                    var nRow = $(this).parents('tr')[0];
                    oRefreshTable.fnDeleteRow(nRow);
                } else {
                    restoreRow(oRefreshTable, nEditing);
                    nEditing = null;
                }
            });


            $('#editable-ad a.cancel').live('click', function (e) {
                e.preventDefault();
                if ($(this).attr("data-mode") == "new") {
                    var nRow = $(this).parents('tr')[0];
                    oAdTable.fnDeleteRow(nRow);
                } else {
                    restoreRow(oAdTable, nEditing);
                    nEditing = null;
                }
            });

            $('#editable-ad a.edit').live('click', function (e) {
                e.preventDefault();

                /* Get the row as a parent of the link that was clicked on */
                var nRow = $(this).parents('tr')[0];

                if (nEditing !== null && nEditing != nRow) {
                    /* Currently editing - but not this row - restore the old before continuing to edit mode */
                    restoreRow(oAdTable, nEditing);
                    editRowLong(oAdTable, nRow);
                    nEditing = nRow;
                } else if (nEditing == nRow && this.innerHTML == "保存") {
                    /* Editing this row and want to save it */
                    saveRow(oAdTable, nEditing);
                    nEditing = null;

                    var aData = oAdTable.fnGetData(nRow);
                    var url = "/config/saveConfig";

                    //console.log(aData[0]+","+aData[2]);
                    $.jpost(url, {
                        configId:aData[0],
                        configValue:aData[2],
                        //_token:csrf_token
                    }).then(res => {
                        //console.log(res);
                        alert(res.successMessage);
                });
                } else {
                    /* No edit in progress - let's start one */
                    editRowLong(oAdTable, nRow);
                    nEditing = nRow;
                }
            });

            $('#editable-coupon a.cancel').live('click', function (e) {
                e.preventDefault();
                if ($(this).attr("data-mode") == "new") {
                    var nRow = $(this).parents('tr')[0];
                    oCouponTable.fnDeleteRow(nRow);
                } else {
                    restoreRow(oCouponTable, nEditing);
                    nEditing = null;
                }
            });

            $('#editable-coupon a.edit').live('click', function (e) {
                e.preventDefault();

                /* Get the row as a parent of the link that was clicked on */
                var nRow = $(this).parents('tr')[0];

                if (nEditing !== null && nEditing != nRow) {
                    /* Currently editing - but not this row - restore the old before continuing to edit mode */
                    restoreRow(oCouponTable, nEditing);
                    editRowLong(oCouponTable, nRow);
                    nEditing = nRow;
                } else if (nEditing == nRow && this.innerHTML == "保存") {
                    /* Editing this row and want to save it */
                    saveRow(oCouponTable, nEditing);
                    nEditing = null;

                    var aData = oCouponTable.fnGetData(nRow);
                    var url = "/config/saveConfig";

                    //console.log(aData[0]+","+aData[2]);
                    $.jpost(url, {
                        configId:aData[0],
                        configValue:aData[2],
                        //_token:csrf_token
                    }).then(res => {
                        //console.log(res);
                        alert(res.successMessage);
                });
                } else {
                    /* No edit in progress - let's start one */
                    editRowLong(oCouponTable, nRow);
                    nEditing = nRow;
                }
            });

            $('#editable-spirit a.cancel').live('click', function (e) {
                e.preventDefault();
                if ($(this).attr("data-mode") == "new") {
                    var nRow = $(this).parents('tr')[0];
                    oSpiritTable.fnDeleteRow(nRow);
                } else {
                    restoreRow(oSpiritTable, nEditing);
                    nEditing = null;
                }
            });

            $('#editable-spirit a.edit').live('click', function (e) {
                e.preventDefault();

                /* Get the row as a parent of the link that was clicked on */
                var nRow = $(this).parents('tr')[0];

                if (nEditing !== null && nEditing != nRow) {
                    /* Currently editing - but not this row - restore the old before continuing to edit mode */
                    restoreRow(oSpiritTable, nEditing);
                    editRowLong(oSpiritTable, nRow);
                    nEditing = nRow;
                } else if (nEditing == nRow && this.innerHTML == "保存") {
                    /* Editing this row and want to save it */
                    saveRow(oSpiritTable, nEditing);
                    nEditing = null;

                    var aData = oSpiritTable.fnGetData(nRow);
                    var url = "/config/saveConfig";

                    //console.log(aData[0]+","+aData[2]);
                    $.jpost(url, {
                        configId:aData[0],
                        configValue:aData[2],
                        //_token:csrf_token
                    }).then(res => {
                        //console.log(res);
                        alert(res.successMessage);
                });
                } else {
                    /* No edit in progress - let's start one */
                    editRowLong(oSpiritTable, nRow);
                    nEditing = nRow;
                }
            });



        }
    };

}();