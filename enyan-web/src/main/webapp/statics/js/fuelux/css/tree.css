.fuelux .tree {
  position: relative;
  padding: 10px 15px 0 15px;
  overflow-x: hidden;
  overflow-y: auto;
  border: 1px solid #BBBBBB;
  border-radius: 4px 4px 4px 4px;
}

.fuelux .tree .tree-folder {
  width: 100%;
  min-height: 20px;
  margin-top: 1px;
  cursor: pointer;
}

.fuelux .tree .tree-folder .tree-folder-header {
  position: relative;
  height: 20px;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
}

.fuelux .tree .tree-folder .tree-folder-header:hover {
  background-color: #dfeef5;
}

.fuelux .tree .tree-folder .tree-folder-header i {
  position: absolute;
  top: 1px;
  left: 5px;
  float: left;
}

.fuelux .tree .tree-folder .tree-folder-header .tree-folder-name {
  padding-left: 29px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fuelux .tree .tree-folder .tree-folder-content {
  margin-left: 23px;
}

.fuelux .tree .tree-item {
  position: relative;
  width: 100%;
  height: 20px;
  margin-top: 1px;
  cursor: pointer;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
}

.fuelux .tree .tree-item:hover {
  background-color: #dfeef5;
}

.fuelux .tree .tree-item .tree-item-name {
  position: absolute;
  left: 29px;
}

.fuelux .tree .tree-item .tree-dot {
  position: absolute;
  top: 8px;
  left: 10px;
  display: block;
  width: 4px;
  height: 4px;
  background-color: #333333;
  -webkit-border-radius: 6px;
     -moz-border-radius: 6px;
          border-radius: 6px;
}

.fuelux .tree .tree-item .icon-ok {
  position: absolute;
  top: 1px;
  left: 5px;
}

.fuelux .tree .tree-selected {
  background-color: #b9dff1;
}

.fuelux .tree .tree-selected:hover {
  background-color: #b9dff1;
}