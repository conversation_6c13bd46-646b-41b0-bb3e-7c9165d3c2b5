<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd" id="WebApp_ID" version="3.0">
  	<display-name>Enyan-Web</display-name>
  
  	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath:spring.xml,classpath:spring-mybatis.xml</param-value>
  	</context-param>

	<context-param>
		<param-name>webAppRootKey</param-name>
		<param-value>evn.webapp</param-value>
	</context-param>
	
	<filter>
		<filter-name>encodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>encodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!--<filter>
		<filter-name>cors</filter-name>
		<filter-class>com.aaron.web.filter.CORSFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>cors</filter-name>
		<url-pattern>*</url-pattern>
	</filter-mapping>-->
	<!--
        <filter>
            <filter-name>CorsFilter</filter-name>
            <filter-class>org.apache.catalina.filters.CorsFilter</filter-class>
            <init-param>
                <param-name>cors.allowed.origins</param-name>
                <param-value>https://e.bookapp.cc, https://ebook.endao.cloud, https://edbook.click</param-value>
            </init-param>

            <init-param>
                <param-name>cors.support.credentials</param-name>
                <param-value>true</param-value>
            </init-param>

            <init-param>
                <param-name>cors.allowed.methods</param-name>
                <param-value>GET,HEAD,POST,PUT,DELETE,OPTIONS</param-value>
            </init-param>

            <init-param>
                <param-name>cors.exposed.headers</param-name>
                <param-value>Access-Control-Allow-Origin,Access-Control-Allow-Credentials,X-Bonita-API-Token</param-value>
            </init-param>

            <init-param>
                <param-name>cors.allowed.headers</param-name>
                <param-value>Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers,X-Bonita-API-Token</param-value>
            </init-param>

        </filter>
        <filter-mapping>
            <filter-name>CorsFilter</filter-name>
            <url-pattern>/*</url-pattern>
        </filter-mapping>
    -->
        <listener>
            <description>spring listener</description>
            <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
        </listener>

        <listener>
            <listener-class>org.springframework.web.util.WebAppRootListener</listener-class>
        </listener>

        <!--<listener>
            <listener-class>com.aaron.servlet.listener.TomcatStopContextListener</listener-class>
        </listener>-->
	
	<servlet>
		<description>spring mvc servlet</description>
		<servlet-name>springmvc</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<description>springmvc config</description>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath:spring-mvc.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>springmvc</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>
	<!--
	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.html</url-pattern>
	</servlet-mapping>

	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.jpg</url-pattern>
	</servlet-mapping>

	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.gif</url-pattern>
	</servlet-mapping>

	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.css</url-pattern>
	</servlet-mapping>

	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.png</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.eot</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.woff</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.ttf</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>default</servlet-name>
		<url-pattern>*.pdf</url-pattern>
	</servlet-mapping>
	-->

	<error-page>
		<location>/500</location>
	</error-page>
	<error-page>
		<error-code>400</error-code>
		<location>/500</location>
	</error-page>
	<error-page>
		<error-code>401</error-code>
		<location>/500</location>
	</error-page>
	<error-page>
		<error-code>402</error-code>
		<location>/500</location>
	</error-page>
	<error-page>
		<error-code>403</error-code>
		<location>/500</location>
	</error-page>
	<error-page>
		<error-code>404</error-code>
		<location>/store/index.html</location>
	</error-page>

	<error-page>
		<error-code>500</error-code>
		<location>/500</location>
	</error-page>
	
    <welcome-file-list>
<!--    	<welcome-file>index.html</welcome-file>-->
<!--    	<welcome-file>index.htm</welcome-file>-->
    	<welcome-file>index.jsp</welcome-file>
  	</welcome-file-list>
</web-app>