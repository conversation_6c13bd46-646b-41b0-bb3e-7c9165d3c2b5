<%--
  User: <PERSON>
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<div class="container padding-bottom-3x mb-2 padding-top-2x">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-10 text-center">
            <form class="login-box padding-bottom-2x" method="post">

                <c:choose>
                    <c:when test="${emailStatus.end}"> <%--操作成功--%>
                        <h4 class="margin-bottom-2x padding-top-2x">${emailStatus.explan}去
                            <a class="text-medium text-decoration-none" href="<at:web type="login" value="login"/>">登入</a>
                        </h4>
                    </c:when>
                    <c:when test="${emailStatus.success}">  <%--發送成功或發送頻繁--%>
                        <%--<c:when test="${emailStatus.step == 'email_active'}">

                        </c:when>--%>
                        <h4 class="margin-bottom-2x"><%--${emailStatus.head}--%><spring:message code="email.verify"/> </h4>
                        <p class="text-lg text-bold"><c:out value="${emailStatus.explan}" default="已成功發送驗證郵件！"/></p>
                        <p class="card-text">
                            請在<a class="text-medium text-decoration-none" href="#">1小時內</a>前往郵箱點擊驗證鏈接
                        </p>
                        <p class="margin-top-1x text-lg text-bold">一直沒有收到驗證郵件？</p>
                        <p class="card-text">1. 請確認您輸入的郵箱地址是否正確</p>
                        <p class="card-text">2. 請檢查是否在垃圾郵件中</p>
                        <p class="card-text">未收到請點擊:
                            <a class="text-medium text-decoration-none" href="${emailStatus.emailOperation}?emailCode=${emailStatus.emailCode}">
                                &nbsp;重新發送郵件
                            </a>
                        </p>
                    </c:when>
                    <c:otherwise>  <%--操作失敗--%>
                        <h4 class="margin-bottom-2x padding-top-2x">${emailStatus.explan}去
                            <a class="text-medium text-decoration-none" href="<at:web type="login" value="login"/>">登入</a>
                        </h4>
                    </c:otherwise>
                </c:choose>
            </form>
        </div>
    </div>
</div>