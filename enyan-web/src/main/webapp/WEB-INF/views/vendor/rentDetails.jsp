<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header left-side-collapsed">
<sec:authentication var="user" property="principal" />
<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="rentDetails?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->

                <div class="form-group">
                    <spring:message code="search.time.select" var="placeHolderTimeRange"/>
                    <spring:message code="search.by.range"/>：
                    <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}"/>
                    <script>

                        laydate.render({
                            elem: '#rangeDate'
                            ,format: 'yyyyMMdd'
                            ,range: true
                            ,lang:'<spring:message code="search.time.lang"/>'
                        });
                    </script>

                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();"><spring:message code="search.select"/> </button>
                    <a class="btn btn-primary" href="rentDetailsExcel-${dto.startDate}-${dto.endDate}"><spring:message code="search.download"/> </a>
                </div>

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <spring:message code="rent.detail"/>
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.balance"/></a>
                </li>
                <li class="active"> <spring:message code="rent.detail"/></li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <spring:message code="rent.detail"/>
                            <span class="tools pull-right">
                                ①版税金额=净收益*版税比例
                                ②净收益=销售额-支付手续费
                                ③销售额=售价*销量 <br>
                                声明：以下数据供您参考，实际版税以最终结算金额为准，如有疑问请联系我们
                            </span>
                        </header>
                        <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                        </div>
                        <div class="panel-body">
                            <table class="table  table-hover general-table" >
                                <thead>
                                <tr>
                                    <th class="hidden-phone"><spring:message code="order.num"/> </th>
                                    <th class="hidden-phone">Email</th>
                                    <th><spring:message code="order.date"/></th>
                                    <th>订购类型</th>
                                    <th>自动</th>
                                    <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                        <th><spring:message code="income.total"/> </th>
                                        <th><spring:message code="pay.type"/> </th>
                                        <th><spring:message code="income.pay.fee"/> </th>
                                    </c:if>
                                    <th>净收益</th>
                                    <th><spring:message code="income.vendor"/> </th>

                                </tr>
                                </thead>
                                <tbody>

                                <c:forEach var="list" items="${list}">
                                <tr>
                                    <td class="hidden-phone">
                                        ${list.orderNum}
                                    </td>
                                    <td class="hidden-phone">
                                            ${list.userEmail}
                                    </td>
                                    <td>
                                        <fmt:formatDate value="${list.purchasedAt}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                    </td>
                                    <td class="hidden-phone">
                                        <at:rentDetailInfo infoType="rentName" enyanRentDetail="${list}"/>
                                    </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${list.isAuto == 1}">
                                                自动订购
                                            </c:when>
                                            <c:when test="${list.isAuto == 0}">
                                                手动订购
                                            </c:when>
                                        </c:choose>
                                    </td>
                                    <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                        <td>
                                            HK$${list.incomeTotal}
                                        </td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${list.payType == 1}">
                                                    <spring:message code="label.pay.1"/>
                                                </c:when>
                                                <c:when test="${list.payType == 11}">
                                                    <spring:message code="label.pay.11"/>
                                                </c:when>
                                                <c:when test="${list.payType == 2}">
                                                    <spring:message code="label.pay.1"/>
                                                </c:when>
                                                <c:when test="${list.payType == 3}">
                                                    <spring:message code="label.pay.3"/>
                                                </c:when>
                                                <c:when test="${list.payType == 4}">
                                                    <spring:message code="label.pay.4"/>
                                                </c:when>
                                                <c:when test="${list.payType == 5}">
                                                    <spring:message code="label.pay.5"/>
                                                </c:when>
                                                <c:when test="${list.payType == 21}">
                                                    <spring:message code="label.pay.21"/>
                                                </c:when>
                                                <c:when test="${list.payType == 22}">
                                                    <spring:message code="label.pay.22"/>
                                                </c:when>
                                            </c:choose>
                                        </td>
                                        <td>
                                            HK$${list.payFee}
                                        </td>
                                    </c:if>
                                    <td>
                                        HK$${list.netSales}
                                    </td>
                                    <td>
                                        HK$${list.incomeVendor}
                                    </td>
                                </tr>
                                </c:forEach>

                                </tbody>
                            </table>
                            <c:if test="${empty list}">
                                <div class="">
                                    <spring:message code="data.empty"/>
                                </div>
                            </c:if>
                            <!--pagination start-->
                            <div class="">
                                ${pageLand}
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->


        <jsp:include page="../admin/footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#rentDetails").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
