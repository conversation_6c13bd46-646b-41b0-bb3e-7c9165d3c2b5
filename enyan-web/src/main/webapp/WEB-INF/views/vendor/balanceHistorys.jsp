<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">
<sec:authentication var="user" property="principal" />
<section>
    <!-- left side start-->
    <jsp:include page="../admin/adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="balanceHistory?${_csrf.parameterName}=${_csrf.token}" modelAttribute="dto" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->

                <div class="form-group">
                    <spring:message code="search.time.select" var="placeHolderTimeRange"/>
                    <spring:message code="search.by.range"/>：
                    <form:input path="rangeDate" cssClass="form-control" size="20" placeholder="${placeHolderTimeRange}"/>
                    <script>
                        laydate.render({
                            elem: '#rangeDate'
                            ,type: 'month'
                            ,format: 'yyyyMM'
                            ,range: true
                            ,lang:'<spring:message code="search.time.lang"/>'
                        });
                    </script>
                    <button type="submit" class="btn btn-primary m-bot15" onclick="disabled=true;this.form.submit();" style="margin-top: 1.5rem"><spring:message code="search.select"/> </button>
                </div>

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <spring:message code="balance.history"/>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.balance"/></a>
                    </li>
                    <li class="active"> <spring:message code="balance.history"/></li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <spring:message code="balance.history"/>
                                <span class="tools pull-right">
                         </span>
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <table class="table  table-hover general-table" >
                                    <thead>
                                    <tr>
                                        <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                            <th><spring:message code="label.vendor"/> </th>
                                        </c:if>
                                        <th><spring:message code="income.vendor"/> </th>
                                        <th><spring:message code="label.status"/> </th>
                                        <th><spring:message code="label.time.create"/> </th>
                                        <th><spring:message code="info.look"/> </th>
                                        <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                        <th><spring:message code="operation.label" /></th>
                                        </c:if>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    <c:forEach var="list" items="${list}">
                                        <tr>
                                            <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                                <td>  <at:publisherName publisherId="${list.publisherId}"/></td>
                                            </c:if>
                                            <td>
                                                HK$${list.incomeVendorTotal}
                                            </td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${list.isCounted == 0}">
                                                        <span class="label label-info label-mini"><spring:message code="label.counted.0"/></span>
                                                    </c:when>
                                                    <c:when test="${list.isCounted == -1}">
                                                        <span class="label label-danger label-mini"><spring:message code="label.counted.-1"/></span>
                                                    </c:when>
                                                    <c:when test="${list.isCounted == -2}">
                                                        <span class="label label-danger label-mini"><spring:message code="label.counted.-2"/></span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span class="label label-success label-mini"><spring:message code="label.counted.1"/></span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td>
                                                <fmt:formatDate value="${list.balanceAt}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                            </td>
                                            <td>
                                                <a href="balanceHistoryView-${list.balanceHistoryId}"><spring:message code="info.look"/></a>
                                            </td>
                                            <c:if test="${user.authorities[0].authority == 'ROLE_ADMIN' or user.authorities[0].authority == 'ROLE_FINANCE'}">
                                                <td>
                                                    <c:if test="${list.isCounted == 0}">
                                                        <a href="balanceHistoryConfirm-${list.balanceHistoryId}" onclick="javascript:return checkConfirm()">&nbsp;执行结算&nbsp;</a>
                                                    </c:if>
                                                </td>
                                            </c:if>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                                <!--pagination start-->
                                <div class="">
                                        ${pageLand}
                                </div>
                                <!--pagination end-->
                            </div>
                        </section>

                    </div>
                </div>
            </div>
            <!--body wrapper end-->


            <jsp:include page="../admin/footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../admin/footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#balanceHistoryUI").addClass("active");
    })

    function checkConfirm() {
        var msg = "您确定已经结算吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>

</body>
</html>
