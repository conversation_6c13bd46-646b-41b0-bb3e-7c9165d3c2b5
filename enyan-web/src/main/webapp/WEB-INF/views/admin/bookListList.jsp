<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="list?${_csrf.parameterName}=${_csrf.token}" modelAttribute="record" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <div class="form-group">
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="请输入"/>
                    <form:select path="searchType"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15">
                        <form:option value="0" >书单名称</form:option>
                    </form:select>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
                </div>


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <spring:message code="bookList.list"/>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active"> <spring:message code="bookList.list"/></li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">
                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <spring:message code="bookList.list"/>
                                <span class="tools pull-right">
                                    <a href="/book/initIndexCategory" target="_blank">重置首页推荐</a>
                                    <a href="addUI"><spring:message code="bookList.add"/></a>
                                 </span>
                            </header>
                            <div class="panel-body">
                                <table class="table  table-hover general-table">
                                    <thead>
                                    <tr>
                                        <th width="200"> 书单名称</th>
                                        <th width="15"> 推荐值</th>
                                        <th width="10"> 关于首页</th>
                                        <th width="10"> 全套购买</th>
<%--                                        <th class="hidden-phone" width="100">书单图片</th>--%>
<%--                                        <th width="50"> 书单介绍</th>--%>
                                        <th width="10"> 折扣值</th>
                                        <th width="10"> 价格</th>
                                        <th width="10"> 折扣后价格</th>
                                        <th width="200" style="word-wrap:break-word;word-break: break-all"> 书籍ID</th>
                                        <th width="100">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>

                                    <c:forEach var="list" items="${list}">
                                        <tr>
                                            <td>
                                                <a href="get-${list.setId}">${list.setName}</a>
                                            </td>
<%--                                            <td class="hidden-phone">--%>
<%--                                                <c:if test="${not empty list.bannerUrl}">--%>
<%--                                                    Banner:<img src="${list.bannerUrl}" width="200" height="100">--%>
<%--                                                </c:if>--%>
<%--                                                <c:if test="${not empty list.bookCover}">--%>
<%--                                                    <br/>封  面:<img src="${list.bookCover}" width="140" height="100">--%>
<%--                                                </c:if>--%>
<%--                                            </td>--%>
<%--                                            <td>--%>
<%--                                                    ${list.setAbstract}--%>
<%--                                            </td>--%>
                                            <td>
                                                    ${list.showOrder}
                                            </td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${list.isIndex == 0}">
                                                        <span class="label label-success label-mini">非首页</span>
                                                    </c:when>
                                                    <c:when test="${list.isIndex == 1}">
                                                        <span class="label label-warning label-mini">在首页列表</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span class="label label-danger label-mini">在首页套系推荐</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${list.canAllBuy == 0}">
                                                        <span class="label label-danger label-mini">不可以</span>
                                                    </c:when>
                                                    <c:when test="${list.canAllBuy == 1}">
                                                        <span class="label label-success label-mini">可以</span>
                                                    </c:when>
                                                </c:choose>
                                            </td>
                                            <td>
                                                    ${list.discountValue}
                                            </td>
                                            <td>
                                                    ${list.price}
                                            </td>
                                            <td>
                                                    ${list.priceDiscount}
                                            </td>
                                            <td style="word-wrap:break-word;word-break: break-all">
                                                    ${list.bookIdText}
                                            </td>
                                            <%--
                                            <td>
                                                <c:if test="${list.isValid==0}">
                                                    <span style="color: red;font-weight: bold">无效</span>
                                                </c:if>
                                                <c:if test="${list.isValid==1}">
                                                    <span style="color: #00EA00;font-weight: bold">有效</span>
                                                </c:if>
                                            </td>--%>
                                            <td>
                                                <a href="set-${list.setId}" target="_blank">&nbsp;关联书籍｜</a>&nbsp;
                                                <a href="get-${list.setId}">设置｜</a>&nbsp;
                                                <a href="/store/booklist-${list.setId}#" target="_blank">预览</a>&nbsp;
                                                <a href="del-${list.setId}" onclick="javascript:return checkDel()"></a>
                                                &nbsp;｜<span id="status${list.setId}"><a onclick="javascript:return checkReset(${list.setId})">重置</a></span>
                                            </td>
                                        </tr>
                                    </c:forEach>

                                    </tbody>
                                </table>
                                <c:if test="${empty list}">
                                    <div class="">
                                        <spring:message code="data.empty"/>
                                    </div>
                                </c:if>
                                <!--pagination start-->
                                <div class="">
                                        ${pageLand}
                                </div>
                                <!--pagination end-->
                            </div>
                        </section>

                    </div>
                </div>
            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#bookListList").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }

    function checkReset(dataId) {
        let msg = "您真的要重置本书单吗？\n\n请确认！";
        if (confirm(msg)==true){
            reset(dataId)
            return true;
        }else{
            return false;
        }
    }
    function reset(dataId){
        url="reset?${_csrf.parameterName}=${_csrf.token}";
        $.jpost(url, {
            "setId":dataId,
        }).then(res => {
            //console.log(res);
            if(res.success){
                //alert("sss")
                //$('#do'+dataId).html("")
                $('#status'+dataId).html("<span class=\"label label-success label-mini\">已重置</span>")

                //$('#data'+dataId).remove()
            }else{
                alert("发生错误！");
            }
        });
    }

</script>

</body>
</html>
