<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/jquery-multi-select/css/multi-select.css' />" />

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-daterangepicker/daterangepicker-bs3.css' />" />
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-datepicker/css/datepicker-custom.css' />" />
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->

    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="save?${_csrf.parameterName}=${_csrf.token}" modelAttribute="record" method="post" enctype="multipart/form-data" role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <c:choose>
                        <c:when test="${record.blogId==null}">
                            <spring:message code="blog.add"/>
                        </c:when>
                        <c:otherwise>
                            <spring:message code="blog.edit"/>
                        </c:otherwise>
                    </c:choose>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active">
                        <c:choose>
                            <c:when test="${record.blogId==null}">
                                <spring:message code="blog.add"/>
                            </c:when>
                            <c:otherwise>
                                <spring:message code="blog.edit"/>
                            </c:otherwise>
                        </c:choose>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <c:choose>
                                    <c:when test="${record.blogId==null}">
                                        <spring:message code="blog.add"/>
                                    </c:when>
                                    <c:otherwise>
                                        <spring:message code="blog.edit"/>
                                    </c:otherwise>
                                </c:choose>
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="blogTitle">标题</label>
                                        <div class="col-sm-10">
                                            <form:input path="blogTitle" cssClass="form-control" id="blogTitle" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="author">作者</label>
                                        <div class="col-sm-10">
                                            <form:input path="author" cssClass="form-control" id="author" maxlength="40"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="blogCover">头图</label>
                                        <div class="col-sm-10">
                                            <form:input path="blogCover" cssClass="form-control" id="blogCover" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="blogCoverApp">头图App封面</label>
                                        <div class="col-sm-10">
                                            <form:input path="blogCoverApp" cssClass="form-control" id="blogCoverApp" maxlength="100"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="recommendedOrder">推荐优先级(数字越大越优先)</label>
                                        <div class="col-sm-10">
                                            <form:input path="recommendedOrder" cssClass="form-control" id="recommendedOrder" maxlength="5"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="isDeleted">隐藏</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="isDeleted" value="1" id="isDeleted"/>是
                                            <form:radiobutton path="isDeleted" value="0"/>否
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="publisherId">出版商</label>
                                        <div class="col-sm-10">
                                            <form:select path="publisherId" items="${publisherList}" itemLabel="name" itemValue="value" cssClass="form-control m-bot15"  id="publisherId"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="publisherId">Blog类别</label>
                                        <div class="col-sm-10">
                                            <form:select path="categoryId" cssClass="form-control m-bot15">
                                                <form:option value="1" ><spring:message code="reading.nav.1"/></form:option>
                                                <form:option value="2" ><spring:message code="reading.nav.2"/></form:option>
                                                <form:option value="3" ><spring:message code="reading.nav.3"/></form:option>
                                                <form:option value="4" ><spring:message code="reading.nav.4"/></form:option>
                                                <form:option value="5" ><spring:message code="reading.nav.5"/></form:option>
                                                <form:option value="6" ><spring:message code="reading.nav.6"/></form:option>
                                                <form:option value="7" ><spring:message code="reading.nav.7"/></form:option>
                                                <form:option value="8" ><spring:message code="reading.nav.8"/></form:option>
                                            </form:select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="blogAbstract">摘要</label>
                                        <div class="col-sm-10">
                                            <form:textarea path="blogAbstract" cssClass="form-control" id="blogAbstract" rows="3" maxlength="200"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="blogContent">正文</label>
                                        <div class="col-sm-10">
                                            <form:textarea path="blogContent" cssClass="form-control ckeditor" id="blogContent" rows="6"/>
                                        </div>
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <form:hidden path="blogId"/>
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">提  交</button>
                                    </div>
                                </form>

                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#blogList").addClass("active");
    })
</script>
<script type="text/javascript" src="<c:url value='/statics/js/ckeditor/ckeditor.js' />"></script>
<!--multi-select-->
<script type="text/javascript" src="<c:url value='/statics/js/jquery-multi-select/js/jquery.multi-select.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/jquery-multi-select/js/jquery.quicksearch.js' />"></script>
<script src="<c:url value='/statics/js/multi-select-init.js' />"></script>
</body>
</html>
