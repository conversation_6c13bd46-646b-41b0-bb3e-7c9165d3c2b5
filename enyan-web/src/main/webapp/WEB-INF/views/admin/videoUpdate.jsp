<%@ page language="java" contentType="text/html; charset=utf-8"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@page import="com.aaron.common.*"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<html> 
	<head>
		<title>修改产品</title>
	<link href="<%=basePath %>css/admin.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="<%=basePath %>js/comm.js"></script>
	<script type="text/javascript" src="<%=basePath %>ckeditor/ckeditor.js"></script>
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="expires" content="0">    
	<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
	<meta http-equiv="description" content="This is my page">
	
  </head>
  
  <body>
    <Table width="90%"  border="0" align="center" cellpadding="10" cellspacing="0" class="table1">
    <tr>
          <td colspan="3" >
          <strong>
           修改产品
          </strong></td>
     </tr>
     
      <tr >
        <td colspan="3"  <c:if test='${isSaveError }'>class="errormsg"</c:if> <c:if test='${isSaveSuccess }'>class="msg3"</c:if>>
        ${msg}
        </td>
         
      </tr>
      
    <form:form action="updateVideo.html" modelAttribute="videoInfo"  >
     <form:hidden path="id"/>    
    <tr class="td1">
    <td vertical-align=middle width=80  align=right ><b>视频名称</b><span class="font2">*</span></td>
    <td width=800>
    <form:input path="title" cssClass="input2" size="100" maxlength="100" onblur="changeStyle('titleMsg','msg1');" onfocus="changeStyle('titleMsg','msg2');"/>
    <div class="msg1" id="titleMsg" >请填写全英文的视频名称</div>
      
    </td>
    <td >
    
    </td>
    </tr>    
    <tr >
    <td vertical-align=middle width=80  align=right ><b>设置优先级</b><span class="font2">*</span></td>
    <td width=200>
    <form:input path="priority" cssClass="input2" size="40" maxlength="4" onblur="changeStyle('priorityMsg','msg1');" onfocus="changeStyle('priorityMsg','msg2');"/>
    <div class="msg1" id="priorityMsg" >设置优先级</div>
      
    </td>
    <td >
    
    </td>
    </tr>	
    <tr >
    <td vertical-align=middle width=80  align=right ><b>选择产品类型</b><span class="font2">*</span></td>
    <td width=200>
    <form:select path="type" items="${videoInfo.typeMap}"  cssClass="input2"  onblur="changeStyle('priorityMsg','msg1');" onfocus="changeStyle('priorityMsg','msg2');"/>
    <div class="msg1" id="typeMsg" >选择产品类型</div>
      
    </td>
    <td >
    
    </td>
    </tr>
    	
    <tr>
    <td vertical-align="middle" align="right"> <strong>视频地址</strong></td>
    <td vertical-align="top">
      <form:input path="videourl" cssClass="input2" size="100" maxlength="140" onblur="changeStyle('titleMsg','msg1');" onfocus="changeStyle('titleMsg','msg2');"/>
    </td>
    <td>
    	
    </td>   
  </tr> 
   <tr>
  <td colspan="3" >
          <strong>
           比如：https://www.youtube.com/embed/gotZFmB0yyM
          </strong></td>
  </tr> 
  
  
    <tr class="td1">    
    <td colspan="3" align=center> 
       <input type="submit" value="修 改" onclick="disabled=true;this.form.submit();"/>
    
    </td>
    </tr>    
   </form:form>
    </table>
    
  </body>
</html>
