<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/jquery-multi-select/css/multi-select.css' />" />

    <!--tags input-->
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/jquery-tags-input/jquery.tagsinput.css' />" />

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->



    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="saveSpirit?${_csrf.parameterName}=${_csrf.token}" modelAttribute="enyanSpirit" method="post" enctype="multipart/form-data" role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    添加灵修书籍
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active">
                        添加灵修书籍
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                添加灵修书籍
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="name">书籍名称</label>
                                    <div class="col-sm-10">
                                        <form:input path="name" cssClass="form-control" id="name" maxlength="50"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="author">作者</label>
                                    <div class="col-sm-10">
                                        <form:input path="author" cssClass="form-control" id="author" maxlength="50"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="isInCn">书籍语言</label>
                                    <div class="col-sm-10">
                                        <form:radiobutton path="languageType" value="1" checked="checked"/><spring:message code="book.sc"/>
                                        <form:radiobutton path="languageType" value="2" id="isInCn"/><spring:message code="book.tc"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="isInCn">上架状态</label>
                                    <div class="col-sm-10">
                                        <form:radiobutton path="shelfStatus" value="1" checked="checked"/>上架
                                        <form:radiobutton path="shelfStatus" value="0" id="isInCn"/>下架
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="days">推荐优先级</label>
                                    <div class="col-sm-10">
                                        <form:input path="recommendedOrder" cssClass="form-control" id="days" maxlength="50"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="days">天数</label>
                                    <div class="col-sm-10">
                                        <form:input path="days" cssClass="form-control" id="days" maxlength="50"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="tags_1">不需要读的天</label>
                                    <div class="col-sm-10">
                                        <form:input path="daysExclude" cssClass="form-control tags" id="tags_1" maxlength="50"/>
                                        直接输入要排除的天数（如：7、14、21）
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="bookImgUrl">我的灵修-封面图</label>
                                    <div class="col-sm-10">
                                        <form:input path="bookImgUrl" cssClass="form-control" id="bookImgUrl" maxlength="100"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="infoImgUrl">全部灵修-封面图（2：1）</label>
                                    <div class="col-sm-10">
                                        <form:input path="infoImgUrl" cssClass="form-control" id="infoImgUrl" maxlength="100"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="toBuyImgUrl">灵修内页-去购买的图片</label>
                                    <div class="col-sm-10">
                                        <form:input path="toBuyImgUrl" cssClass="form-control" id="toBuyImgUrl" maxlength="100"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="uploadBook">上传书籍</label>
                                    <div class="col-sm-10">
                                        <input type="file" name="uploadBook" class="input2" size="40" id="uploadBook">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="slogan">Slogan</label>
                                    <div class="col-sm-10">
                                        <form:textarea path="slogan" cssClass="form-control ckeditor" id="slogan" rows="6"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="bookDescription">本书简介：</label>
                                    <div class="col-sm-10">
                                        <form:textarea path="bookDescription" cssClass="form-control ckeditor" id="bookDescription" rows="6"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="authorDescription">作者简介：</label>
                                    <div class="col-sm-10">
                                        <form:textarea path="authorDescription" cssClass="form-control ckeditor" id="authorDescription" rows="6"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" for="copyright">版权信息</label>
                                    <div class="col-sm-10">
                                        <form:textarea path="copyright" cssClass="form-control ckeditor" id="copyright" rows="6"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 col-sm-2 control-label" >关联书籍</label>
                                    <div class="col-sm-10 input-group input-large">
                                        <form:select path="bookIDs" items="${bookIDsList}" itemLabel="name" itemValue="value" cssClass="multi-select" multiple="true" id="my_multi_select3" />
                                    </div>
                                </div>
                                <div class="col-lg-offset-2 col-lg-10">
                                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">提  交</button>
                                </div>
                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#spiritList").addClass("active");
    })
</script>
<!--multi-select-->
<script type="text/javascript" src="<c:url value='/statics/js/jquery-multi-select/js/jquery.multi-select.js' />"></script>
<script type="text/javascript" src="<c:url value='/statics/js/jquery-multi-select/js/jquery.quicksearch.js' />"></script>
<script src="<c:url value='/statics/js/multi-select-init.js' />"></script>

<!--tags input-->
<script src="<c:url value='/statics/js/jquery-tags-input/jquery.tagsinput.js' />"></script>
<script src="<c:url value='/statics/js/tagsinput-init.js' />"></script>

</body>
</html>
