<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../adminLeft.jsp"/>
    <!-- left side end-->

    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">
            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->
        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>播客设置</h3>
            <ul class="breadcrumb">
                <li><a href=""><spring:message code="home"/></a></li>
                <li><a href="#">播客管理</a></li>
                <li class="active">播客设置</li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">
            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            播客全局设置
                        </header>
                        <!-- 消息显示 -->
                        <c:if test="${not empty successMsg}">
                            <div class="alert alert-success alert-dismissible">
                                <button type="button" class="close" data-dismiss="alert">&times;</button>
                                ${successMsg}
                            </div>
                        </c:if>
                        <c:if test="${not empty errorMsg}">
                            <div class="alert alert-danger alert-dismissible">
                                <button type="button" class="close" data-dismiss="alert">&times;</button>
                                ${errorMsg}
                            </div>
                        </c:if>
                        <div class="panel-body">
                            <form action="saveSettings?${_csrf.parameterName}=${_csrf.token}" method="post" class="form-horizontal" role="form">
                                
                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">播客平台名称</label>
                                    <div class="col-lg-6">
                                        <input type="text" name="platformName" value="恩言播客" class="form-control" placeholder="请输入播客平台名称"/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">平台描述</label>
                                    <div class="col-lg-8">
                                        <textarea name="platformDescription" class="form-control" rows="3" placeholder="请输入平台描述">恩言播客 - 传播福音，分享恩典</textarea>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">默认封面图片</label>
                                    <div class="col-lg-6">
                                        <input type="text" name="defaultCoverImage" value="" class="form-control" placeholder="请输入默认封面图片URL"/>
                                        <p class="help-block">当播客没有设置封面时使用的默认图片</p>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">每页显示数量</label>
                                    <div class="col-lg-6">
                                        <select name="pageSize" class="form-control">
                                            <option value="10" selected>10条/页</option>
                                            <option value="20">20条/页</option>
                                            <option value="50">50条/页</option>
                                            <option value="100">100条/页</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">自动发布</label>
                                    <div class="col-lg-6">
                                        <label class="checkbox-inline">
                                            <input type="checkbox" name="autoPublish" value="1"> 新增播客时自动发布
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">音频格式支持</label>
                                    <div class="col-lg-6">
                                        <label class="checkbox-inline">
                                            <input type="checkbox" name="supportedFormats" value="mp3" checked> MP3
                                        </label>
                                        <label class="checkbox-inline">
                                            <input type="checkbox" name="supportedFormats" value="wav"> WAV
                                        </label>
                                        <label class="checkbox-inline">
                                            <input type="checkbox" name="supportedFormats" value="m4a"> M4A
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">最大文件大小</label>
                                    <div class="col-lg-6">
                                        <div class="input-group">
                                            <input type="number" name="maxFileSize" value="100" class="form-control" min="1" max="1000"/>
                                            <span class="input-group-addon">MB</span>
                                        </div>
                                        <p class="help-block">单个音频文件的最大大小限制</p>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">缓存设置</label>
                                    <div class="col-lg-6">
                                        <label class="checkbox-inline">
                                            <input type="checkbox" name="enableCache" value="1" checked> 启用缓存
                                        </label>
                                        <p class="help-block">启用后可提高播客列表加载速度</p>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">统计功能</label>
                                    <div class="col-lg-6">
                                        <label class="checkbox-inline">
                                            <input type="checkbox" name="enableStats" value="1" checked> 启用播放统计
                                        </label>
                                        <label class="checkbox-inline">
                                            <input type="checkbox" name="enableLikes" value="1" checked> 启用点赞功能
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <button type="submit" class="btn btn-primary">保存设置</button>
                                        <button type="button" class="btn btn-default" onclick="resetForm()">重置</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </section>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            系统维护
                        </header>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>数据维护</h4>
                                    <div class="btn-group-vertical" role="group">
                                        <button type="button" class="btn btn-warning" onclick="refreshCache()">
                                            <i class="fa fa-refresh"></i> 刷新缓存
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="updateStats()">
                                            <i class="fa fa-bar-chart"></i> 更新统计数据
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="optimizeDatabase()">
                                            <i class="fa fa-database"></i> 优化数据库
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h4>导入导出</h4>
                                    <div class="btn-group-vertical" role="group">
                                        <button type="button" class="btn btn-primary" onclick="exportData()">
                                            <i class="fa fa-download"></i> 导出播客数据
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="importData()">
                                            <i class="fa fa-upload"></i> 导入播客数据
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
        <!--body wrapper end-->

        <jsp:include page="../footer_show.jsp"></jsp:include>

    </div>
    <!-- main content end-->
</section>

<jsp:include page="../footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        // 先清除所有菜单的激活状态
        $(".menu-list").removeClass("nav-active");
        $(".menu-list li").removeClass("active");

        // 激活播客管理菜单
        $("#menu_podcast").addClass("nav-active");
        $("#podcastSettings").addClass("active");
    });

    function resetForm() {
        if (confirm("确定要重置所有设置吗？")) {
            document.forms[0].reset();
        }
    }

    function refreshCache() {
        if (confirm("确定要刷新缓存吗？")) {
            alert("缓存刷新功能开发中...");
        }
    }

    function updateStats() {
        if (confirm("确定要更新统计数据吗？")) {
            alert("统计数据更新功能开发中...");
        }
    }

    function optimizeDatabase() {
        if (confirm("确定要优化数据库吗？此操作可能需要一些时间。")) {
            alert("数据库优化功能开发中...");
        }
    }

    function exportData() {
        alert("数据导出功能开发中...");
    }

    function importData() {
        alert("数据导入功能开发中...");
    }
</script>

</body>
</html>
