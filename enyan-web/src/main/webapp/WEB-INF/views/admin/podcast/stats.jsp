<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../adminLeft.jsp"/>
    <!-- left side end-->

    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">
            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->
        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>播客统计</h3>
            <ul class="breadcrumb">
                <li><a href=""><spring:message code="home"/></a></li>
                <li><a href="#">播客管理</a></li>
                <li class="active">播客统计</li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">
            <div class="row">
                <!-- 统计卡片 -->
                <div class="col-lg-3 col-md-6">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-3">
                                    <i class="fa fa-microphone fa-5x"></i>
                                </div>
                                <div class="col-xs-9 text-right">
                                    <div class="huge">${totalPodcasts}</div>
                                    <div>播客总数</div>
                                </div>
                            </div>
                        </div>
                        <a href="<c:url value='/admin/podcast/list' />">
                            <div class="panel-footer">
                                <span class="pull-left">查看详情</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="panel panel-green">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-3">
                                    <i class="fa fa-play-circle fa-5x"></i>
                                </div>
                                <div class="col-xs-9 text-right">
                                    <div class="huge">${totalEpisodes}</div>
                                    <div>单集总数</div>
                                </div>
                            </div>
                        </div>
                        <a href="<c:url value='/admin/podcast/episode/list' />">
                            <div class="panel-footer">
                                <span class="pull-left">查看详情</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="panel panel-yellow">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-3">
                                    <i class="fa fa-check-circle fa-5x"></i>
                                </div>
                                <div class="col-xs-9 text-right">
                                    <div class="huge">${publishedPodcasts}</div>
                                    <div>已发布播客</div>
                                </div>
                            </div>
                        </div>
                        <a href="<c:url value='/admin/podcast/list' />">
                            <div class="panel-footer">
                                <span class="pull-left">查看详情</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="panel panel-red">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-3">
                                    <i class="fa fa-volume-up fa-5x"></i>
                                </div>
                                <div class="col-xs-9 text-right">
                                    <div class="huge">${publishedEpisodes}</div>
                                    <div>已发布单集</div>
                                </div>
                            </div>
                        </div>
                        <a href="<c:url value='/admin/podcast/episode/list' />">
                            <div class="panel-footer">
                                <span class="pull-left">查看详情</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            播客数据概览
                        </header>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>发布状态统计</h4>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-success" role="progressbar" 
                                             style="width: ${totalPodcasts > 0 ? (publishedPodcasts * 100 / totalPodcasts) : 0}%">
                                            已发布播客: ${publishedPodcasts}/${totalPodcasts}
                                        </div>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-info" role="progressbar" 
                                             style="width: ${totalEpisodes > 0 ? (publishedEpisodes * 100 / totalEpisodes) : 0}%">
                                            已发布单集: ${publishedEpisodes}/${totalEpisodes}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h4>快捷操作</h4>
                                    <div class="btn-group-vertical" role="group">
                                        <a href="<c:url value='/admin/podcast/addUI' />" class="btn btn-primary">
                                            <i class="fa fa-plus"></i> 新增播客
                                        </a>
                                        <a href="<c:url value='/admin/podcast/episode/addUI' />" class="btn btn-success">
                                            <i class="fa fa-plus"></i> 新增单集
                                        </a>
                                        <a href="<c:url value='/admin/topic/addUI' />" class="btn btn-info">
                                            <i class="fa fa-plus"></i> 新增专题
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
        <!--body wrapper end-->

        <jsp:include page="../footer_show.jsp"></jsp:include>

    </div>
    <!-- main content end-->
</section>

<jsp:include page="../footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        // 先清除所有菜单的激活状态
        $(".menu-list").removeClass("nav-active");
        $(".menu-list li").removeClass("active");

        // 激活播客管理菜单
        $("#menu_podcast").addClass("nav-active");
        $("#podcastStats").addClass("active");
    });
</script>

<style>
    .huge {
        font-size: 40px;
    }
    .panel-green {
        border-color: #5cb85c;
    }
    .panel-green > .panel-heading {
        border-color: #5cb85c;
        color: white;
        background-color: #5cb85c;
    }
    .panel-green > a {
        color: #5cb85c;
    }
    .panel-green > a:hover {
        color: #3d8b3d;
    }
    .panel-yellow {
        border-color: #f0ad4e;
    }
    .panel-yellow > .panel-heading {
        border-color: #f0ad4e;
        color: white;
        background-color: #f0ad4e;
    }
    .panel-yellow > a {
        color: #f0ad4e;
    }
    .panel-yellow > a:hover {
        color: #df8a13;
    }
    .panel-red {
        border-color: #d9534f;
    }
    .panel-red > .panel-heading {
        border-color: #d9534f;
        color: white;
        background-color: #d9534f;
    }
    .panel-red > a {
        color: #d9534f;
    }
    .panel-red > a:hover {
        color: #c12e2a;
    }
</style>

</body>
</html>
