<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->

    <script src="<c:url value='/statics/js/laydate/laydate.js' />"></script>
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="save?${_csrf.parameterName}=${_csrf.token}" modelAttribute="record" method="post" enctype="multipart/form-data" role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <c:choose>
                        <c:when test="${record.podcastId==null}">
                            新增播客栏目
                        </c:when>
                        <c:otherwise>
                            编辑播客栏目
                        </c:otherwise>
                    </c:choose>
                </h3>
                <ul class="breadcrumb">
                    <li><a href=""><spring:message code="home"/></a></li>
                    <li><a href="#">播客管理</a></li>
                    <li><a href="list">栏目管理</a></li>
                    <li class="active">
                        <c:choose>
                            <c:when test="${record.podcastId==null}">
                                新增栏目
                            </c:when>
                            <c:otherwise>
                                编辑栏目
                            </c:otherwise>
                        </c:choose>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">
                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                <c:choose>
                                    <c:when test="${record.podcastId==null}">
                                        新增播客栏目
                                    </c:when>
                                    <c:otherwise>
                                        编辑播客栏目
                                    </c:otherwise>
                                </c:choose>
                            </header>
                            <div
                                <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> 
                                <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                ${msg}
                            </div>
                            <div class="panel-body">
                                <form role="form">
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="title">栏目标题 *</label>
                                        <div class="col-sm-10">
                                            <form:input path="title" cssClass="form-control" id="title" maxlength="100" placeholder="请输入栏目标题"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="authorName">主持人/作者 *</label>
                                        <div class="col-sm-10">
                                            <form:input path="authorName" cssClass="form-control" id="authorName" maxlength="50" placeholder="请输入主持人或作者名称"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="description">栏目描述</label>
                                        <div class="col-sm-10">
                                            <form:textarea path="description" cssClass="form-control ckeditor" id="description" rows="4" placeholder="请输入栏目描述"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="coverImageUrl">封面图片URL</label>
                                        <div class="col-sm-10">
                                            <form:input path="coverImageUrl" cssClass="form-control" id="coverImageUrl" maxlength="500" placeholder="请输入封面图片URL"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="coverImageUrl2">详情页长条图片URL</label>
                                        <div class="col-sm-10">
                                            <form:input path="coverImageUrl2" cssClass="form-control" id="coverImageUrl2" maxlength="500" placeholder="请输入详情页长条图片URL"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="displayOrder">显示顺序</label>
                                        <div class="col-sm-10">
                                            <form:input path="displayOrder" cssClass="form-control" id="displayOrder" type="number" placeholder="数字越大越靠前"/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="isPublished">发布状态</label>
                                        <div class="col-sm-10">
                                            <form:radiobutton path="isPublished" value="1" id="isPublished"/>已发布
                                            <form:radiobutton path="isPublished" value="0"/>未发布
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2 col-sm-2 control-label" for="publicationDate">发布日期</label>
                                        <div class="col-sm-10">
                                            <form:input path="publicationDate" cssClass="form-control" id="publicationDate" placeholder="请选择发布日期"/>
                                            <script>
                                                laydate.render({
                                                    elem: '#publicationDate',
                                                    format: 'yyyy-MM-dd',
                                                    type: 'date'
                                                });
                                            </script>
                                        </div>
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <form:hidden path="podcastId"/>
                                        <form:hidden path="episodeCount"/>
                                        <form:hidden path="isDeleted"/>
                                        <form:hidden path="createdAt"/>
                                        <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">提  交</button>
                                        <a href="list" class="btn btn-default">返  回</a>
                                    </div>
                                </form>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <!--body wrapper end-->

            <jsp:include page="../footer_show.jsp"></jsp:include>

        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="../footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        // 先清除所有菜单的激活状态
        $(".menu-list").removeClass("nav-active");
        $(".menu-list li").removeClass("active");

        // 激活播客管理菜单
        $("#menu_podcast").addClass("nav-active");
        $("#podcastList").addClass("active");
    });
</script>
<script type="text/javascript" src="<c:url value='/statics/js/ckeditor/ckeditor.js' />"></script>
</body>
</html>
