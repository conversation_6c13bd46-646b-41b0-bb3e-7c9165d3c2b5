<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../adminLeft.jsp"/>
    <!-- left side end-->
    <form action="list?${_csrf.parameterName}=${_csrf.token}" method="post" enctype="multipart/form-data" class="form-inline" role="form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->
                <div class="form-group">
                    <input name="searchText" value="${record.searchText}" size="25" maxlength="50" class="form-control" placeholder="请输入播客名称或ID"/>
                    <select name="searchType" style="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle" class="form-control m-bot15">
                        <option value="0" ${record.searchType == '0' ? 'selected' : ''}>按名称搜索</option>
                        <option value="1" ${record.searchType == '1' ? 'selected' : ''}>按ID搜索</option>
                    </select>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
                </div>
                <!--search end-->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>播客栏目管理</h3>
                <ul class="breadcrumb">
                    <li><a href=""><spring:message code="home"/></a></li>
                    <li><a href="#">播客管理</a></li>
                    <li class="active">栏目管理</li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">
                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                播客栏目管理
                                <span class="tools pull-right">
                                    <a href="addUI">新增栏目</a>
                                </span>
                            </header>
                            <!-- 消息显示 -->
                            <c:if test="${not empty successMsg}">
                                <div class="alert alert-success alert-dismissible">
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                    ${successMsg}
                                </div>
                            </c:if>
                            <c:if test="${not empty errorMsg}">
                                <div class="alert alert-danger alert-dismissible">
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                    ${errorMsg}
                                </div>
                            </c:if>
                            <div class="panel-body">
                                <div class="adv-table">
                                    <form id="batchForm" action="batchDelete?${_csrf.parameterName}=${_csrf.token}" method="post">
                                        <div class="clearfix">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                                            </div>
                                        </div>
                                        <table class="table table-hover general-table">
                                            <thead>
                                            <tr>
                                                <th><input type="checkbox" id="checkAll"></th>
                                                <th>ID</th>
                                                <th>栏目标题</th>
                                                <th>作者</th>
                                                <th>单集数</th>
                                                <th>发布状态</th>
                                                <th>显示顺序</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <c:forEach var="podcast" items="${page.records}">
                                                <tr>
                                                    <td><input type="checkbox" name="ids" value="${podcast.podcastId}"></td>
                                                    <td>${podcast.podcastId}</td>
                                                    <td>
                                                        <a href="get-${podcast.podcastId}">${podcast.title}</a>
                                                    </td>
                                                    <td>${podcast.authorName}</td>
                                                    <td>${podcast.episodeCount}</td>
                                                    <td>
                                                        <c:choose>
                                                            <c:when test="${podcast.isPublished == 1}">
                                                                <span class="label label-success label-mini">已发布</span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <span class="label label-danger label-mini">未发布</span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                    <td>${podcast.displayOrder}</td>
                                                    <td>
                                                        <fmt:formatDate value="${podcast.createdAt}" pattern="yyyy-MM-dd HH:mm"/>
                                                    </td>
                                                    <td>
                                                        <a href="episode/list?podcastId=${podcast.podcastId}">单集管理</a> |
                                                        <a href="get-${podcast.podcastId}">编辑</a> |
                                                        <a href="del-${podcast.podcastId}" onclick="javascript:return checkDel()">删除</a>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </table>
                                    </form>
                                </div>
                                <c:if test="${empty page.records}">
                                    <div class="">
                                        <spring:message code="data.empty"/>
                                    </div>
                                </c:if>
                                <!--pagination start-->
                                <div class="">
                                    ${record.pageLand}
                                </div>
                                <!--pagination end-->
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <!--body wrapper end-->

            <jsp:include page="../footer_show.jsp"></jsp:include>

        </div>
        <!-- main content end-->
    </form>
</section>

<jsp:include page="../footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        // 先清除所有菜单的激活状态
        $(".menu-list").removeClass("nav-active");
        $(".menu-list li").removeClass("active");

        // 激活播客管理菜单
        $("#menu_podcast").addClass("nav-active");
        $("#podcastList").addClass("active");

        // 全选/取消全选
        $("#checkAll").click(function(){
            $("input[name='ids']").prop("checked", this.checked);
        });
        
        // 单个复选框点击事件
        $("input[name='ids']").click(function(){
            var total = $("input[name='ids']").length;
            var checked = $("input[name='ids']:checked").length;
            $("#checkAll").prop("checked", total === checked);
        });
    });

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
    
    function batchDelete() {
        var checkedIds = $("input[name='ids']:checked");
        if (checkedIds.length === 0) {
            alert("请选择要删除的记录！");
            return;
        }
        
        var msg = "您真的确定要批量删除这 " + checkedIds.length + " 条记录吗？\n\n请确认！";
        if (confirm(msg)) {
            $("#batchForm").submit();
        }
    }
</script>

</body>
</html>
