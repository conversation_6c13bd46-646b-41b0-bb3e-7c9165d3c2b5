<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-daterangepicker/daterangepicker-bs3.css' />" />
    <link rel="stylesheet" type="text/css" href="<c:url value='/statics/js/bootstrap-datepicker/css/datepicker-custom.css' />" />
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="saveDiscount?${_csrf.parameterName}=${_csrf.token}" modelAttribute="enyanOrder" method="post" enctype="multipart/form-data" role="form" cssClass="form-horizontal adminex-form">
        <!-- main content start-->
        <div class="main-content">

            <!-- header section start-->
            <div class="header-section">

                <!--toggle button start-->
                <a class="toggle-btn"><i class="fa fa-bars"></i></a>
                <!--toggle button end-->

                <!--search start-->


                <!--search end-->

                <!--notification menu start -->

                <!--notification menu end -->

            </div>
            <!-- header section end-->

            <!-- page heading start-->
            <div class="page-heading">
                <h3>
                    <spring:message code="order.detail"/>
                </h3>
                <ul class="breadcrumb">
                    <li>
                        <a href=""><spring:message code="home"/></a>
                    </li>
                    <li>
                        <a href="#"><spring:message code="menu.web"/></a>
                    </li>
                    <li class="active">
                        <spring:message code="order.detail"/>
                    </li>
                </ul>
            </div>
            <!-- page heading end-->

            <!--body wrapper start-->
            <div class="wrapper">


                <div class="row">
                    <div class="col-sm-12">
                        <section class="panel">
                            <header class="panel-heading">
                                订单号：${orderMain.orderNum}
                            </header>
                            <div
                                    <c:if test='${isSaveError }'>class="alert alert-danger"</c:if> <c:if test='${isSaveSuccess }'>class="alert alert-success"</c:if>>
                                    ${msg}
                            </div>
                            <div class="panel-body">

                                <form role="form">

                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th>图书名称</th>
                                            <th>ESIN</th>
                                            <th class="text-center">单价</th>
                                            <th class="text-center">数量</th>
                                            <th class="text-center">小计</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="discountList" items="${order.cartDiscountInfoList}" varStatus="status">
                                            <c:forEach var="productInfo" items="${discountList.productInfoList}" varStatus="status">
                                                <tr>
                                                    <td>
                                                        <a href="/book-${productInfo.code}" target="_blank">
                                                            <c:if test="${productInfo.salesModel == 1}">
                                                                <spring:message code="book.title.presale"/>
                                                            </c:if>
                                                                ${productInfo.name}
                                                        </a>
                                                    </td>
                                                    <td>
                                                            ${productInfo.bookEsin}
                                                    </td>
                                                    <td class="text-center text-lg text-medium">
                                                        <c:choose>
                                                            <c:when test="${productInfo.discountAnyIsValid}">
                                                                <del>HK$ ${productInfo.priceHkd}</del>
                                                                HK$ ${productInfo.priceHKDDiscount}
                                                            </c:when>
                                                            <c:otherwise>
                                                                HK$ ${productInfo.priceHkd}
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                    <td class="text-center text-lg text-medium">${productInfo.quantity}</td>
                                                    <td class="text-center text-lg text-medium">
                                                        <c:choose>
                                                            <c:when test="${productInfo.discountAnyIsValid}">
                                                                HK$ ${productInfo.priceHKDDiscount*productInfo.quantity}
                                                            </c:when>
                                                            <c:otherwise>
                                                                HK$ ${productInfo.priceHkd*productInfo.quantity}
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:forEach>
                                        </tbody>
                                    </table>

                                    <div class="col-sm-12">
                                        <ol class="list-unstyled">
                                            <li><span class="text-muted">Email：</span> ${orderMain.userEmail}</li>
                                            <c:if test="${orderMain.orderDetailInfo.amountCoupon > 0}">
                                                <li><span class="text-muted"><spring:message code="coupon.value.label"/>：</span> HK$${orderMain.orderDetailInfo.amountCoupon}</li>
                                            </c:if>
                                        </ol>
                                    </div>

                                    <div class="col-sm-12">
                                        <ol class="list-unstyled">
                                            <li>
                                                <p>付费信息：<br>
                                                    <code><c:out value="${payInfo}" default="无付费信息" escapeXml="false"></c:out></code><br>
                                                        <%-- <a href="#" onclick="createAcsm(${orderMain.orderId})">重新生成下载信息</a> createAcsm-orderId --%>
                                                </p>

                                            </li>
                                        </ol>
                                    </div>

                                </form>

                            </div>
                        </section>
                    </div>

                </div>


            </div>
            <!--body wrapper end-->


            <jsp:include page="footer_show.jsp"></jsp:include>


        </div>
        <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>
<script type="text/javascript" src="<c:url value='/statics/js/aaron-admin.js' />"></script>
<script>
    $(document).ready(function(){
        //do something
        $("#menu_balance").addClass("nav-active");
        $("#orderList").addClass("active");
    })
</script>

</body>
</html>
