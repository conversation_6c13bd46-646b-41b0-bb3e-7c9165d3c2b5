<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header">

<section>
    <!-- left side start-->
    <jsp:include page="../adminLeft.jsp"/>
    <!-- left side end-->

    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">
            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->
        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                <c:choose>
                    <c:when test="${empty record.topicId}">新增专题</c:when>
                    <c:otherwise>编辑专题</c:otherwise>
                </c:choose>
            </h3>
            <ul class="breadcrumb">
                <li><a href=""><spring:message code="home"/></a></li>
                <li><a href="#">播客管理</a></li>
                <li><a href="list">专题管理</a></li>
                <li class="active">
                    <c:choose>
                        <c:when test="${empty record.topicId}">新增专题</c:when>
                        <c:otherwise>编辑专题</c:otherwise>
                    </c:choose>
                </li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">
            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            <c:choose>
                                <c:when test="${empty record.topicId}">新增专题</c:when>
                                <c:otherwise>编辑专题</c:otherwise>
                            </c:choose>
                        </header>
                        <!-- 消息显示 -->
                        <c:if test="${not empty successMsg}">
                            <div class="alert alert-success alert-dismissible">
                                <button type="button" class="close" data-dismiss="alert">&times;</button>
                                ${successMsg}
                            </div>
                        </c:if>
                        <c:if test="${not empty errorMsg}">
                            <div class="alert alert-danger alert-dismissible">
                                <button type="button" class="close" data-dismiss="alert">&times;</button>
                                ${errorMsg}
                            </div>
                        </c:if>
                        <div class="panel-body">
                            <form action="save?${_csrf.parameterName}=${_csrf.token}" method="post" class="form-horizontal" role="form">
                                <input type="hidden" name="topicId" value="${record.topicId}"/>
                                
                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">所属播客 <span style="color: red;">*</span></label>
                                    <div class="col-lg-6">
                                        <select name="podcastId" class="form-control m-bot15" required>
                                            <option value="">请选择播客</option>
                                            <c:forEach var="podcast" items="${podcastList}">
                                                <option value="${podcast.podcastId}" ${record.podcastId == podcast.podcastId ? 'selected' : ''}>${podcast.title}</option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">专题标题 <span style="color: red;">*</span></label>
                                    <div class="col-lg-6">
                                        <input type="text" name="title" value="${record.title}" class="form-control" placeholder="请输入专题标题" required maxlength="255"/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">专题描述</label>
                                    <div class="col-lg-8">
                                        <textarea name="description" class="form-control" rows="5" placeholder="请输入专题描述（可选）">${record.description}</textarea>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-lg-2 col-sm-2 control-label">显示顺序</label>
                                    <div class="col-lg-6">
                                        <input type="number" name="displayOrder" value="${record.displayOrder}" class="form-control" placeholder="请输入显示顺序，数字越小越靠前" min="0"/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="col-lg-offset-2 col-lg-10">
                                        <button type="submit" class="btn btn-primary">保存</button>
                                        <a href="list" class="btn btn-default">取消</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </section>
                </div>
            </div>
        </div>
        <!--body wrapper end-->

        <jsp:include page="../footer_show.jsp"></jsp:include>

    </div>
    <!-- main content end-->
</section>

<jsp:include page="../footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        // 先清除所有菜单的激活状态
        $(".menu-list").removeClass("nav-active");
        $(".menu-list li").removeClass("active");

        // 激活播客管理菜单
        $("#menu_podcast").addClass("nav-active");
        $("#topicList").addClass("active");
    });
</script>

</body>
</html>
