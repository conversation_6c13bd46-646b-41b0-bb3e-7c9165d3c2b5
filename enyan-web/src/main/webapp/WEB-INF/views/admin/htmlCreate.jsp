<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
String path = request.getContextPath();
String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>后台</title>
<link href="<%=basePath %>css/admin.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="<%=basePath %>js/comm.js"></script>
</head>

<body>
<table width="100%" border="0">
  <tr>
    <td>当前位置：网站管理 &gt;&gt; 生成页面</td>
  </tr>
</table>
  <table width="90%"  border="0" align="center" cellpadding="10" cellspacing="0" class="table1">
    <tr>
      <td><table width="100%"  border="0" cellpadding="5" cellspacing="0">       
        <tr>
        <td colspan="2">
        </td>
      </tr>
      <tr >
        <td colspan="2"  <c:if test='${isSaveError }'>class="errormsg"</c:if> <c:if test='${isSaveSuccess }'>class="msg3"</c:if>>
        ${msg}
        </td>
         
      </tr>
      </table>
<table width="100%" border="0" cellpadding="5" cellspacing="0">
  <tr class="td1">   
    <td vertical-align="middle" align="right"  width="120" ><b>单个产品生成</b></td>
   <td width="800" height="80">
        <a href="htmlProduct.html" >单个产品生成</a>
    </td>
    <td >
    
    </td>
    </tr>  
  <tr >   
    <td vertical-align="middle" align="right"  ><b>首页生成</b></td>
   <td width="800" height="80">
        <a href="htmlIndex.html" >首页生成</a>
    </td>
    <td >
    
    </td>
    </tr> 
    <tr class="td1">   
    <td vertical-align="middle" align="right"  ><b>产品列表生成</b></td>
   <td width="800" height="80">
        <a href="htmlProducts.html" >产品列表生成</a>
    </td>
    <td >
    
    </td>
    </tr> 
    
    </tr> 
    <tr >   
    <td vertical-align="middle" align="right"  ><b>视频列表生成</b></td>
   <td width="800" height="80">
        <a href="htmlVideos.html" >视频列表生成</a>
    </td>
    <td >
    
    </td>
    </tr> 
     <tr class="td1">   
    <td vertical-align="middle" align="right"  ><b>“关于我们“生成</b></td>
   <td width="800" height="80">
        <a href="htmlAboutUs.html" >“关于我们“生成</a>
    </td>
    <td >
    
    </td>
    </tr>
    
     <tr >   
    <td vertical-align="middle" align="right"  ><b>“新闻列表“生成</b></td>
   <td width="800" height="80">
        <a href="htmlNews.html" >“新闻列表“生成</a>
    </td>
    <td >
    
    </td>
    </tr>
    
    <tr class="td1">   
    <td vertical-align="middle" align="right"  ><b>“支持列表“生成</b></td>
   <td width="800" height="80">
        <a href="htmlSupports.html" >“支持列表“生成</a>
    </td>
    <td >
    
    </td>
    </tr>
    
    <tr class="td1">   
    <td vertical-align="middle" align="right"  ><b>单个新闻及支持 生成</b></td>
   <td width="800" height="80">
        <a href="htmlArticle.html" >单个新闻及支持 生成</a>
    </td>
    <td >
    
    </td>
    </tr>
</table>

      </td>
    </tr>
  </table>
</body>
</html>
