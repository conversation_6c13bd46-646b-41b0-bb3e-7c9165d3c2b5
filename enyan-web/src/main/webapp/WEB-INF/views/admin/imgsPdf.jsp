<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="ThemeBucket">
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">

    <title><spring:message code="main.title"/></title>

    <link href="<c:url value='/statics/css/style.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/style-responsive.css' />" rel="stylesheet">
    <link href="<c:url value='/statics/css/bootstrap-select.min.css' />" rel="stylesheet">
    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="<c:url value='/statics/js/html5shiv.js' />"></script>
    <script src="<c:url value='/statics/js/respond.min.js' />"></script>
    <![endif]-->
</head>

<body class="sticky-header left-side-collapsed">

<section>
    <!-- left side start-->
    <jsp:include page="adminLeft.jsp"/>
    <!-- left side end-->
    <form:form action="imgs?${_csrf.parameterName}=${_csrf.token}&imageType=1" modelAttribute="enyanImg" method="post" enctype="multipart/form-data" cssClass="form-inline" role="form">
    <!-- main content start-->
    <div class="main-content">

        <!-- header section start-->
        <div class="header-section">

            <!--toggle button start-->
            <a class="toggle-btn"><i class="fa fa-bars"></i></a>
            <!--toggle button end-->

            <!--search start-->


                <div class="form-group">
                    <form:input path="searchText" size="25" maxlength="50" cssClass="form-control" placeholder="请输入"/>
                    <form:select path="searchType" items="${enyanImg.searchList}" itemLabel="name" itemValue="value"
                                 cssStyle="margin-top: 1.5rem; margin-left: .5rem; min-height: 3.5rem; vertical-align: middle"
                                 cssClass="form-control m-bot15"/>
                    <button type="submit" class="btn btn-primary" onclick="disabled=true;this.form.submit();">搜  索</button>
                </div>


            <!--search end-->

            <!--notification menu start -->

            <!--notification menu end -->

        </div>
        <!-- header section end-->

        <!-- page heading start-->
        <div class="page-heading">
            <h3>
                PDF列表
            </h3>
            <ul class="breadcrumb">
                <li>
                    <a href=""><spring:message code="home"/></a>
                </li>
                <li>
                    <a href="#"><spring:message code="menu.web"/></a>
                </li>
                <li class="active">PDF列表</li>
            </ul>
        </div>
        <!-- page heading end-->

        <!--body wrapper start-->
        <div class="wrapper">


            <div class="row">
                <div class="col-sm-12">
                    <section class="panel">
                        <header class="panel-heading">
                            PDF列表
                            <span class="tools pull-right">
                            <a href="addImgUI?imageType=1">添加PDF</a>
                         </span>
                        </header>
                        <div class="panel-body">
                            <table class="table  table-hover general-table">
                                <thead>
                                <tr>
                                    <th> 描述</th>
                                    <th>链接(点击复制)</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>

                                <c:forEach var="list" items="${list}">
                                <tr vertical-align="middle">
                                    <td vertical-align="center">
                                        <a href="get-${list.imgId}">${list.imgDescription}</a>
                                    </td>

                                    <td vertical-align="center">
                                        <textarea name="" id="copy_msg_${list.imgId}" readonly onclick="copyText($('#copy_msg_${list.imgId}'))" cols="70"><at:bookImage imageName='${list.imgName}' scope='sample'/></textarea>
                                        <%--<input type="button" id="copy_url_${list.imgId}" value="复制" onclick="copyUrl($('#copy_msg_${list.imgId}'))">--%>
                                        <%--<script>copyUrl($('#copy_msg_${list.imgId}'));</script>--%>
                                    </td>
                                    <td vertical-align="center">
                                        <a href="get-${list.imgId}">&nbsp;编辑｜</a>&nbsp;
                                        <a href="del-${list.imgId}" onclick="javascript:return checkDel()">｜删除 &nbsp;</a>
                                    </td>
                                </tr>
                                </c:forEach>

                                </tbody>
                            </table>
                            <!--pagination start-->
                            <div class="">
                                ${pageLand}
                            </div>
                            <!--pagination end-->
                        </div>
                    </section>

                </div>
            </div>
        </div>
        <!--body wrapper end-->


        <jsp:include page="footer_show.jsp"></jsp:include>


    </div>
    <!-- main content end-->
    </form:form>
</section>

<jsp:include page="footer_js.jsp"></jsp:include>

<script>
    $(document).ready(function(){
        //do something
        $("#menu_web").addClass("nav-active");
        $("#imgList1").addClass("active");
    })

    function checkDel() {
        var msg = "您真的确定要删除吗？\n\n请确认！";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }

    function copyText(dom) {
        dom.select();
        //alert(dom.text());
        document.execCommand("Copy");
        alert("已复制至剪切板");
    }

    /*function copyUrl(dom) {
        alert("copyUrl");
        dom.click(function () {
            var url = $(this).prev(); //根据实际情况更改,需要复制内容的载体
            url.select();
            dom.select();
            alert(dom);
            alert(dom.val());
            alert(dom.text());
            console.log(url);
            document.execCommand("Copy");
            alert("已复制至剪切板");
        })
    }*/
</script>

</body>
</html>
