<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>阅读器下载-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Favicon Icons-->
    <link rel="icon" type="image/x-icon" href="<c:url value='/favicon.ico'/>">
    <link rel="icon" type="image/png" href="<c:url value='/favicon.png' />">
    <!-- Main Template Styles-->
    <style type="text/css">
        html {
            height: 100%;
        }
        body {
            min-height: 100%;
            background-image: url('<c:url value="/statics/images/wechat_bak.png" />');
            background-repeat: no-repeat;
            background-position: top center;
            background-size: cover;
        }
    </style>
    <!-- Modernizr-->
    <script type="text/javascript">
        // 获取终端的相关信息，根据终端辨别下载地址
        var Terminal = {
            // 辨别移动终端类型
            platform : function(){
                var u = navigator.userAgent, app = navigator.appVersion;
                return {
                    //IE内核
                    windows: u.indexOf('Windows') > -1,
                    //苹果、谷歌内核
                    webKit: u.indexOf('AppleWebKit') > -1,
                    //是否为移动终端
                    mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/),
                    //ios终端
                    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
                    // android终端
                    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,
                    // 是否为iPhone
                    iPhone: u.indexOf('iPhone') > -1 ,
                    // 是否iPad
                    iPad: u.indexOf('iPad') > -1,
                    //是否为mac系统
                    Mac: u.indexOf('Macintosh') > -1,
                    //是否web应该程序
                    webApp: u.indexOf('Safari') == -1,
                    //是否微信
                    weixin: u.indexOf('MicroMessenger') > -1,
                };
            }(),
        }

        // 根据不同的终端，跳转到不同的地址
        var theUrl = '/index-Reader';
        if(Terminal.platform.weixin){
            theUrl = '#';
        }else {
            location.href = theUrl;
        }

    </script>
</head>
<!-- Body-->
<body>

</body>
</html>