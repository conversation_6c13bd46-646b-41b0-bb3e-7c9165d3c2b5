<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<!-- Page Title-->
<%--
<div class="page-title">
    <div class="container">
        <div class="column">
            <h1>账户注销</h1>
        </div>
        <div class="column">
            <ul class="breadcrumbs">
                <li><a href="/">首页</a>
                </li>
                <li class="separator">&nbsp;</li>
                <li><a href="/myCenter">个人中心</a>
                </li>
                <li class="separator">&nbsp;</li>
                <li>账户注销</li>
            </ul>
        </div>
    </div>
</div>--%>
<!-- Page Content-->
<div class="container padding-top-3x padding-bottom-3x mb-2">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <form:form action="revokeAction?${_csrf.parameterName}=${_csrf.token}" modelAttribute="authUser" method="post" enctype="multipart/form-data" role="form" cssClass="close-box">
                <h4 class="margin-bottom-2x text-center">重要提示</h4>
                <p class="margin-bottom-1x">注销恩道电子书帐号是不可恢复的操作，在您申请注销前，请充分阅读、理解并同意下列事项：</p>
                <ol class="list-unstyled">
                    <li><span class="text-primary text-medium">1. 所有恩道平台均无法继续使用本账号</span>
                        <p>注销账号后，您将无法在所有恩道平台（网站/客户端）登录、使用本帐号。已经登录本账号的设备将自动登出账号。</p>
                    </li>
                    <li><span class="text-primary text-medium">2. 所有已购电子书和赠书兑换码将视为自动放弃</span>
                        <p>请您务必在注销前处理未送出的赠书兑换码。</p>
                    </li>
                    <li><span class="text-primary text-medium">3. 帐号相关信息将被清空且无法恢复</span>
                        <p>包括但不限于本账号的个人资料、阅读数据（读书/灵修进度、划线笔记、书签等）、购书记录、赠书记录、收藏数据等。建议您在最终确定注销前自行备份本帐号相关的所有重要信息。</p>
                    </li>
                    <li><span class="text-primary text-medium">4. 帐号注销后无法找回</span>
                        <p>如果您使用相同的电子邮箱再次注册，会以新的用户身份进行登录，依旧无法找回之前的帐号信息。</p>
                    </li>
                </ol>
                <div class="d-flex flex-wrap justify-content-between">
                    <label class="custom-control custom-checkbox">
                        <input class="custom-control-input" type="checkbox" id="agree" onchange="warning()"><span class="custom-control-indicator"></span><span class="custom-control-description" >我已阅读并接受以上事项<br><p class="text-primary mt-1" id="warning">您需要接受以上条款，方能进行注销</p></span>
                    </label>
                </div>
                <c:if test='${isSaveError }'>
                    <div class="form-group input-group">
                        <h6 class="text-danger">${msg}</h6>
                    </div>
                </c:if>
                <div class="form-group input-group">
                    <input class="form-control" type="email" placeholder="邮箱地址" required name="email"><span class="input-group-addon"><i class="icon-mail"></i></span>
                </div>
                <div class="form-group input-group">
                    <input class="form-control" type="password" placeholder="密码" required name="userPassword"><span class="input-group-addon"><i class="icon-lock"></i></span>
                </div>
                <div class="text-center">
                    <button class="btn btn-primary margin-bottom-none" type="submit" id="pay" disabled onclick="javascript:return checkRevoke()">申请注销</button>
                </div>
            </form:form>
        </div>
    </div>
</div>
<script>
    function warning(){
        var checkAllBox = document.getElementById("agree");
        var displayWarning = document.getElementById("warning");
        var pay = document.getElementById("pay");
        if(checkAllBox.checked){
            displayWarning.innerHTML="";
            pay.disabled = false;
        }else{
            displayWarning.innerHTML='您需要接受以上条款，方能进行注销';
            pay.disabled = true;
        }
    }
    function checkRevoke() {
        let msg = "点击确认注销，您的帐号将被立即注销，无法撤销，请谨慎操作。";
        if (confirm(msg)==true){
            return true;
        }else{
            return false;
        }
    }
</script>