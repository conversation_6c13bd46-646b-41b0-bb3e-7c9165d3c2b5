<%--
  User: Aaron
  Date: 2017/12/12
  Time: 上午10:24
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="pay.label"/> </title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <!-- Modernizr-->
    <jsp:include page="track-info.jsp"/>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-1 padding-top-2x">
        <!-- 购物车 begin-->
        <div class="table-responsive shopping-cart">
            <c:forEach var="discountList" items="${BUYER_CART.cartDiscountInfoList}" varStatus="status">
                <table class="table">
                    <thead>
                    <tr>
                        <th><c:out value="${discountList.discountTitle}" default="图书名称"/></th>
                        <th class="text-center">><spring:message code="book.price"/></th>
                        <th class="text-center"><spring:message code="book.amount"/> </th>
                        <th class="text-center"><spring:message code="book.total"/> </th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach var="cartLine" items="${discountList.productInfoList}">
                        <tr>
                            <td>
                                <div class="product-item">
                                    <a class="product-thumb" href="book-${cartLine.productInfo.code}">
                                    <img src="${cartLine.productInfo.productCover}" alt="Product"></a>
                                    <div class="product-info">
                                        <br/><h4 class="product-title">
                                        <a href="book-${cartLine.productInfo.code}">
                                            <c:if test="${cartLine.productInfo.salesModel == 1}">
                                                <spring:message code="book.title.presale"/>
                                            </c:if>
                                                ${cartLine.productInfo.name}
                                        </a></h4>
                                        <span><em><spring:message code="book.author"/> :</em>
                                            <c:if test="${not empty cartLine.productInfo.producer}">
                                                <c:set var="authors" value="${fn:split(cartLine.productInfo.producer, '#')}" />
                                                <c:forEach var="author" items="${authors}">
                                                    <a class="navi-link" href="searchBook?searchText=${author}">${author}</a>
                                                </c:forEach>
                                            </c:if>
                                        </span>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center text-lg text-medium">
                                <c:choose>
                                    <c:when test="${cartLine.productInfo.discountSingleIsValid}">
                                        <c:choose>
                                            <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                                <del>$ ${cartLine.productInfo.priceUsd}</del>
                                                $ ${cartLine.productInfo.priceUSDDiscount}
                                            </c:when>
                                            <c:otherwise>
                                                <del>¥${cartLine.productInfo.priceCny}</del>
                                                ¥${cartLine.productInfo.priceCnyDiscount}
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:choose>
                                            <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                                $ ${cartLine.productInfo.priceUsd}
                                            </c:when>
                                            <c:otherwise>
                                                ¥${cartLine.productInfo.priceCny}
                                            </c:otherwise>
                                        </c:choose>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="text-center text-lg text-medium">1</td>
                            <td class="text-center text-lg text-medium">
                                <c:choose>
                                    <c:when test="${cartLine.productInfo.discountSingleIsValid}">
                                        <c:choose>
                                            <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                                <del>$ ${cartLine.productInfo.priceUsd}</del>
                                                $ ${cartLine.productInfo.priceUSDDiscount}
                                            </c:when>
                                            <c:otherwise>
                                                <del>¥${cartLine.productInfo.priceCny}</del>
                                                ¥${cartLine.productInfo.priceCnyDiscount}
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:choose>
                                            <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                                $ ${cartLine.productInfo.priceUsd}
                                            </c:when>
                                            <c:otherwise>
                                                ¥${cartLine.productInfo.priceCny}
                                            </c:otherwise>
                                        </c:choose>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                    </c:forEach>
                    </tbody>
                </table>
            </c:forEach>
        </div>
        <!-- 购物车 end-->
        <div class="shopping-cart-footer">
            <!-- <div class="column">
              <form class="coupon-form" method="post">
                <input class="form-control form-control-sm" type="text" placeholder="" required>
                <button class="btn btn-outline-primary btn-sm" type="submit"></button>
              </form>
            </div> -->
            <div class="column text-lg  text-right">总计：
                <c:choose>
                    <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                        <span class="text-medium"> $  ${BUYER_CART.amountUsd}</span><br/>
                        <spring:message code="account.toPay"/> ： <span class="text-medium">$  ${BUYER_CART.amountUsd}</span>
                    </c:when>
                    <c:otherwise>
                        <span class="text-medium"> ¥  ${BUYER_CART.amountCny}</span><br/>
                        <spring:message code="account.toPay"/> ： <span class="text-medium">¥  ${BUYER_CART.amountCny}</span>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
        <!-- 支付方式-->
        <div class="col-xl-6 col-lg-12 padding-top-1x">
            <h4>支付方式</h4>
            <hr class="padding-bottom-1x">
            <div class="accordion" id="accordion" role="tablist">
                <c:choose>
                    <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">

                        <div class="card">
                            <div class="card-header" role="tab">
                                <h6><a href="#card" data-toggle="collapse" data-parent="#accordion"><spring:message code="pay.credit"/> </a></h6>
                            </div>
                            <div class="collapse" id="card" role="tabpanel">
                                <div class="card-body">
                                    <!-- checked 默认勾选 -->
                                    <label class="custom-control custom-checkbox">
                                        <input class="custom-control-input" type="checkbox" checked><span
                                            class="custom-control-indicator"></span><span
                                            class="custom-control-description"><spring:message code="pay.credit"/> : &nbsp;<img
                                            class="d-inline-block align-middle"
                                            src="<c:url value='/statics/images/credit-cards.png' />" style="width: 160px;"
                                            alt="信用卡"></span>
                                    </label>
                                    <div class="card-wrapper"></div>
                                    <form class="interactive-credit-card row">
                                        <div class="form-group col-sm-12">
                                            <input class="form-control" type="text" name="number"
                                                   placeholder="Card Number" required>
                                        </div>
                                        <div class="form-group col-sm-6">
                                            <input class="form-control" type="text" name="expiry" placeholder="MM/YY"
                                                   required>
                                        </div>
                                        <div class="form-group col-sm-6">
                                            <input class="form-control" type="text" name="cvc" placeholder="CVC" required>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </c:when>
                    <c:otherwise>
                        <div class="card">
                            <div class="card-header" role="tab">
                                <h6><a class="collapsed" href="#alipay" data-toggle="collapse"
                                       data-parent="#accordion"><spring:message code="alipay.label"/> </a></h6>
                            </div>
                            <div class="collapse show" id="alipay" role="tabpanel">
                                <div class="card-body">
                                    <!-- checked 默认勾选 -->
                                    <label class="custom-control custom-checkbox">
                                        <input class="custom-control-input" type="checkbox" checked><span
                                            class="custom-control-indicator"></span><span
                                            class="custom-control-description"><spring:message code="pay.alipay"/> : &nbsp;<img
                                            class="d-inline-block align-middle"
                                            src="<c:url value='/statics/images/credit-alipay.png' />" style="width: 75px;"
                                            alt="支付宝"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </c:otherwise>
                </c:choose>

            </div>
        </div>
        <div class="shopping-cart-footer">
            <div class="column">
                <label class="custom-control custom-checkbox d-block">
                    <input class="custom-control-input" type="checkbox" checked><span
                        class="custom-control-indicator"></span><span class="custom-control-description"><spring:message code="read.had"/>
                    <a class="text-medium text-decoration-none" href="index-Conditions"><spring:message code="protocal.user"/> </a></span>
                </label>
            </div>
            <div class="column"><a class="btn btn-outline-secondary" href="category-0-grid-0-0-0-0-0"
                                   data-toast-position="topRight"><i class="icon-arrow-left"></i>&nbsp;<spring:message code="buy.back"/> </a><a
                    class="btn btn-primary" href="checkout-${order.orderId}"><spring:message code="account.pay"/> </a></div>
        </div>


    </div>
<!-- Site Footer start-->
<jsp:include page="footer.jsp"/>
<!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
</body>
</html>
