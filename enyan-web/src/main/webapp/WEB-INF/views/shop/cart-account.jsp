<%--
  User: Aaron
  Date: 2017/12/9
  Time: 上午10:41
--%>
<%@ page import="java.util.Locale" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code='label.cart'/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <!-- Modernizr-->
    <script src="<c:url value='/js/modernizr.min.js' />"></script>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->
    <div class="page-title">
        <div class="container">
            <div class="column">
                <h1><spring:message code='label.cart'/></h1>
            </div>
            <div class="column">
                <ul class="breadcrumbs">
                    <li><a href="index.html">首页</a>
                    </li>
                    <li class="separator">&nbsp;</li>
                    <li><a href="account-orders.html">个人中心</a>
                    </li>
                    <li class="separator">&nbsp;</li>
                    <li><spring:message code='label.cart'/></li>
                </ul>
            </div>
        </div>
    </div>
    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-2">
        <div class="row">
            <div class="col-lg-4">
                <!-- 我的账户侧边栏 -->
                <nav class="list-group">
                    <a class="list-group-item" href="#"><h4>个人中心</h4><span>欢迎，用户昵称</span></a>
                    <a class="list-group-item" href="account-profile.html"><i class="icon-head"></i>我的账户</a>
                    <a class="list-group-item with-badge" href="account-orders.html"><i class="icon-bag"></i>我的订单<span class="badge badge-primary badge-pill">3</span></a>
                    <a class="list-group-item with-badge" href="account-wishlist.html"><i class="icon-heart"></i>我的收藏<span class="badge badge-primary badge-pill">3</span></a>
                    <a class="list-group-item with-badge  active" href="account-cart.html"><i class="icon-tag"></i>购物车<span class="badge badge-primary badge-pill">2</span></a>
                </nav>
            </div>
            <div class="col-lg-8">
                <div class="padding-top-2x mt-2 hidden-lg-up"></div>
                <div class="table-responsive shopping-cart sp-buttons ">
                    <table class="table">
                        <thead>
                        <tr>
                            <th>图书名称</th>
                            <th class="text-center">型号</th>
                            <th class="text-center">金额</th>
                            <th class="text-center">操作</th>
                            <th class="text-center"></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>
                                <div class="product-item"><a class="product-thumb" href="shop-single.html"><img src="img/shop/cart/01.jpg" alt="Product"></a>
                                    <div class="product-info">
                                        <h4 class="product-title"><a href="shop-single.html">以弗所书 • 歌罗西书 • 腓利门书</a></h4><span><em>作 者:</em>盖瑞·史密斯</span>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center text-lg text-medium">eIP021</td>
                            <td class="text-center text-lg text-medium">¥50.00</td>
                            <td class="text-center"><button class="btn btn-outline-secondary btn-sm btn-wishlist" data-toggle="tooltip" title="<spring:message code='label.wish'/>"><i class="icon-heart"></i></button></td>
                            <td class="text-center"><a class="remove-from-cart" href="delCart-${list.bookId}" data-toggle="tooltip" title="删除"><i class="icon-cross"></i></a></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <hr class="mb-4">
                <div class="text-right">
                    <div class="column"><a class="btn btn-outline-secondary" href="shop-grid-ls.html" data-toast-position="topRight"><i class="icon-arrow-left"></i>&nbsp;返回继续购物</a><a class="btn btn-primary" href="checkout.html">立即支付</a></div>
                </div>
            </div>
        </div>
    </div>
    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<!-- JavaScript (jQuery) libraries, plugins and custom scripts-->
<script src="<c:url value='/js/vendor.min.js' />"></script>
<script src="<c:url value='/js/scripts.min.js' />"></script>
</body>
</html>