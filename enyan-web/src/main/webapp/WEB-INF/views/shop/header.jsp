<%@ page import="com.aaron.util.CookieUtil" %>
<%@ page import="java.util.Locale" %>
<%@ page import="com.aaron.api.constant.InterfaceContant" %>
<%@ page import="java.net.URLEncoder" %>
<%@ page import="com.aaron.spring.common.Constant" %>
<%@ page import="org.apache.commons.lang3.StringUtils" %>
<%@ page import="com.aaron.spring.model.CurrencyType" %>
<%@ page import="javax.validation.spi.ConfigurationState" %>
<%@ page import="com.aaron.spring.common.WebUtil" %>
<%--
  User: Aaron
  Date: 2017/12/4
  Time: 下午6:46
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%
    /*
    String currency = Locale.SIMPLIFIED_CHINESE.getCountry();
    String value = CookieUtil.getCookieValue(InterfaceContant.CookieName.CURRENCY, String.class, request);

    if (Locale.TRADITIONAL_CHINESE.getCountry().equals(value)){
        currency = Locale.TRADITIONAL_CHINESE.getCountry();
    }*/
    String currency = CookieUtil.getCookieValue(InterfaceContant.CookieName.CURRENCY, String.class, (HttpServletRequest) request,false);
    if (StringUtils.isEmpty(currency)){
        currency = CurrencyType.CNY.getHeaderName();
    }else {
		currency = CurrencyType.getPriceNameByHeaderName(currency).getHeaderName();//后续可以屏蔽掉
    }

    request.setAttribute("currency",currency);
    request.setAttribute("categoryList", Constant.categoriesList);

	String baseServerPath = WebUtil.getBasePathNoSuffix();
    request.setAttribute("baseServerPath",baseServerPath);
%>

<!-- Off-Canvas Category Menu-->
<div class="offcanvas-container" id="shop-categories">
    <div class="offcanvas-header">
        <h3 class="offcanvas-title"><spring:message code="web.nav"/> </h3>
    </div>
    <nav class="offcanvas-menu">
        <ul class="menu">
<%--            <li class="has-children"><span><a href="${baseServerPath}/"><span><spring:message code="shop.index"/> </span></a></span></li>--%>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-0-0-0-0-0-0-0-0"><spring:message code="shop.nav.book.all"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-0-1-0-0-0-0-0-0"><spring:message code="shop.nav.inspirata"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-1-0-0-0-0-0-0-0"><spring:message code="shop.nav.book.sc"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-2-0-0-0-0-0-0-0"><spring:message code="shop.nav.book.tc"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-3-0-0-0-0-0-0-0"><spring:message code="shop.nav.book.eng"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-0-0-1-0-0-0-0-0"><spring:message code="shop.nav.book.free"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/readingIndex"><spring:message code="shop.nav.reading"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/rent-to-own"><spring:message code="shop.nav.rent"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/blogs"><spring:message code="shop.nav.blog"/> </a></span></li>
<%--            <li class="has-children"><span><a href="category-0-grid-0-0-0-1-0-0-0-0"><spring:message code="shop.nav.book.presale"/> </a></span></li>--%>
            <li class="has-children"><span><a href="${baseServerPath}/index-Reader"><spring:message code="shop.nav.reader"/> </a></span></li><%--index-Wechat--%>
        </ul>
    </nav>
</div>

<%--手持设备菜单栏--%>
<div class="offcanvas-container" id="mobile-menu">
<sec:authorize access="isAuthenticated() or isRememberMe()">
    <sec:authentication var="user" property="principal" />
    <nav class="offcanvas-menu">
        <ul>
            <li><a href="#"><span class="text-lg"><spring:message code="welcome"/>：${user.nickName}</span></a></li>
        </ul>
    </nav>
    <!-- 个人中心 -->
    <a class="account-link" href="#">
        <div class="user-info">
            <h6 class="user-name"><span><spring:message code="info.own"/> </span></h6>
        </div></a>
    <nav class="offcanvas-menu">
        <ul class="menu">
            <li><a href="${baseServerPath}/myCenter"><span><spring:message code="my.account"/> </span></a></li>
            <li><a href="${baseServerPath}/myRent"><span><spring:message code="my.rent"/></span></a></li>
            <li><a href="${baseServerPath}/myDevices"><span><spring:message code="my.device"/> </span></a></li>
            <li><a href="${baseServerPath}/myOrders"><spring:message code="my.order"/> </a></li>
            <li><a href="${baseServerPath}/myWishes"><spring:message code="my.wish"/> </a></li>
            <li><a href="${baseServerPath}/myRedeemCode"><spring:message code="my.redeemCode"/> </a></li>
            <li><a href="${baseServerPath}/myGiftHistory"><spring:message code="my.gift.history"/> </a></li>
            <li><a href="${baseServerPath}/myCart"><span><spring:message code="cart.label"/></span></a></li>
            <li><a href="${baseServerPath}/logout"><span><spring:message code="info.logout"/></span></a></li>
        </ul>
    </nav>

</sec:authorize>
<sec:authorize access="not (isAuthenticated() or isRememberMe())">

    <nav class="offcanvas-menu">
        <ul>
            <li> <a href='<at:web type="login" value="/login"/>'><span class="text-lg"><spring:message code="reg.label"/> &nbsp;/&nbsp;<spring:message code="login.label"/> </span></a></li>
        </ul>
    </nav>

</sec:authorize>
    <a class="account-link" href="#">
        <div class="user-info">
            <h6 class="user-name"><spring:message code="web.nav"/> </h6>
        </div>
    </a>
    <nav class="offcanvas-menu">
        <ul class="menu">
<%--            <li><span><a href="/"><span><spring:message code="shop.index"/> </span></a></span></li>--%>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-0-0-0-0-0-0-0-0"><spring:message code="shop.nav.book.all"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-0-1-0-0-0-0-0-0"><spring:message code="shop.nav.inspirata"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-1-0-0-0-0-0-0-0"><spring:message code="shop.nav.book.sc"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-2-0-0-0-0-0-0-0"><spring:message code="shop.nav.book.tc"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-3-0-0-0-0-0-0-0"><spring:message code="shop.nav.book.eng"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/category-0-grid-0-0-1-0-0-0-0-0"><spring:message code="shop.nav.book.free"/> </a></span></li>
<%--            <li class="has-children"><span><a href="category-0-grid-0-0-0-1-0-0-0-0"><spring:message code="shop.nav.book.presale"/> </a></span></li>--%>
            <li class="has-children"><span><a href="${baseServerPath}/readingIndex"><spring:message code="shop.nav.reading"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/rent-to-own"><spring:message code="shop.nav.rent"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/blogs"><spring:message code="shop.nav.blog"/> </a></span></li>
            <li class="has-children"><span><a href="${baseServerPath}/index-Reader"><spring:message code="shop.nav.reader"/> </a></span></li>
        </ul>
    </nav>
    <a class="account-link" href="#">
        <div class="user-info">
            <h6 class="user-name"><spring:message code="nav.money"/> </h6>
        </div>
    </a>
    <!-- 货币切换 -->
    <div class="column text-center padding-top-1x">
        <div class="switcher-wrap">
            <div class="switcher">
               <%-- <a class="text-lg" href="/shop/currency/TW/${fn:replace(requestScope['javax.servlet.forward.request_uri'], "/shop/", "")}">$ USD</a>
                &nbsp;&#124;&nbsp;
                <a class="text-lg" href="/shop/currency/CN/${fn:replace(requestScope['javax.servlet.forward.request_uri'], "/shop/", "")}">&nbsp;¥ CNY</a>--%>
                <at:currency headerMin="true"/>
            </div>
        </div>
    </div>
    <a class="account-link" href="#">
        <div class="user-info">
            <h6 class="user-name"><spring:message code="nav.language"/> </h6>
        </div>
    </a>
    <!-- 语言切换 -->
    <div class="column text-center padding-top-1x padding-bottom-10x">
        <div class="switcher-wrap">
            <div class="switcher">
                <a class="text-lg" href="${baseServerPath}/index?locale=zh_CN">简体</a>
                &nbsp;&#124;&nbsp;
                <a class="text-lg" href="${baseServerPath}/index?locale=zh_HK">&nbsp;繁體</a>
                &#124;&nbsp;
                <a class="text-lg" href="${baseServerPath}/index?locale=en_US">&nbsp;English</a>
            </div>
        </div>
    </div>


</div>
<!-- Topbar-->
<div class="topbar"></div>
<!-- Navbar-->
<!-- Remove "navbar-sticky" 导航栏与页面一起滚动-->
<header class="navbar navbar-sticky">
    <!-- Search-->
    <form class="site-search" method="get" action="${baseServerPath}/searchBook?${_csrf.parameterName}=${_csrf.token}" enctype="multipart/form-data">
        <input type="text" name="searchText" placeholder="<spring:message code='shop.search.placehold'/>" id="shopSearch">
        <input type="hidden" name="order" value="3">
        <div class="search-tools"><span class="clear-search">Clear</span><span class="close-search"><i class="icon-cross"></i></span></div>
    </form>
    <div class="site-branding">
        <div class="inner">
            <!-- Off-Canvas Toggle (#shop-categories)--><a class="offcanvas-toggle cats-toggle" href="#shop-categories" data-toggle="offcanvas"></a>
            <!-- Off-Canvas Toggle (#mobile-menu)--><a class="offcanvas-toggle menu-toggle" href="#mobile-menu" data-toggle="offcanvas"></a>
            <!-- Site Logo--><a class="site-logo" href="${baseServerPath}/"><img src="<c:url value='/statics/images/logo/logo.png' />" alt="log"></a>
        </div>
    </div>
    <!-- 导航栏 -->
    <nav class="site-menu">
        <ul>
<%--            <li><a href="${baseServerPath}/"><span><spring:message code="shop.index"/> </span></a></li>--%>
            <li>
                <a href="${baseServerPath}/category-0-grid-0-0-0-0-0-0-0-0"><span><spring:message code="shop.nav.book.all"/> </span></a>
                <ul class="sub-menu clearfix" style="width: auto;">
                    <div style="display: flex; white-space: nowrap;">
                        <div style="float: left;">
                            <li style="font-size: 15px;padding: 15px 36px;text-align: start;font-weight: 700;" class="bookGrouped"><spring:message code="shop.nav.category.by"/></li>
                            <c:choose>
                                <c:when test="${pageContext.response.locale == 'zh_CN'}">
                                    <c:forEach var="category" items="${categoryList}">
                                        <li class="subBookItem"><a class="subBookPage1" href="${baseServerPath}/category-${category.value}-grid-0-0-0-0-0-0-0-0">${category.name}</a></li>
                                    </c:forEach>
                                </c:when>
                                <c:when test="${pageContext.response.locale == 'en_US'}">
                                    <c:forEach var="category" items="${categoryList}">
                                        <li class="subBookItem"><a class="subBookPage1" href="${baseServerPath}/category-${category.value}-grid-0-0-0-0-0-0-0-0">${category.third}</a></li>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <c:forEach var="category" items="${categoryList}">
                                        <li class="subBookItem"><a class="subBookPage1" href="${baseServerPath}/category-${category.value}-grid-0-0-0-0-0-0-0-0">${category.other}</a></li>
                                    </c:forEach>
                                </c:otherwise>
                            </c:choose>
                        </div>
                        <div style="float: right;">
                            <li style="font-size: 15px;padding: 15px 36px;text-align: start;font-weight: 700;" class="bookLanguage"><spring:message code="shop.nav.language.by"/></li>
                            <li class="subBookItem"><a href="${baseServerPath}/category-0-grid-1-0-0-0-0-0-0-0"><span><spring:message code="shop.nav.book.sc"/> </span></a></li>
                            <li class="subBookItem"><a href="${baseServerPath}/category-0-grid-2-0-0-0-0-0-0-0"><span><spring:message code="shop.nav.book.tc"/> </span></a></li>
                            <li class="subBookItem"><a href="${baseServerPath}/category-0-grid-3-0-0-0-0-0-0-0"><span><spring:message code="shop.nav.book.eng"/> </span></a></li>
                        </div>
                    </div>
                </ul>
            </li>

            <li><a href="${baseServerPath}/category-0-grid-0-1-0-0-0-0-0-0"><span><spring:message code="shop.nav.inspirata"/> </span></a></li>

            <li><a href="${baseServerPath}/category-0-grid-0-0-1-0-0-0-0-0"><span><spring:message code="shop.nav.book.free"/> </span></a></li>
            <li><a href="${baseServerPath}/readingIndex"><span><spring:message code="shop.nav.reading"/> </span></a></li>
            <li><a href="${baseServerPath}/rent-to-own"><span><spring:message code="shop.nav.rent"/> </span></a><img src="https://d2.edhub.cc/fire.png" alt="new" style="position: absolute; top: 20px; right: 6px; width: 16px; height: auto;"></li>
            <li><a href="${baseServerPath}/blogs"><span><spring:message code="shop.nav.blog"/> </span></a></li>
<%--            <li><a href="<c:url value='/category-0-grid-0-0-0-1-0-0-0-0' />"><span><spring:message code="shop.nav.book.presale"/> </span></a></li>--%>
            <li><a href="${baseServerPath}/index-Reader"><span><spring:message code="shop.nav.reader"/> </span></a></li>

            <li>
                <div class="lang-currency-switcher-wrap">
                    <%--<c:choose>
                        <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                            <div class="lang-currency-switcher dropdown-toggle" id="currencyCN">
                                <span class="currency" id="currency-active">$ USD</span>
                            </div>
                            <div class="dropdown-menu" >
                                <a class="dropdown-item" href="/shop/currency/CN/${fn:replace(requestScope['javax.servlet.forward.request_uri'], "/shop/", "")}" id="currency-active-not">¥ CNY</a>
                            </div>
                        </c:when>
                        <c:otherwise>
                            <div class="lang-currency-switcher dropdown-toggle" id="currencyCN">
                                <span class="currency" id="currency-active">¥ CNY</span>
                            </div>
                            <div class="dropdown-menu" >
                                <a class="dropdown-item" href="/shop/currency/TW/${fn:replace(requestScope['javax.servlet.forward.request_uri'], "/shop/", "")}" id="currency-active-not">$ USD</a>
                            </div>
                        </c:otherwise>
                    </c:choose>--%>
                    <at:currency headerNormal="true"/>
                </div>
            </li>
            <li>
                <div class="lang-currency-switcher-wrap">
                    <c:choose>
                        <c:when test="${pageContext.response.locale == 'zh_CN'}">
                            <div class="lang-currency-switcher dropdown-toggle" id="localeCN">
                                <span class="currency" id="locale-active">简体 </span>
                            </div>
                            <div class="dropdown-menu" >
                                <a class="dropdown-item" href="#" id="locale-active-not-cn">简体</a>
                                <a class="dropdown-item" href="${baseServerPath}/index?locale=zh_HK" id="locale-active-not-hk">繁體 </a>
                                <a class="dropdown-item" href="${baseServerPath}/index?locale=en_US" id="locale-active-not-us">English </a>
                            </div>
                        </c:when>
                        <c:when test="${pageContext.response.locale == 'en_US'}">
                            <div class="lang-currency-switcher dropdown-toggle" id="localeCN">
                                <span class="currency" id="locale-active">English </span>
                            </div>
                            <div class="dropdown-menu" >
                                <a class="dropdown-item" href="${baseServerPath}/index?locale=zh_HK" id="locale-active-not-hk">繁體 </a>
                                <a class="dropdown-item" href="${baseServerPath}/index?locale=zh_CN" id="locale-active-not-cn">简体</a>
                                <a class="dropdown-item" href="#" id="locale-active-not-us">English</a>
                            </div>
                        </c:when>
                        <c:otherwise>
                            <div class="lang-currency-switcher dropdown-toggle" id="localeCN">
                                <span class="currency" id="locale-active">繁体</span>
                            </div>
                            <div class="dropdown-menu" >
                                <a class="dropdown-item" href="${baseServerPath}/index?locale=zh_CN" id="locale-active-not-cn">简体</a>
                                <a class="dropdown-item" href="#" id="locale-active-not-hk">繁体</a>
                                <a class="dropdown-item" href="${baseServerPath}/index?locale=en_US" id="locale-active-not-us">English </a>
                            </div>
                        </c:otherwise>
                    </c:choose>

                </div>
            </li>
        </ul>
    </nav>

    <!-- Toolbar-->
    <div class="toolbar">
        <div class="inner">
            <div class="tools">
                <div class="search"><i class="icon-search"></i></div>
                <%--<sec:authorize access="hasRole('ROLE_USER') and isAuthenticated()">
                    ${user}
                </sec:authorize>--%>
                <sec:authorize access="isAuthenticated() or isRememberMe()">
                    <sec:authentication var="user" property="principal" />
                    <div class="account"><a href="${baseServerPath}/myCenter"></a><i class="icon-head"></i>
                        <ul class="toolbar-dropdown">
                            <li class="sub-menu-user">
                                <div class="user-info">
                                    <h6 class="user-name">${user.nickName}</h6>
                                </div>
                            </li>
                            <li><a href="${baseServerPath}/myCenter"><spring:message code="info.myaccount"/> </a></li>
                            <li><a href="${baseServerPath}/myRent"><spring:message code="my.rent"/> </a></li>
                            <li><a href="${baseServerPath}/myDevices"><spring:message code="my.device"/> </a></li>
                            <li><a href="${baseServerPath}/myOrders"><spring:message code="info.myorder"/> </a></li>
                            <li><a href="${baseServerPath}/myWishes"><spring:message code="info.mywishes"/> </a></li>
                            <li><a href="${baseServerPath}/myGiftHistory"><spring:message code="my.gift.history"/> </a></li>
                            <li><a href="${baseServerPath}/myRedeemCode"><spring:message code="my.redeemCode"/> </a></li>
                            <li class="sub-menu-separator"></li>
                            <li><a href="${baseServerPath}/logout"> <i class="icon-unlock"></i><spring:message code="info.logout"/> </a></li>
                        </ul>
                    </div>
                </sec:authorize>
                <sec:authorize access="not (isAuthenticated() or isRememberMe())">

                    <div class="account"><a href='<at:web type="login" value="/login"/>'></a><i class="icon-head"></i>
                        <ul class="toolbar-dropdown w-150">
                            <li><a href='<at:web type="reg" value="/reg"/>'><spring:message code="info.reg"/> </a></li>
                            <li><a href='<at:web type="login" value="/login"/>'><spring:message code="info.login"/> </a></li>
                        </ul>
                    </div>

                </sec:authorize>
                <sec:authorize access="isAuthenticated() or isRememberMe()">
                <div class="cart"><a href="${baseServerPath}/myCart"></a><i class="icon-bag"></i><span class="count" id="buyerCartQuantity">
                       <%-- ${BUYER_CART.quantity}--%>
                    ${sessionScope.Cart_Data.quantity}
                </span>
                    <%--<span class="subtotal">
                        <c:choose>
                            <c:when test="${currency == Locale.TRADITIONAL_CHINESE.country}">
                                $  ${BUYER_CART.amountUsd}&nbsp;
                            </c:when>
                            <c:otherwise>
                                ¥  ${BUYER_CART.amountCny}&nbsp;
                            </c:otherwise>
                        </c:choose>
                    </span>--%>
                    <%--<div class="toolbar-dropdown w-150">
                        <div class="column"><a class="btn btn-sm btn-block btn-secondary" href="myCart">查看购物车</a></div>
                        <div class="column"><a class="btn btn-sm btn-block btn-primary" href="toCheckout">立即结账</a></div>
                    </div>--%>
                </div>
                </sec:authorize>

            </div>
        </div>
    </div>
</header>


<script>
    function changeCurrency(sel)
    {
        var url = "/currency/"+sel.value+"/${fn:replace(requestScope['javax.servlet.forward.request_uri'], "//", "/")}";
        //alert(url);
        location.href = url;//location.href实现客户端页面的跳转
    }


</script>
