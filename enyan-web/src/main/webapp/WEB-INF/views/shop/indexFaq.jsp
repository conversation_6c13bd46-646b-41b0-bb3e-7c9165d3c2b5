<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="footer.faq"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
    <style>
        .accordion {
            margin-top:1.875rem
        }

        .accordion-title {
            font-size: 18px;
            line-height: 1.35;
            margin-top: 36px;
            margin-bottom: 36px;
            font-weight: 500 !important;
            color:#000000
        }

        .accordion .accordion-item {
            background-color: white;
            color: #000000;
            border-radius:0.5rem
        }

        .accordion .accordion-item.active .accordion-body {
            max-height:max-content
        }

        .accordion .accordion-item.active .accordion-headerc {
            transform:rotate(0deg)
        }

        .accordion .accordion-item:last-child {
            margin-bottom:0
        }

        .accordion .accordion-item .accordion-header {
            padding: 0.68rem 0px;
            font-size: 15px;
            line-height: 1.6rem;
            display: flex;
            align-items: center;
            position: relative;
            cursor:pointer
        }

        .accordion .accordion-item .accordion-header::after {
            content: "";
            flex-shrink: 0;
            width: 1.0rem;
            aspect-ratio: 1;
            margin-left: auto;
            background: url(./img/iconDown.svg) no-repeat;
            width: 12px;
            height: 12px;
            transition: transform 0.2s ease-in-out;
            transform:rotate(-90deg)
        }

        .accordion .accordion-item .accordion-header span {
            padding-right: 16px;
        }

        .accordion .accordion-item .accordion-body {
            max-height: 0;
            overflow: hidden;
            transition:max-height 0.3s ease-in-out
        }

        .accordion .accordion-item .accordion-body .accordion-body-content {
            margin: 0 0.5rem;
            font-size: 15px;
            line-height:1.6rem
        }

        .accordion .accordion-item.active .accordion-body .accordion-body-content {
            background-color: rgba(96, 105, 117, 0.05);
            padding:1.4rem 1.2rem
        }

        .accordion .accordion-item p {
            margin: 1.0rem 1.4rem;
            font-size: 15px;
            line-height:1.6rem
        }

        .accordion .accordion-item .accordion-header:active {
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            -webkit-tap-highlight-color: transparent;
            -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
            -webkit-user-select: none;
            -moz-user-focus: none;
            -moz-user-select:none
        }

        * {
            -webkit-touch-callout: none;
            -webkit-text-size-adjust: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            -webkit-user-select: none
        }
    </style>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <c:choose>
        <c:when test="${pageContext.response.locale == 'zh_CN'}">
            <jsp:include page="indexFaq-sc.jsp"/>
        </c:when>
        <c:when test="${pageContext.response.locale == 'en_US'}">
            <jsp:include page="indexFaq-en.jsp"/>
        </c:when>
        <c:otherwise>
            <jsp:include page="indexFaq-tc.jsp"/>
        </c:otherwise>
    </c:choose>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
    <jsp:include page="ad-info.jsp"/>
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
<script>
    const accSingleTriggers = document.querySelectorAll(".accordion-header");
    accSingleTriggers.forEach((trigger) =>
        trigger.addEventListener("click", toggleAccordion)
    );
    function toggleAccordion() {
        const items = document.querySelectorAll(".accordion-item");
        const thisItem = this.parentNode;
        items.forEach((item) => {
            if (thisItem == item) {
                thisItem.classList.toggle("active");
                return;
            }
            item.classList.remove("active");
        });
    }
</script>
</body>
</html>