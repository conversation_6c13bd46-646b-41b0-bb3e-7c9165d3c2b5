<%@ page import="com.aaron.spring.common.Constant" %><%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="order.confirm"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="恩道电子书是恩道出版（香港）有限公司旗下的基督教电子书阅读平台，旨在通过与主内的出版机构合作，协力促进华文基督教资源电子化，帮助中国乃至全球华人基督徒，更加便捷地获取并阅读基督教图书。">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音书籍,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="${_csrf.token}">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Title-->

    <!-- Page Content-->
    <div class="container padding-bottom-3x mb-1 padding-top-2x">
        <%--<div class="entry">
            <div class="entry-content">
                <h5>订单号：<span class="text-lg text-muted">${orderMain.orderNum}</span></h5>
                <h5>订单日期：<span class="text-lg text-muted">2017-10-21</span></h5>
                <h5>付款方式：<span class="text-lg text-muted">
                    <c:choose>
                        <c:when test="${orderMain.orderCurrency == 1}">
                            美元
                        </c:when>
                        <c:otherwise>
                            支付宝
                        </c:otherwise>
                    </c:choose>

                </span></h5><br />
            </div>
        </div>--%>
        <!-- 购物车-->
        <div class="table-responsive shopping-cart">
            <table class="table">
                <thead>
                <tr>
                    <th><spring:message code="book.title"/> </th>
                    <th class="text-center"><spring:message code="book.price"/></th>
                    <th class="text-center"><spring:message code="book.amount"/> </th>
                    <th class="text-center"><spring:message code="book.total"/> </th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="discountList" items="${order.cartDiscountInfoList}" varStatus="status">

                    <c:choose>
                        <c:when test="${discountList.discountId > 0}">
                            <c:if test="${not empty discountList.discountTitle}">
                                <tr>
                                    <td colspan="4">
                                        <span class="text-danger">
                                            <spring:message code="discount.n.has"/>${discountList.discountTitle}
                                        </span>
                                    </td>
                                </tr>
                            </c:if>
                        </c:when>
                        <c:otherwise>
                            <c:if test="${status.index == 1}">
                                <tr>
                                    <td colspan="4">
                                        <span class="text-danger">
                                            <spring:message code="discount.n.not"/>
                                        </span>
                                    </td>
                                </tr>
                            </c:if>
                        </c:otherwise>
                    </c:choose>

                    <c:forEach var="productInfo" items="${discountList.productInfoList}" varStatus="st">
                        <tr>
                            <td>
                                <div class="product-item">
                                    <a class="product-thumb" href="/book-${productInfo.code}">
                                        <img src="${productInfo.productCover}" alt="Product"></a>
                                    <div class="product-info">
                                        <h4 class="product-title">
                                            <a href="/book-${productInfo.code}">
                                                <c:if test="${productInfo.salesModel == 1}">
                                                    <spring:message code="book.title.presale"/>
                                                </c:if>
                                                    ${productInfo.name}
                                            </a>
                                        </h4><span><em><spring:message code="book.author"/> :</em>
                                    <c:if test="${not empty productInfo.producer}">
                                        <c:set var="authors" value="${fn:split(productInfo.producer, '#')}" />
                                        <c:forEach var="author" items="${authors}">
                                            <a class="navi-link" href="searchBook?searchText=${author}">${author}</a>
                                        </c:forEach>
                                    </c:if>
                                </span>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center text-lg text-medium">
                                <c:choose>
                                    <c:when test="${productInfo.discountAnyIsValid}">
                                        <del>HK$${productInfo.priceHkd}</del>
                                        HK$${productInfo.priceHKDDiscount}
                                    </c:when>
                                    <c:otherwise>
                                        HK$${productInfo.priceHkd}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="text-center text-lg text-medium">${productInfo.quantity}</td>
                            <td class="text-center text-lg text-medium">
                                <c:choose>
                                    <c:when test="${productInfo.discountAnyIsValid}">
                                        HK$${productInfo.priceHKDDiscount*productInfo.quantity}
                                    </c:when>
                                    <c:otherwise>
                                        HK$${productInfo.priceHkd*productInfo.quantity}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                        </tr>
                    </c:forEach>
                </c:forEach>
                <c:forEach var="productInfo" items="${order.productInfoList}" varStatus="status">
                    <tr>
                        <td>
                            <div class="product-item">
                                <a class="product-thumb" href="/book-${productInfo.code}">
                                    <img src="${productInfo.productCover}" alt="Product"></a>
                                <div class="product-info">
                                    <h4 class="product-title">
                                        <a href="/book-${productInfo.code}">
                                            <c:if test="${productInfo.salesModel == 1}">
                                                <spring:message code="book.title.presale"/>
                                            </c:if>
                                                ${productInfo.name}
                                        </a>
                                    </h4><span><em><spring:message code="book.author"/> :</em>
                                    <c:if test="${not empty productInfo.producer}">
                                        <c:set var="authors" value="${fn:split(productInfo.producer, '#')}" />
                                        <c:forEach var="author" items="${authors}">
                                            <a class="navi-link" href="searchBook?searchText=${author}">${author}</a>
                                        </c:forEach>
                                    </c:if>
                                </span>
                                </div>
                            </div>
                        </td>
                        <td class="text-center text-lg text-medium">
                            <c:choose>
                                <c:when test="${productInfo.discountAnyIsValid}">
                                    <del>HK$${productInfo.priceHkd}</del>
                                    HK$${productInfo.priceHKDDiscount}
                                </c:when>
                                <c:otherwise>
                                    HK$${productInfo.priceHkd}
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td class="text-center text-lg text-medium">${productInfo.quantity}</td>
                        <td class="text-center text-lg text-medium">
                            <c:choose>
                                <c:when test="${productInfo.discountAnyIsValid}">
                                    HK$${productInfo.priceHKDDiscount*productInfo.quantity}
                                </c:when>
                                <c:otherwise>
                                    HK$${productInfo.priceHkd*productInfo.quantity}
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>

                </tbody>
            </table>
        </div>
        <c:if test="${orderMain.isPaid == 0}"><%-- <c:if test="${not empty order.productInfoList}"> --%>
            <div class="shopping-cart-footer">
                <div class="col-xl-11">
                    <span id="warning" class="text-medium text-danger"><spring:message code="tip.currency"/></span>
                </div>

                <div class="column text-lg-right">
                    <h4 class="product-price">
                        <spring:message code="account.total"/> ：
                        <span class="text-medium">HK$${order.amountHkdMiddle}</span>
                    </h4>
                    <c:if test="${order.amountDiscount > 0}">
                        <h4 class="product-price">
                            <%--N件折（${order.discountTitle}） --%>
                            <spring:message code="account.discount"/>：
                            <span class="text-medium">-HK$${order.amountDiscount}</span>
                        </h4>
                    </c:if>
                    <h4 class="product-price">
                        <spring:message code="account.toPay"/> ：<span class="text-medium">HK$${order.amountHkd}</span>
                    </h4>
                    <%--<h4 class="product-price">
                         <span class="text-medium">(HK$${order.amountHkd})</span>
                    </h4>--%>
                    <at:bookPrice orderConfirm="true" priceToDo="${order.amountHkd}"/>
                </div>
            </div>

            <!-- 优惠码 start-->
            <div class="col-xl-8 col-lg-8 padding-top-1x">
                <h4><spring:message code="coupon.use"/></h4>
                <hr class="padding-bottom-1x">

                <div class="accordion pb-3" id="accordion" role="tablist">
                    <div class="card">
                        <div class="card-header" role="tab">
                            <h6><a class="collapsed" href="#alipay" data-toggle="collapse"
                                   data-parent="#accordion"><spring:message code="coupon.label"/></a></h6>
                        </div>
                        <div class="collapse" id="alipay" role="tabpanel">
                            <div class="card-body">
                                <div class="coupon-form" method="post" action="#">
                                    <!-- <span class="test-lg text-medium">优惠码: </span>  -->
                                    <input class="form-control form-control-sm" type="text" id="couponCode" placeholder="<spring:message code='coupon.placeholder'/>" required>
                                    <a class="btn btn-outline-primary btn-sm" id="btn-send-coupon"><spring:message code="button.apply" /></a>
                                </div>
                                <span id="couponError" class="text-medium text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 优惠码 end-->

        </c:if>

            <div class="shopping-cart-footer">
                <div class="column">
                    <a class="btn btn-outline-secondary" href="javascript:history.go(-1)" data-toast-position="topRight"><i class="icon-arrow-left"></i>&nbsp;<spring:message code="back.label" /></a>
                    <a class="btn btn-primary" href="toCheckout-${clearCart}" ><spring:message code="order.submit"/> </a>
                </div>
            </div>

    </div>

    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>

<script>
    function isEmpty(str){
        if($.trim(str)==''){
            return true;
        }else{
            return false;
        }
    };
    function validate() {
        var hasRead = document.getElementById('hasRead');
        if (!hasRead.checked){
            alert("请确认阅读恩言书库的用户协议") ;
            return false;
        }
        return true;
    };
    $(document).ready(function(){
        $("#usdPay").click(function(){

            payNumber = $("#payNumber").val();
            payExpire = $("#payExpire").val();
            payCvc = $("#payCvc").val();

            var hasRead = document.getElementById('hasRead');
            if (!hasRead.checked){
                alert("请确认阅读恩言书库的用户协议") ;
                return;
            }

            if(isEmpty(payNumber)){
                alert("信用卡不可以为空！"+payNumber);
                return;
            }

            if (payExpire.length == 0){
                alert("时间不可以为空！");
                return;
            }
            if (payCvc.length == 0){
                alert("CVC不可以为空！");
                return;
            }
            url="/checkoutUsd-${orderMain.orderId}";
            $.jpost(url, {
                "payNumber":payNumber,
                "payExpire":payExpire,
                "payCvc":payCvc
            }).then(res => {
                //console.log(res);
                alert(res.result);
                window.location.reload();
                //windows.href("");
            });


        });
    });
</script>
</body>
</html>