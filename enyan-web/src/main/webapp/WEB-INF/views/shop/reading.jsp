<%--
  User: Aaron
  Date: 2017/12/13
  Time: 下午2:15
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="at" uri="AaronTagLib" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title><spring:message code="shop.nav.reading"/>-<spring:message code="shop.title"/></title>
    <!-- SEO Meta Tags-->
    <meta name="description" content="<spring:message code='footer.aboutus'/>">
    <meta name="keywords" content="基督教,主内,电子书,基督教电子书,基督教图书,主内电子书,主内图书,福音真理,圣经辅读,圣经注释,恩道书房,恩道出版,恩道出版社">
    <!-- Mobile Specific Meta Tag-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <jsp:include page="header-css.jsp"/>
    <jsp:include page="track-info.jsp"/>
    <style>
        .ui-autocomplete-loading {
            background: white url(<c:url value='/statics/images/ui-anim_basic_16x16.gif'/>) right center no-repeat;
        }
        .ui-menu .ui-menu-item-wrapper {
            position: relative;
            padding: 13px 1em 3px 1.5em;
        }
    </style>
</head>
<!-- Body-->
<body>
<!--header start-->
<jsp:include page="header.jsp"/>
<!--header end-->
<!-- Off-Canvas Wrapper-->
<div class="offcanvas-wrapper">
    <!-- Page Content-->
    <div class="container padding-bottom-1x mb-1 padding-top-2x">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-xl-2 col-lg-2">
                <aside class="sidebar">
                    <div class="padding-top-2x hidden-lg-up"></div>
                    <section class="widget widget-categories">
                        <ul>
                            <li id="readingListIndex"><a href="readingIndex"><spring:message code="reading.index"/></a></li>
                            <li class="has-children expanded" id="readingList0"><a href="reading-0#"><spring:message code="reading.nav.0"/></a>
                                <ul>
                                    <li id="readingList2"><a href="reading-2#"><spring:message code="reading.nav.2"/></a></li>
                                    <li id="readingList3"><a href="reading-3#"><spring:message code="reading.nav.3"/></a></li>
                                    <li id="readingList4"><a href="reading-4#"><spring:message code="reading.nav.4"/></a></li>
                                    <li id="readingList5"><a href="reading-5#"><spring:message code="reading.nav.5"/></a></li>
                                </ul>
                            </li>
                            <li id="readingList6"><a href="reading-6#"><spring:message code="reading.nav.6"/></a></li>
<%--                            <li id="readingListJoin"><a href="readingJoin"><spring:message code="reading.nav.join"/></a></li>--%>
                        </ul>
                    </section>
                </aside>
            </div>
            <!-- 页面内容 -->
            <div class="col-xl-9 col-lg-9">
                <div class="container-desktop mb-2">
                    <div class="align-items-center justify-content-center">
                        <div class="shop-toolbar mb-2">
                            <div class="column">
                                <div class="shop-sorting">
                                    <label for="sorting" class="navbar-back"><spring:message code="order.label"/>:</label>
                                    <select class="form-control" id="sorting" onchange="changeSort(this);">
                                        <option value="5" <c:if test="${param.order==5}">selected</c:if>><spring:message code="order.default"/> </option>
                                        <option value="0" <c:if test="${param.order==0}">selected</c:if>><spring:message code="order.read.desc"/> </option>
                                        <option value="2" <c:if test="${param.order==2}">selected</c:if>><spring:message code="order.create"/> </option>
                                    </select><span class="text-muted navbar-back"><spring:message code="print"/>:&nbsp;</span><span class="navbar-back">${pageDescription} </span>
                                </div>
                            </div>
                            <div class="column navbar-back"></div>
                        </div>
                    </div>
                    <c:forEach var="list" items="${blogList}">
                        <div class="row justify-content-center padding-top-2x padding-bottom-2x">
                            <div class="col-md-3"><img class="d-block w-200 m-auto rounded" src="${list.blogCover}" alt="blog"></div>
                            <div class="col-md-9 text-md-left">
                                <div class="mt-30 hidden-md-up"></div>
                                <h3 class="post-title"><a href="blog-${list.blogId}#">${list.blogTitle}</a></h3>
                                <p class="text-justify" style="text-align:justify;">${list.blogAbstract}</p>
                                <span class="text-medium text-decoration-none">${list.author}</span>
                                <div class="single-post-meta padding-top-1x">
                                    <div class="column" style="text-align: left;">
                                        <div class="meta-link" style="margin-left: 0px;"><span><fmt:formatDate value="${list.createAt}" pattern="yyyy-MM-dd"/> &nbsp;</span></div>
                                        <div class="meta-link"><span><i class="icon-eye"></i>${list.readCount} &nbsp;</span></div>
                                        <div class="meta-link"><span><i class="icon-heart"></i>${list.likeCount}</span></div>
                                    </div>
                                </div>
                            </div>
                        </div><hr>
                    </c:forEach>
                </div>

                <div class="container-desktop padding-bottom-2x mb-2">
                    <div class="row align-items-center justify-content-center">
                        <div class="col-12">
                            <nav class="pagination" style="border-top:0;">
                                ${pageLand}
                            </nav>
                        </div>
                    </div>
                </div>

            </div>
    </div>
    </div>
    <!-- Site Footer start-->
    <jsp:include page="footer.jsp"/>
    <!-- Site Footer end-->
</div>
<jsp:include page="service-info.jsp"/>
<!-- Back To Top Button--><a class="scroll-to-top-btn" href="#"><i class="icon-arrow-up"></i></a>
<!-- Backdrop-->
<div class="site-backdrop"></div>
<jsp:include page="footer-js.jsp"/>
<script>
    $(document).ready(function(){
        //do something
        $("#readingList${categoryId}").addClass("active");
    })
    function changeSort(sel)
    {
        var url = "?order="+sel.value;
        //alert(url);
        location.href = url;//location.href实现客户端页面的跳转
    }
</script>
</body>
</html>