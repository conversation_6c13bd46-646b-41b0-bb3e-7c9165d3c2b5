<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanBlogCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBlog">
    <id column="blog_id" jdbcType="BIGINT" property="blogId" />
    <result column="blog_title" jdbcType="VARCHAR" property="blogTitle" />
    <result column="author" jdbcType="VARCHAR" property="author" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="blog_cover" jdbcType="VARCHAR" property="blogCover" />
    <result column="blog_cover_app" jdbcType="VARCHAR" property="blogCoverApp" />
    <result column="blog_abstract" jdbcType="VARCHAR" property="blogAbstract" />
    <result column="recommended_order" jdbcType="INTEGER" property="recommendedOrder" />
    <result column="recommended_caption" jdbcType="VARCHAR" property="recommendedCaption" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="read_count" jdbcType="INTEGER" property="readCount" />
    <result column="like_count" jdbcType="INTEGER" property="likeCount" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanBlog">
    <result column="blog_content" jdbcType="LONGVARCHAR" property="blogContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    blog_id, blog_title, author, publisher_id, blog_cover, blog_cover_app, blog_abstract, 
    recommended_order, recommended_caption, category_id, read_count, like_count, is_deleted, 
    create_at
  </sql>
  <sql id="Blob_Column_List">
    blog_content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.EnyanBlogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_blog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanBlogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_blog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_blog
    where blog_id = #{blogId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_blog
    where blog_id = #{blogId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanBlogExample">
    delete from enyan_blog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanBlog">
    <selectKey keyProperty="blogId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_blog (blog_title, author, publisher_id, 
      blog_cover, blog_cover_app, blog_abstract, 
      recommended_order, recommended_caption, category_id, 
      read_count, like_count, is_deleted, 
      create_at, blog_content)
    values (#{blogTitle,jdbcType=VARCHAR}, #{author,jdbcType=VARCHAR}, #{publisherId,jdbcType=BIGINT}, 
      #{blogCover,jdbcType=VARCHAR}, #{blogCoverApp,jdbcType=VARCHAR}, #{blogAbstract,jdbcType=VARCHAR}, 
      #{recommendedOrder,jdbcType=INTEGER}, #{recommendedCaption,jdbcType=VARCHAR}, #{categoryId,jdbcType=INTEGER}, 
      #{readCount,jdbcType=INTEGER}, #{likeCount,jdbcType=INTEGER}, #{isDeleted,jdbcType=INTEGER}, 
      #{createAt,jdbcType=TIMESTAMP}, #{blogContent,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanBlog">
    <selectKey keyProperty="blogId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_blog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="blogTitle != null">
        blog_title,
      </if>
      <if test="author != null">
        author,
      </if>
      <if test="publisherId != null">
        publisher_id,
      </if>
      <if test="blogCover != null">
        blog_cover,
      </if>
      <if test="blogCoverApp != null">
        blog_cover_app,
      </if>
      <if test="blogAbstract != null">
        blog_abstract,
      </if>
      <if test="recommendedOrder != null">
        recommended_order,
      </if>
      <if test="recommendedCaption != null">
        recommended_caption,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="readCount != null">
        read_count,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="blogContent != null">
        blog_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="blogTitle != null">
        #{blogTitle,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        #{author,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="blogCover != null">
        #{blogCover,jdbcType=VARCHAR},
      </if>
      <if test="blogCoverApp != null">
        #{blogCoverApp,jdbcType=VARCHAR},
      </if>
      <if test="blogAbstract != null">
        #{blogAbstract,jdbcType=VARCHAR},
      </if>
      <if test="recommendedOrder != null">
        #{recommendedOrder,jdbcType=INTEGER},
      </if>
      <if test="recommendedCaption != null">
        #{recommendedCaption,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="readCount != null">
        #{readCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="blogContent != null">
        #{blogContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanBlogExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_blog
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_blog
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_blog
    <set>
      <if test="record.blogId != null">
        blog_id = #{record.blogId,jdbcType=BIGINT},
      </if>
      <if test="record.blogTitle != null">
        blog_title = #{record.blogTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.author != null">
        author = #{record.author,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.blogCover != null">
        blog_cover = #{record.blogCover,jdbcType=VARCHAR},
      </if>
      <if test="record.blogCoverApp != null">
        blog_cover_app = #{record.blogCoverApp,jdbcType=VARCHAR},
      </if>
      <if test="record.blogAbstract != null">
        blog_abstract = #{record.blogAbstract,jdbcType=VARCHAR},
      </if>
      <if test="record.recommendedOrder != null">
        recommended_order = #{record.recommendedOrder,jdbcType=INTEGER},
      </if>
      <if test="record.recommendedCaption != null">
        recommended_caption = #{record.recommendedCaption,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.readCount != null">
        read_count = #{record.readCount,jdbcType=INTEGER},
      </if>
      <if test="record.likeCount != null">
        like_count = #{record.likeCount,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.blogContent != null">
        blog_content = #{record.blogContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update enyan_blog
    set blog_id = #{record.blogId,jdbcType=BIGINT},
      blog_title = #{record.blogTitle,jdbcType=VARCHAR},
      author = #{record.author,jdbcType=VARCHAR},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      blog_cover = #{record.blogCover,jdbcType=VARCHAR},
      blog_cover_app = #{record.blogCoverApp,jdbcType=VARCHAR},
      blog_abstract = #{record.blogAbstract,jdbcType=VARCHAR},
      recommended_order = #{record.recommendedOrder,jdbcType=INTEGER},
      recommended_caption = #{record.recommendedCaption,jdbcType=VARCHAR},
      category_id = #{record.categoryId,jdbcType=INTEGER},
      read_count = #{record.readCount,jdbcType=INTEGER},
      like_count = #{record.likeCount,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      blog_content = #{record.blogContent,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_blog
    set blog_id = #{record.blogId,jdbcType=BIGINT},
      blog_title = #{record.blogTitle,jdbcType=VARCHAR},
      author = #{record.author,jdbcType=VARCHAR},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      blog_cover = #{record.blogCover,jdbcType=VARCHAR},
      blog_cover_app = #{record.blogCoverApp,jdbcType=VARCHAR},
      blog_abstract = #{record.blogAbstract,jdbcType=VARCHAR},
      recommended_order = #{record.recommendedOrder,jdbcType=INTEGER},
      recommended_caption = #{record.recommendedCaption,jdbcType=VARCHAR},
      category_id = #{record.categoryId,jdbcType=INTEGER},
      read_count = #{record.readCount,jdbcType=INTEGER},
      like_count = #{record.likeCount,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      create_at = #{record.createAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanBlog">
    update enyan_blog
    <set>
      <if test="blogTitle != null">
        blog_title = #{blogTitle,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        author = #{author,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="blogCover != null">
        blog_cover = #{blogCover,jdbcType=VARCHAR},
      </if>
      <if test="blogCoverApp != null">
        blog_cover_app = #{blogCoverApp,jdbcType=VARCHAR},
      </if>
      <if test="blogAbstract != null">
        blog_abstract = #{blogAbstract,jdbcType=VARCHAR},
      </if>
      <if test="recommendedOrder != null">
        recommended_order = #{recommendedOrder,jdbcType=INTEGER},
      </if>
      <if test="recommendedCaption != null">
        recommended_caption = #{recommendedCaption,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="readCount != null">
        read_count = #{readCount,jdbcType=INTEGER},
      </if>
      <if test="likeCount != null">
        like_count = #{likeCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="blogContent != null">
        blog_content = #{blogContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where blog_id = #{blogId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.EnyanBlog">
    update enyan_blog
    set blog_title = #{blogTitle,jdbcType=VARCHAR},
      author = #{author,jdbcType=VARCHAR},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      blog_cover = #{blogCover,jdbcType=VARCHAR},
      blog_cover_app = #{blogCoverApp,jdbcType=VARCHAR},
      blog_abstract = #{blogAbstract,jdbcType=VARCHAR},
      recommended_order = #{recommendedOrder,jdbcType=INTEGER},
      recommended_caption = #{recommendedCaption,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=INTEGER},
      read_count = #{readCount,jdbcType=INTEGER},
      like_count = #{likeCount,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      blog_content = #{blogContent,jdbcType=LONGVARCHAR}
    where blog_id = #{blogId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanBlog">
    update enyan_blog
    set blog_title = #{blogTitle,jdbcType=VARCHAR},
      author = #{author,jdbcType=VARCHAR},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      blog_cover = #{blogCover,jdbcType=VARCHAR},
      blog_cover_app = #{blogCoverApp,jdbcType=VARCHAR},
      blog_abstract = #{blogAbstract,jdbcType=VARCHAR},
      recommended_order = #{recommendedOrder,jdbcType=INTEGER},
      recommended_caption = #{recommendedCaption,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=INTEGER},
      read_count = #{readCount,jdbcType=INTEGER},
      like_count = #{likeCount,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      create_at = #{createAt,jdbcType=TIMESTAMP}
    where blog_id = #{blogId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>