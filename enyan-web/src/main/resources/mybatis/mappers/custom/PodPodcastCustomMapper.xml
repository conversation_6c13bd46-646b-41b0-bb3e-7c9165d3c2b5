<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.PodPodcastCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.PodPodcast">
    <id column="podcast_id" jdbcType="BIGINT" property="podcastId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="author_name" jdbcType="VARCHAR" property="authorName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="cover_image_url" jdbcType="VARCHAR" property="coverImageUrl" />
    <result column="cover_image_url2" jdbcType="VARCHAR" property="coverImageUrl2" />
    <result column="display_order" jdbcType="INTEGER" property="displayOrder" />
    <result column="episode_count" jdbcType="INTEGER" property="episodeCount" />
    <result column="is_published" jdbcType="INTEGER" property="isPublished" />
    <result column="publication_date" jdbcType="TIMESTAMP" property="publicationDate" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  
  <!-- 逻辑删除播客 -->
  <update id="updatePodcastToDeletedById" parameterType="java.lang.Long">
    UPDATE pod_podcast
    SET is_deleted = 1
    WHERE podcast_id = #{podcastId,jdbcType=BIGINT}
  </update>
  
  <!-- 增加播客喜欢量 -->
  <update id="updatePodcastLikeCountById" parameterType="java.lang.Long">
    UPDATE pod_podcast
    SET like_count = like_count + 1
    WHERE podcast_id = #{podcastId,jdbcType=BIGINT}
  </update>
  
  <!-- 增加播客播放量 -->
<!--  <update id="updatePodcastPlayCountById" parameterType="java.lang.Long">-->
<!--    UPDATE pod_podcast-->
<!--    SET play_count = play_count + 1-->
<!--    WHERE podcast_id = #{podcastId,jdbcType=BIGINT}-->
<!--  </update>-->
</mapper>
