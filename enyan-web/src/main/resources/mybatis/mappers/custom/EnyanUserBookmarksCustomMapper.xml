<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanUserBookmarksCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanUserBookmarks">
    <id column="bookmark_id" jdbcType="VARCHAR" property="bookmarkId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="chapter_name" jdbcType="VARCHAR" property="chapterName" />
    <result column="page_chapter" jdbcType="INTEGER" property="pageChapter" />
    <result column="current_page" jdbcType="INTEGER" property="currentPage" />
    <result column="total_page" jdbcType="INTEGER" property="totalPage" />
    <result column="page_process" jdbcType="INTEGER" property="pageProcess" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    bookmark_id, user_id, user_email, book_id, create_time, chapter_name, page_chapter, 
    current_page, total_page, is_deleted, update_time
  </sql>
  <insert id="add" parameterType="com.aaron.spring.model.EnyanUserBookmarks">
    insert into enyan_user_bookmarks (bookmark_id,user_id, user_email, book_id,
                                      create_time, chapter_name, page_chapter,
                                      current_page, total_page, page_process,
                                      is_deleted, update_time)
    values (#{bookmarkId,jdbcType=VARCHAR},#{userId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT},
            #{createTime,jdbcType=BIGINT}, #{chapterName,jdbcType=VARCHAR}, #{pageChapter,jdbcType=INTEGER},
            #{currentPage,jdbcType=INTEGER}, #{totalPage,jdbcType=INTEGER}, #{pageProcess,jdbcType=INTEGER},
            #{isDeleted,jdbcType=INTEGER}, #{updateTime,jdbcType=BIGINT})
  </insert>

  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>