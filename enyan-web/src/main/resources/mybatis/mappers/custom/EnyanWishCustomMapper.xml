<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanWishCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBook">
    <id column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
    <result column="author" jdbcType="VARCHAR" property="author" />
    <result column="author_bio" jdbcType="VARCHAR" property="authorBio" />
    <result column="translator" jdbcType="VARCHAR" property="translator" />
    <result column="word_count" jdbcType="VARCHAR" property="wordCount" />
    <result column="product_web" jdbcType="VARCHAR" property="productWeb" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="sales_volume" jdbcType="BIGINT" property="salesVolume" />
    <result column="price_cny" jdbcType="DECIMAL" property="priceCny" />
    <result column="price_usd" jdbcType="DECIMAL" property="priceUsd" />
    <result column="price_hkd" jdbcType="DECIMAL" property="priceHkd" />
    <result column="book_cost" jdbcType="INTEGER" property="bookCost" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="publisher_name" jdbcType="VARCHAR" property="publisherName" />
    <result column="published_at" jdbcType="VARCHAR" property="publishedAt" />
    <result column="book_abstract" jdbcType="VARCHAR" property="bookAbstract" />
    <result column="book_cover" jdbcType="VARCHAR" property="bookCover" />
    <result column="book_cover_app" jdbcType="VARCHAR" property="bookCoverApp" />
    <result column="book_sample" jdbcType="VARCHAR" property="bookSample" />
    <result column="book_full" jdbcType="VARCHAR" property="bookFull" />
    <result column="book_hash" jdbcType="VARCHAR" property="bookHash" />
    <result column="book_isbn" jdbcType="VARCHAR" property="bookIsbn" />
    <result column="book_esin" jdbcType="VARCHAR" property="bookEsin" />
    <result column="ebook_isbn" jdbcType="VARCHAR" property="ebookIsbn" />
    <result column="has_book_pagination" jdbcType="TINYINT" property="hasBookPagination" />
    <result column="book_pub_code" jdbcType="VARCHAR" property="bookPubCode" />
    <result column="book_keywords" jdbcType="VARCHAR" property="bookKeywords" />
    <result column="book_type" jdbcType="INTEGER" property="bookType" />
    <result column="special_offer" jdbcType="INTEGER" property="specialOffer" />
    <result column="area_discount" jdbcType="INTEGER" property="areaDiscount" />
    <result column="can_tts" jdbcType="INTEGER" property="canTts" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="book_drm_ref" jdbcType="VARCHAR" property="bookDrmRef" />
    <result column="discount_single_id" jdbcType="BIGINT" property="discountSingleId" />
    <result column="discount_single_type" jdbcType="TINYINT" property="discountSingleType" />
    <result column="discount_single_value" jdbcType="INTEGER" property="discountSingleValue" />
    <result column="discount_single_start_time" jdbcType="DATE" property="discountSingleStartTime" />
    <result column="discount_single_end_time" jdbcType="DATE" property="discountSingleEndTime" />
    <result column="discount_single_is_valid" jdbcType="TINYINT" property="discountSingleIsValid" />
    <result column="discount_single_description" jdbcType="VARCHAR" property="discountSingleDescription" />
    <result column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="discount_description" jdbcType="VARCHAR" property="discountDescription" />
    <result column="discount_is_valid" jdbcType="TINYINT" property="discountIsValid" />
    <result column="is_in_cn" jdbcType="TINYINT" property="isInCn" />
    <result column="recommended_order" jdbcType="INTEGER" property="recommendedOrder" />
    <result column="recommended_caption" jdbcType="VARCHAR" property="recommendedCaption" />
    <result column="print_permission" jdbcType="TINYINT" property="printPermission" />
    <result column="copy_permission" jdbcType="TINYINT" property="copyPermission" />
    <result column="ebook_format" jdbcType="TINYINT" property="ebookFormat" />
    <result column="shelf_status" jdbcType="TINYINT" property="shelfStatus" />
    <result column="vendor_percent" jdbcType="INTEGER" property="vendorPercent" />
    <result column="sales_model" jdbcType="INTEGER" property="salesModel" />
    <result column="opensale_at" jdbcType="TIMESTAMP" property="opensaleAt" />
    <result column="cart_id" jdbcType="VARCHAR" property="cartId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanBook">
    <result column="book_description" jdbcType="LONGVARCHAR" property="bookDescription" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="or" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  or ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  or ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  or ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                 ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    book_id, book_title, author, author_bio, translator, word_count, product_web, price,
    sales_volume, price_cny, price_usd, price_hkd, book_cost, category_id, category_name,
    publisher_id, publisher_name, published_at, book_abstract, book_cover, book_cover_app,
    book_sample, book_full, book_hash, book_isbn, book_esin, ebook_isbn, has_book_pagination,
    book_pub_code, book_keywords, book_type, special_offer, area_discount, can_tts,
    size, book_drm_ref, discount_single_id, discount_single_type, discount_single_value,
    discount_single_start_time, discount_single_end_time, discount_single_is_valid, discount_single_description,
    discount_id, discount_description, discount_is_valid, is_in_cn, recommended_order,
    recommended_caption, print_permission, copy_permission, ebook_format, shelf_status,
    vendor_percent, sales_model, opensale_at
  </sql>
  <sql id="Blob_Column_List">
    book_description
  </sql>
  <select id="searchByExample" parameterType="map" resultMap="BaseResultMap">
    select

      book.book_id,book_title, author, author_bio, translator, word_count, product_web, price,
    sales_volume, price_cny, price_usd, price_hkd, book_cost, category_id, category_name,
    publisher_id, publisher_name, published_at, book_abstract, book_cover, book_cover_app,
    book_sample, book_full, book_hash, book_isbn, book_esin, ebook_isbn, has_book_pagination,
    book_pub_code, book_keywords, book_type, special_offer, area_discount, can_tts, size, book_drm_ref, discount_single_id,
    discount_single_type, discount_single_value, discount_single_start_time, discount_single_end_time,
    discount_single_is_valid, discount_single_description, discount_id, discount_description,
    discount_is_valid, is_in_cn, recommended_order, recommended_caption, print_permission,
    copy_permission, ebook_format, shelf_status, vendor_percent, sales_model, opensale_at,cart.cart_id

    from enyan_book book INNER JOIN enyan_wish wish on book.book_id = wish.book_id
        left outer join enyan_cart cart on wish.user_email = cart.user_email and wish.book_id = cart.book_id
      WHERE
      wish.user_email = #{record.userEmail} order by wish.wished_at desc

      <if test="example.page != null">
          <![CDATA[ LIMIT #{example.page.pageSize} OFFSET #{example.page.recordIndex}]]>
      </if>
  </select>

  <select id="searchCountByExample" parameterType="com.aaron.spring.model.EnyanBookExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM enyan_wish wish
      WHERE
        wish.user_email = #{record.userEmail}
  </select>

  <!--<select id="searchCountByExample" parameterType="com.aaron.spring.model.EnyanBookExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
      enyan_book book

        INNER JOIN enyan_wish wish
    WHERE
      wish.user_email = #{record.userEmail}
      AND book.book_id = wish.book_id

  </select>-->

  <update id="updateByExample" parameterType="map">
    update enyan_book
    <set>
      <if test="record.bookTitle != null">
        book_title = #{record.bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.author != null">
        author = #{record.author,jdbcType=VARCHAR},
      </if>
      <if test="record.authorBio != null">
        author_bio = #{record.authorBio,jdbcType=VARCHAR},
      </if>
      <if test="record.translator != null">
        translator = #{record.translator,jdbcType=VARCHAR},
      </if>
      <if test="record.wordCount != null">
        word_count = #{record.wordCount,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.salesVolume != null">
        sales_volume = #{record.salesVolume,jdbcType=BIGINT},
      </if>
      <if test="record.priceCny != null">
        price_cny = #{record.priceCny,jdbcType=DECIMAL},
      </if>
      <if test="record.priceUsd != null">
        price_usd = #{record.priceUsd,jdbcType=DECIMAL},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.categoryName != null">
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.publisherName != null">
        publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      </if>
      <if test="record.publishedAt != null">
        published_at = #{record.publishedAt,jdbcType=VARCHAR},
      </if>
      <if test="record.bookCatalogue != null">
        book_catalogue = #{record.bookCatalogue,jdbcType=VARCHAR},
      </if>
      <if test="record.bookCover != null">
        book_cover = #{record.bookCover,jdbcType=VARCHAR},
      </if>
      <if test="record.bookSample != null">
        book_sample = #{record.bookSample,jdbcType=VARCHAR},
      </if>
      <if test="record.bookFull != null">
        book_full = #{record.bookFull,jdbcType=VARCHAR},
      </if>
      <if test="record.bookHash != null">
        book_hash = #{record.bookHash,jdbcType=VARCHAR},
      </if>
      <if test="record.bookIsbn != null">
        book_isbn = #{record.bookIsbn,jdbcType=VARCHAR},
      </if>
      <if test="record.bookWeb != null">
        book_web = #{record.bookWeb,jdbcType=VARCHAR},
      </if>
      <if test="record.discountSingleType != null">
        discount_single_type = #{record.discountSingleType,jdbcType=TINYINT},
      </if>
      <if test="record.discountSingleValue != null">
        discount_single_value = #{record.discountSingleValue,jdbcType=INTEGER},
      </if>
      <if test="record.discountSingleStartTime != null">
        discount_single_start_time = #{record.discountSingleStartTime,jdbcType=DATE},
      </if>
      <if test="record.discountSingleEndTime != null">
        discount_single_end_time = #{record.discountSingleEndTime,jdbcType=DATE},
      </if>
      <if test="record.discountSingleIsValid != null">
        discount_single_is_valid = #{record.discountSingleIsValid,jdbcType=TINYINT},
      </if>
      <if test="record.discountSingleDescription != null">
        discount_single_description = #{record.discountSingleDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.discountId != null">
        discount_id = #{record.discountId,jdbcType=BIGINT},
      </if>
      <if test="record.discountDescription != null">
        discount_description = #{record.discountDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.discountIsValid != null">
        discount_is_valid = #{record.discountIsValid,jdbcType=TINYINT},
      </if>
      <if test="record.isInCn != null">
        is_in_cn = #{record.isInCn,jdbcType=TINYINT},
      </if>
      <if test="record.recommendedOrder != null">
        recommended_order = #{record.recommendedOrder,jdbcType=INTEGER},
      </if>
      <if test="record.recommendedCaption != null">
        recommended_caption = #{record.recommendedCaption,jdbcType=VARCHAR},
      </if>
      <if test="record.printPermission != null">
        print_permission = #{record.printPermission,jdbcType=TINYINT},
      </if>
      <if test="record.copyPermission != null">
        copy_permission = #{record.copyPermission,jdbcType=TINYINT},
      </if>
      <if test="record.ebookFormat != null">
        ebook_format = #{record.ebookFormat,jdbcType=TINYINT},
      </if>
      <if test="record.shelfStatus != null">
        shelf_status = #{record.shelfStatus,jdbcType=TINYINT},
      </if>
      <if test="record.opensaleAt != null">
        opensale_at = #{record.opensaleAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bookDescription != null">
        book_description = #{record.bookDescription,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>