<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanCouponCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanCoupon">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="coupon_name" jdbcType="VARCHAR" property="couponName" />
    <result column="coupon_code" jdbcType="VARCHAR" property="couponCode" />
    <result column="coupon_value" jdbcType="INTEGER" property="couponValue" />
    <result column="buy_max" jdbcType="INTEGER" property="buyMax" />
    <result column="use_max" jdbcType="INTEGER" property="useMax" />
    <result column="min_limit_value" jdbcType="INTEGER" property="minLimitValue" />
    <result column="buy_count" jdbcType="INTEGER" property="buyCount" />
    <result column="coupon_type" jdbcType="INTEGER" property="couponType" />
    <result column="coupon_status" jdbcType="INTEGER" property="couponStatus" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanCoupon">
    <result column="book_string" jdbcType="LONGVARCHAR" property="bookString" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, coupoon_name, coupoon_code, buy_max, buy_count, coupoon_status, begin_time, 
    end_time, create_at
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanCouponExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_coupon
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_coupon
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanCouponExample">
    delete from enyan_coupon
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanCoupon">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_coupon (coupoon_name, coupoon_code, buy_max, 
      buy_count, coupoon_status, begin_time, 
      end_time, create_at)
    values (#{coupoonName,jdbcType=VARCHAR}, #{coupoonCode,jdbcType=VARCHAR}, #{buyMax,jdbcType=INTEGER}, 
      #{buyCount,jdbcType=INTEGER}, #{coupoonStatus,jdbcType=INTEGER}, #{beginTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{createAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanCoupon">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_coupon
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="coupoonName != null">
        coupoon_name,
      </if>
      <if test="coupoonCode != null">
        coupoon_code,
      </if>
      <if test="buyMax != null">
        buy_max,
      </if>
      <if test="buyCount != null">
        buy_count,
      </if>
      <if test="coupoonStatus != null">
        coupoon_status,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="coupoonName != null">
        #{coupoonName,jdbcType=VARCHAR},
      </if>
      <if test="coupoonCode != null">
        #{coupoonCode,jdbcType=VARCHAR},
      </if>
      <if test="buyMax != null">
        #{buyMax,jdbcType=INTEGER},
      </if>
      <if test="buyCount != null">
        #{buyCount,jdbcType=INTEGER},
      </if>
      <if test="coupoonStatus != null">
        #{coupoonStatus,jdbcType=INTEGER},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanCouponExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_coupon
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_coupon
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_coupon
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.coupoonName != null">
        coupoon_name = #{record.coupoonName,jdbcType=VARCHAR},
      </if>
      <if test="record.coupoonCode != null">
        coupoon_code = #{record.coupoonCode,jdbcType=VARCHAR},
      </if>
      <if test="record.buyMax != null">
        buy_max = #{record.buyMax,jdbcType=INTEGER},
      </if>
      <if test="record.buyCount != null">
        buy_count = #{record.buyCount,jdbcType=INTEGER},
      </if>
      <if test="record.coupoonStatus != null">
        coupoon_status = #{record.coupoonStatus,jdbcType=INTEGER},
      </if>
      <if test="record.beginTime != null">
        begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_coupon
    set data_id = #{record.dataId,jdbcType=BIGINT},
      coupoon_name = #{record.coupoonName,jdbcType=VARCHAR},
      coupoon_code = #{record.coupoonCode,jdbcType=VARCHAR},
      buy_max = #{record.buyMax,jdbcType=INTEGER},
      buy_count = #{record.buyCount,jdbcType=INTEGER},
      coupoon_status = #{record.coupoonStatus,jdbcType=INTEGER},
      begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      create_at = #{record.createAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanCoupon">
    update enyan_coupon
    <set>
      <if test="coupoonName != null">
        coupoon_name = #{coupoonName,jdbcType=VARCHAR},
      </if>
      <if test="coupoonCode != null">
        coupoon_code = #{coupoonCode,jdbcType=VARCHAR},
      </if>
      <if test="buyMax != null">
        buy_max = #{buyMax,jdbcType=INTEGER},
      </if>
      <if test="buyCount != null">
        buy_count = #{buyCount,jdbcType=INTEGER},
      </if>
      <if test="coupoonStatus != null">
        coupoon_status = #{coupoonStatus,jdbcType=INTEGER},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanCoupon">
    update enyan_coupon
    set coupoon_name = #{coupoonName,jdbcType=VARCHAR},
      coupoon_code = #{coupoonCode,jdbcType=VARCHAR},
      buy_max = #{buyMax,jdbcType=INTEGER},
      buy_count = #{buyCount,jdbcType=INTEGER},
      coupoon_status = #{coupoonStatus,jdbcType=INTEGER},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_at = #{createAt,jdbcType=TIMESTAMP}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>