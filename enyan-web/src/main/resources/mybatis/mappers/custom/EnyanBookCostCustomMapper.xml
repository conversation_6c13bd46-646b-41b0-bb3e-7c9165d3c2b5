<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.custom.EnyanBookCostCustomMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanBookCost">
    <id column="book_cost_id" jdbcType="BIGINT" property="bookCostId" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="book_cost" jdbcType="INTEGER" property="bookCost" />
    <result column="purchased_at" jdbcType="TIMESTAMP" property="purchasedAt" />
    <result column="is_counted" jdbcType="TINYINT" property="isCounted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    book_cost_id, book_id, book_title, publisher_id, book_cost, purchased_at, is_counted
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanBookCostExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_book_cost
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_book_cost
    where book_cost_id = #{bookCostId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_book_cost
    where book_cost_id = #{bookCostId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanBookCostExample">
    delete from enyan_book_cost
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insertBookCostBatch" parameterType="java.util.List">
    <selectKey keyProperty="bookCostId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_book_cost (book_id, book_title, publisher_id, 
      book_cost, purchased_at, is_counted
      )values
    <foreach collection ="bookCostList" item="cost" index= "index" separator =",">
      (#{cost.bookId,jdbcType=BIGINT}, #{cost.bookTitle,jdbcType=VARCHAR}, #{cost.publisherId,jdbcType=BIGINT},
      #{cost.bookCost,jdbcType=INTEGER}, #{cost.purchasedAt,jdbcType=TIMESTAMP}, #{cost.isCounted,jdbcType=TINYINT}
      )
    </foreach>
  </insert>

    <sql id="MySqlPaginationSuffix">
        <if test="page != null">
            <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
        </if>
    </sql>
</mapper>