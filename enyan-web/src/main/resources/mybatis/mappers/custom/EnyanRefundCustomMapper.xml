<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanRefund">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanRefund">
    <id column="refund_id" jdbcType="BIGINT" property="refundId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="book_title" jdbcType="VARCHAR" property="bookTitle" />
    <result column="publisher_id" jdbcType="BIGINT" property="publisherId" />
    <result column="publisher_name" jdbcType="VARCHAR" property="publisherName" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="purchased_at" jdbcType="TIMESTAMP" property="purchasedAt" />
    <result column="sales_volume_decrease" jdbcType="INTEGER" property="salesVolumeDecrease" />
    <result column="income_total_decrease" jdbcType="DECIMAL" property="incomeTotalDecrease" />
    <result column="reason_type" jdbcType="INTEGER" property="reasonType" />
    <result column="reason_content" jdbcType="VARCHAR" property="reasonContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    refund_id, order_num, user_id, user_email, book_id, book_title, publisher_id, publisher_name, 
    order_type, purchased_at, sales_volume_decrease, income_total_decrease, reason_type, 
    reason_content, create_time, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanRefundExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_refund
    where refund_id = #{refundId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_refund
    where refund_id = #{refundId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanRefundExample">
    delete from enyan_refund
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanRefund">
    <selectKey keyProperty="refundId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_refund (order_num, user_id, user_email, 
      book_id, book_title, publisher_id, 
      publisher_name, order_type, purchased_at, 
      sales_volume_decrease, income_total_decrease, 
      reason_type, reason_content, create_time, 
      is_deleted)
    values (#{orderNum,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, 
      #{bookId,jdbcType=BIGINT}, #{bookTitle,jdbcType=VARCHAR}, #{publisherId,jdbcType=BIGINT}, 
      #{publisherName,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, #{purchasedAt,jdbcType=TIMESTAMP}, 
      #{salesVolumeDecrease,jdbcType=INTEGER}, #{incomeTotalDecrease,jdbcType=DECIMAL}, 
      #{reasonType,jdbcType=INTEGER}, #{reasonContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanRefund">
    <selectKey keyProperty="refundId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_refund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="bookTitle != null">
        book_title,
      </if>
      <if test="publisherId != null">
        publisher_id,
      </if>
      <if test="publisherName != null">
        publisher_name,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="purchasedAt != null">
        purchased_at,
      </if>
      <if test="salesVolumeDecrease != null">
        sales_volume_decrease,
      </if>
      <if test="incomeTotalDecrease != null">
        income_total_decrease,
      </if>
      <if test="reasonType != null">
        reason_type,
      </if>
      <if test="reasonContent != null">
        reason_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="bookTitle != null">
        #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="publisherName != null">
        #{publisherName,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="purchasedAt != null">
        #{purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="salesVolumeDecrease != null">
        #{salesVolumeDecrease,jdbcType=INTEGER},
      </if>
      <if test="incomeTotalDecrease != null">
        #{incomeTotalDecrease,jdbcType=DECIMAL},
      </if>
      <if test="reasonType != null">
        #{reasonType,jdbcType=INTEGER},
      </if>
      <if test="reasonContent != null">
        #{reasonContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanRefundExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_refund
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_refund
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_refund
    <set>
      <if test="record.refundId != null">
        refund_id = #{record.refundId,jdbcType=BIGINT},
      </if>
      <if test="record.orderNum != null">
        order_num = #{record.orderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.bookTitle != null">
        book_title = #{record.bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.publisherId != null">
        publisher_id = #{record.publisherId,jdbcType=BIGINT},
      </if>
      <if test="record.publisherName != null">
        publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.purchasedAt != null">
        purchased_at = #{record.purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.salesVolumeDecrease != null">
        sales_volume_decrease = #{record.salesVolumeDecrease,jdbcType=INTEGER},
      </if>
      <if test="record.incomeTotalDecrease != null">
        income_total_decrease = #{record.incomeTotalDecrease,jdbcType=DECIMAL},
      </if>
      <if test="record.reasonType != null">
        reason_type = #{record.reasonType,jdbcType=INTEGER},
      </if>
      <if test="record.reasonContent != null">
        reason_content = #{record.reasonContent,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_refund
    set refund_id = #{record.refundId,jdbcType=BIGINT},
      order_num = #{record.orderNum,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      book_title = #{record.bookTitle,jdbcType=VARCHAR},
      publisher_id = #{record.publisherId,jdbcType=BIGINT},
      publisher_name = #{record.publisherName,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      purchased_at = #{record.purchasedAt,jdbcType=TIMESTAMP},
      sales_volume_decrease = #{record.salesVolumeDecrease,jdbcType=INTEGER},
      income_total_decrease = #{record.incomeTotalDecrease,jdbcType=DECIMAL},
      reason_type = #{record.reasonType,jdbcType=INTEGER},
      reason_content = #{record.reasonContent,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanRefund">
    update enyan_refund
    <set>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="bookTitle != null">
        book_title = #{bookTitle,jdbcType=VARCHAR},
      </if>
      <if test="publisherId != null">
        publisher_id = #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="publisherName != null">
        publisher_name = #{publisherName,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="purchasedAt != null">
        purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="salesVolumeDecrease != null">
        sales_volume_decrease = #{salesVolumeDecrease,jdbcType=INTEGER},
      </if>
      <if test="incomeTotalDecrease != null">
        income_total_decrease = #{incomeTotalDecrease,jdbcType=DECIMAL},
      </if>
      <if test="reasonType != null">
        reason_type = #{reasonType,jdbcType=INTEGER},
      </if>
      <if test="reasonContent != null">
        reason_content = #{reasonContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where refund_id = #{refundId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanRefund">
    update enyan_refund
    set order_num = #{orderNum,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      user_email = #{userEmail,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      book_title = #{bookTitle,jdbcType=VARCHAR},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      publisher_name = #{publisherName,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      purchased_at = #{purchasedAt,jdbcType=TIMESTAMP},
      sales_volume_decrease = #{salesVolumeDecrease,jdbcType=INTEGER},
      income_total_decrease = #{incomeTotalDecrease,jdbcType=DECIMAL},
      reason_type = #{reasonType,jdbcType=INTEGER},
      reason_content = #{reasonContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER}
    where refund_id = #{refundId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>