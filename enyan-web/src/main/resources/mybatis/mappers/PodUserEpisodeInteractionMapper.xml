<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aaron.spring.mapper.PodUserEpisodeInteractionMapper" >
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.PodUserEpisodeInteraction" >
    <id column="interaction_id" property="interactionId" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="user_email" property="userEmail" jdbcType="VARCHAR" />
    <result column="episode_id" property="episodeId" jdbcType="BIGINT" />
    <result column="is_liked" property="isLiked" jdbcType="TINYINT" />
    <result column="playback_progress_seconds" property="playbackProgressSeconds" jdbcType="INTEGER" />
    <result column="cumulative_playback_seconds" property="cumulativePlaybackSeconds" jdbcType="INTEGER" />
    <result column="last_played_at" property="lastPlayedAt" jdbcType="TIMESTAMP" />
    <result column="is_completed" property="isCompleted" jdbcType="TINYINT" />
    <result column="downloaded_at" property="downloadedAt" jdbcType="TIMESTAMP" />
    <result column="created_at" property="createdAt" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  
  <sql id="Base_Column_List" >
    interaction_id, user_id, user_email, episode_id, is_liked, playback_progress_seconds, 
    cumulative_playback_seconds, last_played_at, is_completed, downloaded_at, created_at
  </sql>
  
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aaron.spring.model.PodUserEpisodeInteractionExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pod_user_episode_interaction
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pod_user_episode_interaction
    where interaction_id = #{interactionId,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from pod_user_episode_interaction
    where interaction_id = #{interactionId,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.PodUserEpisodeInteractionExample" >
    delete from pod_user_episode_interaction
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  
  <insert id="insert" parameterType="com.aaron.spring.model.PodUserEpisodeInteraction" useGeneratedKeys="true" keyProperty="interactionId">
    insert into pod_user_episode_interaction (user_id, user_email, episode_id, 
      is_liked, playback_progress_seconds, cumulative_playback_seconds, 
      last_played_at, is_completed, downloaded_at, created_at)
    values (#{userId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, 
      #{episodeId,jdbcType=BIGINT}, #{isLiked,jdbcType=TINYINT}, 
      #{playbackProgressSeconds,jdbcType=INTEGER}, #{cumulativePlaybackSeconds,jdbcType=INTEGER}, 
      #{lastPlayedAt,jdbcType=TIMESTAMP}, #{isCompleted,jdbcType=TINYINT}, 
      #{downloadedAt,jdbcType=TIMESTAMP}, #{createdAt,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.aaron.spring.model.PodUserEpisodeInteraction" useGeneratedKeys="true" keyProperty="interactionId">
    insert into pod_user_episode_interaction
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userId != null" >
        user_id,
      </if>
      <if test="userEmail != null" >
        user_email,
      </if>
      <if test="episodeId != null" >
        episode_id,
      </if>
      <if test="isLiked != null" >
        is_liked,
      </if>
      <if test="playbackProgressSeconds != null" >
        playback_progress_seconds,
      </if>
      <if test="cumulativePlaybackSeconds != null" >
        cumulative_playback_seconds,
      </if>
      <if test="lastPlayedAt != null" >
        last_played_at,
      </if>
      <if test="isCompleted != null" >
        is_completed,
      </if>
      <if test="downloadedAt != null" >
        downloaded_at,
      </if>
      <if test="createdAt != null" >
        created_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null" >
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="episodeId != null" >
        #{episodeId,jdbcType=BIGINT},
      </if>
      <if test="isLiked != null" >
        #{isLiked,jdbcType=TINYINT},
      </if>
      <if test="playbackProgressSeconds != null" >
        #{playbackProgressSeconds,jdbcType=INTEGER},
      </if>
      <if test="cumulativePlaybackSeconds != null" >
        #{cumulativePlaybackSeconds,jdbcType=INTEGER},
      </if>
      <if test="lastPlayedAt != null" >
        #{lastPlayedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isCompleted != null" >
        #{isCompleted,jdbcType=TINYINT},
      </if>
      <if test="downloadedAt != null" >
        #{downloadedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null" >
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByExampleSelective" parameterType="map" >
    update pod_user_episode_interaction
    <set >
      <if test="record.userId != null" >
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userEmail != null" >
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.episodeId != null" >
        episode_id = #{record.episodeId,jdbcType=BIGINT},
      </if>
      <if test="record.isLiked != null" >
        is_liked = #{record.isLiked,jdbcType=TINYINT},
      </if>
      <if test="record.playbackProgressSeconds != null" >
        playback_progress_seconds = #{record.playbackProgressSeconds,jdbcType=INTEGER},
      </if>
      <if test="record.cumulativePlaybackSeconds != null" >
        cumulative_playback_seconds = #{record.cumulativePlaybackSeconds,jdbcType=INTEGER},
      </if>
      <if test="record.lastPlayedAt != null" >
        last_played_at = #{record.lastPlayedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isCompleted != null" >
        is_completed = #{record.isCompleted,jdbcType=TINYINT},
      </if>
      <if test="record.downloadedAt != null" >
        downloaded_at = #{record.downloadedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByExample" parameterType="map" >
    update pod_user_episode_interaction
    <set >
      user_id = #{record.userId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      episode_id = #{record.episodeId,jdbcType=BIGINT},
      is_liked = #{record.isLiked,jdbcType=TINYINT},
      playback_progress_seconds = #{record.playbackProgressSeconds,jdbcType=INTEGER},
      cumulative_playback_seconds = #{record.cumulativePlaybackSeconds,jdbcType=INTEGER},
      last_played_at = #{record.lastPlayedAt,jdbcType=TIMESTAMP},
      is_completed = #{record.isCompleted,jdbcType=TINYINT},
      downloaded_at = #{record.downloadedAt,jdbcType=TIMESTAMP},
    </set>
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.PodUserEpisodeInteraction" >
    update pod_user_episode_interaction
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null" >
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="episodeId != null" >
        episode_id = #{episodeId,jdbcType=BIGINT},
      </if>
      <if test="isLiked != null" >
        is_liked = #{isLiked,jdbcType=TINYINT},
      </if>
      <if test="playbackProgressSeconds != null" >
        playback_progress_seconds = #{playbackProgressSeconds,jdbcType=INTEGER},
      </if>
      <if test="cumulativePlaybackSeconds != null" >
        cumulative_playback_seconds = #{cumulativePlaybackSeconds,jdbcType=INTEGER},
      </if>
      <if test="lastPlayedAt != null" >
        last_played_at = #{lastPlayedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isCompleted != null" >
        is_completed = #{isCompleted,jdbcType=TINYINT},
      </if>
      <if test="downloadedAt != null" >
        downloaded_at = #{downloadedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where interaction_id = #{interactionId,jdbcType=BIGINT}
  </update>
</mapper>
