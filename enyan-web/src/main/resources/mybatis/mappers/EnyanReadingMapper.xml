<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanReadingMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanReading">
    <id column="data_id" jdbcType="BIGINT" property="dataId" />
    <result column="data_name" jdbcType="VARCHAR" property="dataName" />
    <result column="data_img_url" jdbcType="VARCHAR" property="dataImgUrl" />
    <result column="data_to_url" jdbcType="VARCHAR" property="dataToUrl" />
    <result column="data_buy_url" jdbcType="VARCHAR" property="dataBuyUrl" />
    <result column="data_read_show" jdbcType="INTEGER" property="dataReadShow" />
    <result column="data_priority" jdbcType="INTEGER" property="dataPriority" />
    <result column="data_status" jdbcType="INTEGER" property="dataStatus" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="begin_at" jdbcType="TIMESTAMP" property="beginAt" />
    <result column="end_at" jdbcType="TIMESTAMP" property="endAt" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    data_id, data_name, data_img_url, data_to_url, data_buy_url, data_read_show, data_priority, 
    data_status, is_deleted, begin_at, end_at, create_at
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanReadingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_reading
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_reading
    where data_id = #{dataId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_reading
    where data_id = #{dataId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanReadingExample">
    delete from enyan_reading
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanReading">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_reading (data_name, data_img_url, data_to_url, 
      data_buy_url, data_read_show, data_priority, 
      data_status, is_deleted, begin_at, 
      end_at, create_at)
    values (#{dataName,jdbcType=VARCHAR}, #{dataImgUrl,jdbcType=VARCHAR}, #{dataToUrl,jdbcType=VARCHAR}, 
      #{dataBuyUrl,jdbcType=VARCHAR}, #{dataReadShow,jdbcType=INTEGER}, #{dataPriority,jdbcType=INTEGER}, 
      #{dataStatus,jdbcType=INTEGER}, #{isDeleted,jdbcType=INTEGER}, #{beginAt,jdbcType=TIMESTAMP}, 
      #{endAt,jdbcType=TIMESTAMP}, #{createAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanReading">
    <selectKey keyProperty="dataId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_reading
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataName != null">
        data_name,
      </if>
      <if test="dataImgUrl != null">
        data_img_url,
      </if>
      <if test="dataToUrl != null">
        data_to_url,
      </if>
      <if test="dataBuyUrl != null">
        data_buy_url,
      </if>
      <if test="dataReadShow != null">
        data_read_show,
      </if>
      <if test="dataPriority != null">
        data_priority,
      </if>
      <if test="dataStatus != null">
        data_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="beginAt != null">
        begin_at,
      </if>
      <if test="endAt != null">
        end_at,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataName != null">
        #{dataName,jdbcType=VARCHAR},
      </if>
      <if test="dataImgUrl != null">
        #{dataImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataToUrl != null">
        #{dataToUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataBuyUrl != null">
        #{dataBuyUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataReadShow != null">
        #{dataReadShow,jdbcType=INTEGER},
      </if>
      <if test="dataPriority != null">
        #{dataPriority,jdbcType=INTEGER},
      </if>
      <if test="dataStatus != null">
        #{dataStatus,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="beginAt != null">
        #{beginAt,jdbcType=TIMESTAMP},
      </if>
      <if test="endAt != null">
        #{endAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanReadingExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_reading
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_reading
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_reading
    <set>
      <if test="record.dataId != null">
        data_id = #{record.dataId,jdbcType=BIGINT},
      </if>
      <if test="record.dataName != null">
        data_name = #{record.dataName,jdbcType=VARCHAR},
      </if>
      <if test="record.dataImgUrl != null">
        data_img_url = #{record.dataImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.dataToUrl != null">
        data_to_url = #{record.dataToUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.dataBuyUrl != null">
        data_buy_url = #{record.dataBuyUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.dataReadShow != null">
        data_read_show = #{record.dataReadShow,jdbcType=INTEGER},
      </if>
      <if test="record.dataPriority != null">
        data_priority = #{record.dataPriority,jdbcType=INTEGER},
      </if>
      <if test="record.dataStatus != null">
        data_status = #{record.dataStatus,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.beginAt != null">
        begin_at = #{record.beginAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endAt != null">
        end_at = #{record.endAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_reading
    set data_id = #{record.dataId,jdbcType=BIGINT},
      data_name = #{record.dataName,jdbcType=VARCHAR},
      data_img_url = #{record.dataImgUrl,jdbcType=VARCHAR},
      data_to_url = #{record.dataToUrl,jdbcType=VARCHAR},
      data_buy_url = #{record.dataBuyUrl,jdbcType=VARCHAR},
      data_read_show = #{record.dataReadShow,jdbcType=INTEGER},
      data_priority = #{record.dataPriority,jdbcType=INTEGER},
      data_status = #{record.dataStatus,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      begin_at = #{record.beginAt,jdbcType=TIMESTAMP},
      end_at = #{record.endAt,jdbcType=TIMESTAMP},
      create_at = #{record.createAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanReading">
    update enyan_reading
    <set>
      <if test="dataName != null">
        data_name = #{dataName,jdbcType=VARCHAR},
      </if>
      <if test="dataImgUrl != null">
        data_img_url = #{dataImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataToUrl != null">
        data_to_url = #{dataToUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataBuyUrl != null">
        data_buy_url = #{dataBuyUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataReadShow != null">
        data_read_show = #{dataReadShow,jdbcType=INTEGER},
      </if>
      <if test="dataPriority != null">
        data_priority = #{dataPriority,jdbcType=INTEGER},
      </if>
      <if test="dataStatus != null">
        data_status = #{dataStatus,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="beginAt != null">
        begin_at = #{beginAt,jdbcType=TIMESTAMP},
      </if>
      <if test="endAt != null">
        end_at = #{endAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanReading">
    update enyan_reading
    set data_name = #{dataName,jdbcType=VARCHAR},
      data_img_url = #{dataImgUrl,jdbcType=VARCHAR},
      data_to_url = #{dataToUrl,jdbcType=VARCHAR},
      data_buy_url = #{dataBuyUrl,jdbcType=VARCHAR},
      data_read_show = #{dataReadShow,jdbcType=INTEGER},
      data_priority = #{dataPriority,jdbcType=INTEGER},
      data_status = #{dataStatus,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      begin_at = #{beginAt,jdbcType=TIMESTAMP},
      end_at = #{endAt,jdbcType=TIMESTAMP},
      create_at = #{createAt,jdbcType=TIMESTAMP}
    where data_id = #{dataId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>