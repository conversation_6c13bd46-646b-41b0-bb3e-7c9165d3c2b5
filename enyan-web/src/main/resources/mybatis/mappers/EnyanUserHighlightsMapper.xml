<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanUserHighlightsMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanUserHighlights">
    <id column="highlight_id" jdbcType="VARCHAR" property="highlightId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="rangy" jdbcType="VARCHAR" property="rangy" />
    <result column="note_for_highlight" jdbcType="VARCHAR" property="noteForHighlight" />
    <result column="chapter_name" jdbcType="VARCHAR" property="chapterName" />
    <result column="page_chapter" jdbcType="INTEGER" property="pageChapter" />
    <result column="current_page" jdbcType="INTEGER" property="currentPage" />
    <result column="total_page" jdbcType="INTEGER" property="totalPage" />
    <result column="page_process" jdbcType="INTEGER" property="pageProcess" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    highlight_id, user_id, user_email, book_id, content, create_time, rangy, note_for_highlight, 
    chapter_name, page_chapter, current_page, total_page, page_process, is_deleted, update_time
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanUserHighlightsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_user_highlights
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_user_highlights
    where highlight_id = #{highlightId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from enyan_user_highlights
    where highlight_id = #{highlightId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanUserHighlightsExample">
    delete from enyan_user_highlights
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanUserHighlights">
    <selectKey keyProperty="highlightId" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_user_highlights (user_id, user_email, book_id, 
      content, create_time, rangy, 
      note_for_highlight, chapter_name, page_chapter, 
      current_page, total_page, page_process, 
      is_deleted, update_time)
    values (#{userId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, 
      #{content,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{rangy,jdbcType=VARCHAR}, 
      #{noteForHighlight,jdbcType=VARCHAR}, #{chapterName,jdbcType=VARCHAR}, #{pageChapter,jdbcType=INTEGER}, 
      #{currentPage,jdbcType=INTEGER}, #{totalPage,jdbcType=INTEGER}, #{pageProcess,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=INTEGER}, #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanUserHighlights">
    <selectKey keyProperty="highlightId" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_user_highlights
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="rangy != null">
        rangy,
      </if>
      <if test="noteForHighlight != null">
        note_for_highlight,
      </if>
      <if test="chapterName != null">
        chapter_name,
      </if>
      <if test="pageChapter != null">
        page_chapter,
      </if>
      <if test="currentPage != null">
        current_page,
      </if>
      <if test="totalPage != null">
        total_page,
      </if>
      <if test="pageProcess != null">
        page_process,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="rangy != null">
        #{rangy,jdbcType=VARCHAR},
      </if>
      <if test="noteForHighlight != null">
        #{noteForHighlight,jdbcType=VARCHAR},
      </if>
      <if test="chapterName != null">
        #{chapterName,jdbcType=VARCHAR},
      </if>
      <if test="pageChapter != null">
        #{pageChapter,jdbcType=INTEGER},
      </if>
      <if test="currentPage != null">
        #{currentPage,jdbcType=INTEGER},
      </if>
      <if test="totalPage != null">
        #{totalPage,jdbcType=INTEGER},
      </if>
      <if test="pageProcess != null">
        #{pageProcess,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanUserHighlightsExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_user_highlights
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_user_highlights
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_user_highlights
    <set>
      <if test="record.highlightId != null">
        highlight_id = #{record.highlightId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=BIGINT},
      </if>
      <if test="record.rangy != null">
        rangy = #{record.rangy,jdbcType=VARCHAR},
      </if>
      <if test="record.noteForHighlight != null">
        note_for_highlight = #{record.noteForHighlight,jdbcType=VARCHAR},
      </if>
      <if test="record.chapterName != null">
        chapter_name = #{record.chapterName,jdbcType=VARCHAR},
      </if>
      <if test="record.pageChapter != null">
        page_chapter = #{record.pageChapter,jdbcType=INTEGER},
      </if>
      <if test="record.currentPage != null">
        current_page = #{record.currentPage,jdbcType=INTEGER},
      </if>
      <if test="record.totalPage != null">
        total_page = #{record.totalPage,jdbcType=INTEGER},
      </if>
      <if test="record.pageProcess != null">
        page_process = #{record.pageProcess,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_user_highlights
    set highlight_id = #{record.highlightId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      content = #{record.content,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=BIGINT},
      rangy = #{record.rangy,jdbcType=VARCHAR},
      note_for_highlight = #{record.noteForHighlight,jdbcType=VARCHAR},
      chapter_name = #{record.chapterName,jdbcType=VARCHAR},
      page_chapter = #{record.pageChapter,jdbcType=INTEGER},
      current_page = #{record.currentPage,jdbcType=INTEGER},
      total_page = #{record.totalPage,jdbcType=INTEGER},
      page_process = #{record.pageProcess,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanUserHighlights">
    update enyan_user_highlights
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="rangy != null">
        rangy = #{rangy,jdbcType=VARCHAR},
      </if>
      <if test="noteForHighlight != null">
        note_for_highlight = #{noteForHighlight,jdbcType=VARCHAR},
      </if>
      <if test="chapterName != null">
        chapter_name = #{chapterName,jdbcType=VARCHAR},
      </if>
      <if test="pageChapter != null">
        page_chapter = #{pageChapter,jdbcType=INTEGER},
      </if>
      <if test="currentPage != null">
        current_page = #{currentPage,jdbcType=INTEGER},
      </if>
      <if test="totalPage != null">
        total_page = #{totalPage,jdbcType=INTEGER},
      </if>
      <if test="pageProcess != null">
        page_process = #{pageProcess,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where highlight_id = #{highlightId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanUserHighlights">
    update enyan_user_highlights
    set user_id = #{userId,jdbcType=BIGINT},
      user_email = #{userEmail,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      content = #{content,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      rangy = #{rangy,jdbcType=VARCHAR},
      note_for_highlight = #{noteForHighlight,jdbcType=VARCHAR},
      chapter_name = #{chapterName,jdbcType=VARCHAR},
      page_chapter = #{pageChapter,jdbcType=INTEGER},
      current_page = #{currentPage,jdbcType=INTEGER},
      total_page = #{totalPage,jdbcType=INTEGER},
      page_process = #{pageProcess,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=BIGINT}
    where highlight_id = #{highlightId,jdbcType=VARCHAR}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>