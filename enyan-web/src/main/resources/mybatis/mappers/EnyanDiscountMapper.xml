<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanDiscountMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanDiscount">
    <id column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="discount_title" jdbcType="VARCHAR" property="discountTitle" />
    <result column="discount_type" jdbcType="TINYINT" property="discountType" />
    <result column="book_count" jdbcType="INTEGER" property="bookCount" />
    <result column="cumulate_package" jdbcType="INTEGER" property="cumulatePackage" />
    <result column="cumulate_discount" jdbcType="INTEGER" property="cumulateDiscount" />
    <result column="cumulate_package_muti" jdbcType="INTEGER" property="cumulatePackageMuti" />
    <result column="cumulate_discount_muti" jdbcType="INTEGER" property="cumulateDiscountMuti" />
    <result column="full_base" jdbcType="INTEGER" property="fullBase" />
    <result column="full_minus" jdbcType="INTEGER" property="fullMinus" />
    <result column="discount_single_value" jdbcType="INTEGER" property="discountSingleValue" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="is_show" jdbcType="TINYINT" property="isShow" />
    <result column="start_time" jdbcType="DATE" property="startTime" />
    <result column="end_time" jdbcType="DATE" property="endTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    discount_id, discount_title, discount_type, book_count, cumulate_package, cumulate_discount, 
    cumulate_package_muti, cumulate_discount_muti, full_base, full_minus, discount_single_value, 
    is_valid, is_show, start_time, end_time
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanDiscountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_discount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_discount
    where discount_id = #{discountId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_discount
    where discount_id = #{discountId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanDiscountExample">
    delete from enyan_discount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanDiscount">
    <selectKey keyProperty="discountId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_discount (discount_title, discount_type, book_count, 
      cumulate_package, cumulate_discount, cumulate_package_muti, 
      cumulate_discount_muti, full_base, full_minus, 
      discount_single_value, is_valid, is_show, 
      start_time, end_time)
    values (#{discountTitle,jdbcType=VARCHAR}, #{discountType,jdbcType=TINYINT}, #{bookCount,jdbcType=INTEGER}, 
      #{cumulatePackage,jdbcType=INTEGER}, #{cumulateDiscount,jdbcType=INTEGER}, #{cumulatePackageMuti,jdbcType=INTEGER}, 
      #{cumulateDiscountMuti,jdbcType=INTEGER}, #{fullBase,jdbcType=INTEGER}, #{fullMinus,jdbcType=INTEGER}, 
      #{discountSingleValue,jdbcType=INTEGER}, #{isValid,jdbcType=TINYINT}, #{isShow,jdbcType=TINYINT}, 
      #{startTime,jdbcType=DATE}, #{endTime,jdbcType=DATE})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanDiscount">
    <selectKey keyProperty="discountId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_discount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="discountTitle != null">
        discount_title,
      </if>
      <if test="discountType != null">
        discount_type,
      </if>
      <if test="bookCount != null">
        book_count,
      </if>
      <if test="cumulatePackage != null">
        cumulate_package,
      </if>
      <if test="cumulateDiscount != null">
        cumulate_discount,
      </if>
      <if test="cumulatePackageMuti != null">
        cumulate_package_muti,
      </if>
      <if test="cumulateDiscountMuti != null">
        cumulate_discount_muti,
      </if>
      <if test="fullBase != null">
        full_base,
      </if>
      <if test="fullMinus != null">
        full_minus,
      </if>
      <if test="discountSingleValue != null">
        discount_single_value,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="isShow != null">
        is_show,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="discountTitle != null">
        #{discountTitle,jdbcType=VARCHAR},
      </if>
      <if test="discountType != null">
        #{discountType,jdbcType=TINYINT},
      </if>
      <if test="bookCount != null">
        #{bookCount,jdbcType=INTEGER},
      </if>
      <if test="cumulatePackage != null">
        #{cumulatePackage,jdbcType=INTEGER},
      </if>
      <if test="cumulateDiscount != null">
        #{cumulateDiscount,jdbcType=INTEGER},
      </if>
      <if test="cumulatePackageMuti != null">
        #{cumulatePackageMuti,jdbcType=INTEGER},
      </if>
      <if test="cumulateDiscountMuti != null">
        #{cumulateDiscountMuti,jdbcType=INTEGER},
      </if>
      <if test="fullBase != null">
        #{fullBase,jdbcType=INTEGER},
      </if>
      <if test="fullMinus != null">
        #{fullMinus,jdbcType=INTEGER},
      </if>
      <if test="discountSingleValue != null">
        #{discountSingleValue,jdbcType=INTEGER},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="isShow != null">
        #{isShow,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=DATE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanDiscountExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_discount
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_discount
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_discount
    <set>
      <if test="record.discountId != null">
        discount_id = #{record.discountId,jdbcType=BIGINT},
      </if>
      <if test="record.discountTitle != null">
        discount_title = #{record.discountTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.discountType != null">
        discount_type = #{record.discountType,jdbcType=TINYINT},
      </if>
      <if test="record.bookCount != null">
        book_count = #{record.bookCount,jdbcType=INTEGER},
      </if>
      <if test="record.cumulatePackage != null">
        cumulate_package = #{record.cumulatePackage,jdbcType=INTEGER},
      </if>
      <if test="record.cumulateDiscount != null">
        cumulate_discount = #{record.cumulateDiscount,jdbcType=INTEGER},
      </if>
      <if test="record.cumulatePackageMuti != null">
        cumulate_package_muti = #{record.cumulatePackageMuti,jdbcType=INTEGER},
      </if>
      <if test="record.cumulateDiscountMuti != null">
        cumulate_discount_muti = #{record.cumulateDiscountMuti,jdbcType=INTEGER},
      </if>
      <if test="record.fullBase != null">
        full_base = #{record.fullBase,jdbcType=INTEGER},
      </if>
      <if test="record.fullMinus != null">
        full_minus = #{record.fullMinus,jdbcType=INTEGER},
      </if>
      <if test="record.discountSingleValue != null">
        discount_single_value = #{record.discountSingleValue,jdbcType=INTEGER},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=TINYINT},
      </if>
      <if test="record.isShow != null">
        is_show = #{record.isShow,jdbcType=TINYINT},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=DATE},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=DATE},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_discount
    set discount_id = #{record.discountId,jdbcType=BIGINT},
      discount_title = #{record.discountTitle,jdbcType=VARCHAR},
      discount_type = #{record.discountType,jdbcType=TINYINT},
      book_count = #{record.bookCount,jdbcType=INTEGER},
      cumulate_package = #{record.cumulatePackage,jdbcType=INTEGER},
      cumulate_discount = #{record.cumulateDiscount,jdbcType=INTEGER},
      cumulate_package_muti = #{record.cumulatePackageMuti,jdbcType=INTEGER},
      cumulate_discount_muti = #{record.cumulateDiscountMuti,jdbcType=INTEGER},
      full_base = #{record.fullBase,jdbcType=INTEGER},
      full_minus = #{record.fullMinus,jdbcType=INTEGER},
      discount_single_value = #{record.discountSingleValue,jdbcType=INTEGER},
      is_valid = #{record.isValid,jdbcType=TINYINT},
      is_show = #{record.isShow,jdbcType=TINYINT},
      start_time = #{record.startTime,jdbcType=DATE},
      end_time = #{record.endTime,jdbcType=DATE}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanDiscount">
    update enyan_discount
    <set>
      <if test="discountTitle != null">
        discount_title = #{discountTitle,jdbcType=VARCHAR},
      </if>
      <if test="discountType != null">
        discount_type = #{discountType,jdbcType=TINYINT},
      </if>
      <if test="bookCount != null">
        book_count = #{bookCount,jdbcType=INTEGER},
      </if>
      <if test="cumulatePackage != null">
        cumulate_package = #{cumulatePackage,jdbcType=INTEGER},
      </if>
      <if test="cumulateDiscount != null">
        cumulate_discount = #{cumulateDiscount,jdbcType=INTEGER},
      </if>
      <if test="cumulatePackageMuti != null">
        cumulate_package_muti = #{cumulatePackageMuti,jdbcType=INTEGER},
      </if>
      <if test="cumulateDiscountMuti != null">
        cumulate_discount_muti = #{cumulateDiscountMuti,jdbcType=INTEGER},
      </if>
      <if test="fullBase != null">
        full_base = #{fullBase,jdbcType=INTEGER},
      </if>
      <if test="fullMinus != null">
        full_minus = #{fullMinus,jdbcType=INTEGER},
      </if>
      <if test="discountSingleValue != null">
        discount_single_value = #{discountSingleValue,jdbcType=INTEGER},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="isShow != null">
        is_show = #{isShow,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=DATE},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=DATE},
      </if>
    </set>
    where discount_id = #{discountId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanDiscount">
    update enyan_discount
    set discount_title = #{discountTitle,jdbcType=VARCHAR},
      discount_type = #{discountType,jdbcType=TINYINT},
      book_count = #{bookCount,jdbcType=INTEGER},
      cumulate_package = #{cumulatePackage,jdbcType=INTEGER},
      cumulate_discount = #{cumulateDiscount,jdbcType=INTEGER},
      cumulate_package_muti = #{cumulatePackageMuti,jdbcType=INTEGER},
      cumulate_discount_muti = #{cumulateDiscountMuti,jdbcType=INTEGER},
      full_base = #{fullBase,jdbcType=INTEGER},
      full_minus = #{fullMinus,jdbcType=INTEGER},
      discount_single_value = #{discountSingleValue,jdbcType=INTEGER},
      is_valid = #{isValid,jdbcType=TINYINT},
      is_show = #{isShow,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=DATE},
      end_time = #{endTime,jdbcType=DATE}
    where discount_id = #{discountId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>