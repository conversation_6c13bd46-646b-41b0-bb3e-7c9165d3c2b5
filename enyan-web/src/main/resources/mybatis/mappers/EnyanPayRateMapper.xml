<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanPayRateMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanPayRate">
    <id column="pay_rate_id" jdbcType="BIGINT" property="payRateId" />
    <result column="rate_date" jdbcType="INTEGER" property="rateDate" />
    <result column="rate_time" jdbcType="INTEGER" property="rateTime" />
    <result column="rate_type" jdbcType="INTEGER" property="rateType" />
    <result column="rate_value" jdbcType="VARCHAR" property="rateValue" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    pay_rate_id, rate_date, rate_time, rate_type, rate_value
  </sql>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanPayRateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_pay_rate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enyan_pay_rate
    where pay_rate_id = #{payRateId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_pay_rate
    where pay_rate_id = #{payRateId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanPayRateExample">
    delete from enyan_pay_rate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanPayRate">
    <selectKey keyProperty="payRateId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_pay_rate (rate_date, rate_time, rate_type, 
      rate_value)
    values (#{rateDate,jdbcType=INTEGER}, #{rateTime,jdbcType=INTEGER}, #{rateType,jdbcType=INTEGER}, 
      #{rateValue,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanPayRate">
    <selectKey keyProperty="payRateId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_pay_rate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rateDate != null">
        rate_date,
      </if>
      <if test="rateTime != null">
        rate_time,
      </if>
      <if test="rateType != null">
        rate_type,
      </if>
      <if test="rateValue != null">
        rate_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rateDate != null">
        #{rateDate,jdbcType=INTEGER},
      </if>
      <if test="rateTime != null">
        #{rateTime,jdbcType=INTEGER},
      </if>
      <if test="rateType != null">
        #{rateType,jdbcType=INTEGER},
      </if>
      <if test="rateValue != null">
        #{rateValue,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanPayRateExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_pay_rate
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_pay_rate
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_pay_rate
    <set>
      <if test="record.payRateId != null">
        pay_rate_id = #{record.payRateId,jdbcType=BIGINT},
      </if>
      <if test="record.rateDate != null">
        rate_date = #{record.rateDate,jdbcType=INTEGER},
      </if>
      <if test="record.rateTime != null">
        rate_time = #{record.rateTime,jdbcType=INTEGER},
      </if>
      <if test="record.rateType != null">
        rate_type = #{record.rateType,jdbcType=INTEGER},
      </if>
      <if test="record.rateValue != null">
        rate_value = #{record.rateValue,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_pay_rate
    set pay_rate_id = #{record.payRateId,jdbcType=BIGINT},
      rate_date = #{record.rateDate,jdbcType=INTEGER},
      rate_time = #{record.rateTime,jdbcType=INTEGER},
      rate_type = #{record.rateType,jdbcType=INTEGER},
      rate_value = #{record.rateValue,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanPayRate">
    update enyan_pay_rate
    <set>
      <if test="rateDate != null">
        rate_date = #{rateDate,jdbcType=INTEGER},
      </if>
      <if test="rateTime != null">
        rate_time = #{rateTime,jdbcType=INTEGER},
      </if>
      <if test="rateType != null">
        rate_type = #{rateType,jdbcType=INTEGER},
      </if>
      <if test="rateValue != null">
        rate_value = #{rateValue,jdbcType=VARCHAR},
      </if>
    </set>
    where pay_rate_id = #{payRateId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanPayRate">
    update enyan_pay_rate
    set rate_date = #{rateDate,jdbcType=INTEGER},
      rate_time = #{rateTime,jdbcType=INTEGER},
      rate_type = #{rateType,jdbcType=INTEGER},
      rate_value = #{rateValue,jdbcType=VARCHAR}
    where pay_rate_id = #{payRateId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>