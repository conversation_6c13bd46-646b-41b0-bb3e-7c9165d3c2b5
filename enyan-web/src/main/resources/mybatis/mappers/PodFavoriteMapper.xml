<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aaron.spring.mapper.PodFavoriteMapper" >
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.PodFavorite" >
    <id column="favorite_id" property="favoriteId" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="user_email" property="userEmail" jdbcType="VARCHAR" />
    <result column="podcast_id" property="podcastId" jdbcType="BIGINT" />
    <result column="favorited_at" property="favoritedAt" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <sql id="Base_Column_List" >
    favorite_id, user_id, user_email, podcast_id, favorited_at
  </sql>
  
  <sql id="Example_Where_Clause" >
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" suffix=")" prefixOverrides="and">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  
  <select id="countByExample" parameterType="com.aaron.spring.model.PodFavoriteExample" resultType="java.lang.Long">
    select count(*) from pod_favorite
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aaron.spring.model.PodFavoriteExample">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pod_favorite
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pod_favorite
    where favorite_id = #{favoriteId,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from pod_favorite
    where favorite_id = #{favoriteId,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.PodFavoriteExample" >
    delete from pod_favorite
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  
  <insert id="insert" parameterType="com.aaron.spring.model.PodFavorite" useGeneratedKeys="true" keyProperty="favoriteId">
    insert into pod_favorite (user_id, user_email, podcast_id, favorited_at)
    values (#{userId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, 
      #{podcastId,jdbcType=BIGINT}, #{favoritedAt,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.aaron.spring.model.PodFavorite" useGeneratedKeys="true" keyProperty="favoriteId">
    insert into pod_favorite
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userId != null" >
        user_id,
      </if>
      <if test="podcastId != null" >
        podcast_id,
      </if>
      <if test="favoritedAt != null" >
        favorited_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="podcastId != null" >
        #{podcastId,jdbcType=BIGINT},
      </if>
      <if test="favoritedAt != null" >
        #{favoritedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByExampleSelective" parameterType="map" >
    update pod_favorite
    <set >
      <if test="record.userId != null" >
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.podcastId != null" >
        podcast_id = #{record.podcastId,jdbcType=BIGINT},
      </if>
      <if test="record.favoritedAt != null" >
        favorited_at = #{record.favoritedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByExample" parameterType="map" >
    update pod_favorite
    set user_id = #{record.userId,jdbcType=BIGINT},
      podcast_id = #{record.podcastId,jdbcType=BIGINT},
      favorited_at = #{record.favoritedAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.PodFavorite" >
    update pod_favorite
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="podcastId != null" >
        podcast_id = #{podcastId,jdbcType=BIGINT},
      </if>
      <if test="favoritedAt != null" >
        favorited_at = #{favoritedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where favorite_id = #{favoriteId,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.PodFavorite" >
    update pod_favorite
    set user_id = #{userId,jdbcType=BIGINT},
      user_email = #{userEmail,jdbcType=VARCHAR},
      podcast_id = #{podcastId,jdbcType=BIGINT},
      favorited_at = #{favoritedAt,jdbcType=TIMESTAMP}
    where favorite_id = #{favoriteId,jdbcType=BIGINT}
  </update>
  
  <select id="countByUserId" resultType="int">
    SELECT COUNT(*) 
    FROM pod_favorite
    WHERE user_id = #{userId}
  </select>

  <select id="selectByUserIdAndPodcastId" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM pod_favorite
    WHERE user_id = #{userId}
    AND podcast_id = #{podcastId}
    ORDER BY favorited_at DESC
  </select>
  
  <select id="selectByUserIdAndType" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM pod_favorite
    WHERE user_id = #{userId}
    ORDER BY favorited_at DESC
  </select>

  <insert id="batchUpsert" parameterType="list">
    INSERT INTO pod_favorite 
    (user_id, user_email, podcast_id, favorited_at)
    VALUES 
    <foreach collection="list" item="item" separator=",">
      (#{item.userId}, #{item.userEmail}, #{item.podcastId}, #{item.favoritedAt})
    </foreach>
    ON DUPLICATE KEY UPDATE
    favorited_at = VALUES(favorited_at)
  </insert>

  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>
