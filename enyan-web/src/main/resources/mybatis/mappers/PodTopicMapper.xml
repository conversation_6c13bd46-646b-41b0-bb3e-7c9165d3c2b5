<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.aaron.spring.mapper.PodTopicMapper" >
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.PodTopic" >
    <id column="topic_id" property="topicId" jdbcType="BIGINT" />
    <result column="podcast_id" property="podcastId" jdbcType="BIGINT" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="LONGVARCHAR" />
    <result column="display_order" property="displayOrder" jdbcType="INTEGER" />
    <result column="episode_count" property="episodeCount" jdbcType="INTEGER" />
    <result column="is_deleted" property="isDeleted" jdbcType="INTEGER" />
    <result column="created_at" property="createdAt" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  
  <sql id="Base_Column_List" >
    topic_id, podcast_id, title, description, display_order, episode_count, is_deleted, created_at
  </sql>
  
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.aaron.spring.model.PodTopicExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from pod_topic
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  
  <select id="countByExample" parameterType="com.aaron.spring.model.PodTopicExample" resultType="java.lang.Long">
    select count(*) from pod_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pod_topic
    where topic_id = #{topicId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from pod_topic
    where topic_id = #{topicId,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.PodTopicExample" >
    delete from pod_topic
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  
  <insert id="insert" parameterType="com.aaron.spring.model.PodTopic" >
    insert into pod_topic (topic_id, podcast_id, title, 
      description, display_order, episode_count, 
      is_deleted, created_at)
    values (#{topicId,jdbcType=BIGINT}, #{podcastId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, 
      #{description,jdbcType=LONGVARCHAR}, #{displayOrder,jdbcType=INTEGER}, #{episodeCount,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=INTEGER}, #{createdAt,jdbcType=TIMESTAMP})
  </insert>
  
  <insert id="insertSelective" parameterType="com.aaron.spring.model.PodTopic" >
    insert into pod_topic
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="topicId != null" >
        topic_id,
      </if>
      <if test="podcastId != null" >
        podcast_id,
      </if>
      <if test="title != null" >
        title,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="displayOrder != null" >
        display_order,
      </if>
      <if test="episodeCount != null" >
        episode_count,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createdAt != null" >
        created_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="topicId != null" >
        #{topicId,jdbcType=BIGINT},
      </if>
      <if test="podcastId != null" >
        #{podcastId,jdbcType=BIGINT},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="displayOrder != null" >
        #{displayOrder,jdbcType=INTEGER},
      </if>
      <if test="episodeCount != null" >
        #{episodeCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null" >
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByExampleSelective" parameterType="map" >
    update pod_topic
    <set >
      <if test="record.topicId != null" >
        topic_id = #{record.topicId,jdbcType=BIGINT},
      </if>
      <if test="record.podcastId != null" >
        podcast_id = #{record.podcastId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null" >
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.displayOrder != null" >
        display_order = #{record.displayOrder,jdbcType=INTEGER},
      </if>
      <if test="record.episodeCount != null" >
        episode_count = #{record.episodeCount,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.createdAt != null" >
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByExample" parameterType="map" >
    update pod_topic
    set topic_id = #{record.topicId,jdbcType=BIGINT},
      podcast_id = #{record.podcastId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=LONGVARCHAR},
      display_order = #{record.displayOrder,jdbcType=INTEGER},
      episode_count = #{record.episodeCount,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.PodTopic" >
    update pod_topic
    <set >
      <if test="podcastId != null" >
        podcast_id = #{podcastId,jdbcType=BIGINT},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="displayOrder != null" >
        display_order = #{displayOrder,jdbcType=INTEGER},
      </if>
      <if test="episodeCount != null" >
        episode_count = #{episodeCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null" >
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where topic_id = #{topicId,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.PodTopic" >
    update pod_topic
    set podcast_id = #{podcastId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      description = #{description,jdbcType=LONGVARCHAR},
      display_order = #{displayOrder,jdbcType=INTEGER},
      episode_count = #{episodeCount,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP}
    where topic_id = #{topicId,jdbcType=BIGINT}
  </update>

  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>
