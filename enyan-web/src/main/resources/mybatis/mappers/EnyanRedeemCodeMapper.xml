<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanRedeemCodeMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanRedeemCode">
    <id column="redeem_code_id" jdbcType="BIGINT" property="redeemCodeId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="end_at" jdbcType="TIMESTAMP" property="endAt" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanRedeemCode">
    <result column="note" jdbcType="LONGVARCHAR" property="note" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    redeem_code_id, user_email, code, type, status, end_at, create_time
  </sql>
  <sql id="Blob_Column_List">
    note
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.EnyanRedeemCodeExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_redeem_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanRedeemCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_redeem_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_redeem_code
    where redeem_code_id = #{redeemCodeId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_redeem_code
    where redeem_code_id = #{redeemCodeId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanRedeemCodeExample">
    delete from enyan_redeem_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanRedeemCode">
    <selectKey keyProperty="redeemCodeId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_redeem_code (user_email, code, type, 
      status, end_at, create_time, 
      note)
    values (#{userEmail,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{endAt,jdbcType=TIMESTAMP}, #{createTime,jdbcType=VARCHAR}, 
      #{note,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanRedeemCode">
    <selectKey keyProperty="redeemCodeId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into enyan_redeem_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="endAt != null">
        end_at,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="note != null">
        note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="endAt != null">
        #{endAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        #{note,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanRedeemCodeExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_redeem_code
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_redeem_code
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_redeem_code
    <set>
      <if test="record.redeemCodeId != null">
        redeem_code_id = #{record.redeemCodeId,jdbcType=BIGINT},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.endAt != null">
        end_at = #{record.endAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=VARCHAR},
      </if>
      <if test="record.note != null">
        note = #{record.note,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update enyan_redeem_code
    set redeem_code_id = #{record.redeemCodeId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      end_at = #{record.endAt,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=VARCHAR},
      note = #{record.note,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_redeem_code
    set redeem_code_id = #{record.redeemCodeId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      end_at = #{record.endAt,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanRedeemCode">
    update enyan_redeem_code
    <set>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="endAt != null">
        end_at = #{endAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where redeem_code_id = #{redeemCodeId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.EnyanRedeemCode">
    update enyan_redeem_code
    set user_email = #{userEmail,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      end_at = #{endAt,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=VARCHAR},
      note = #{note,jdbcType=LONGVARCHAR}
    where redeem_code_id = #{redeemCodeId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanRedeemCode">
    update enyan_redeem_code
    set user_email = #{userEmail,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      end_at = #{endAt,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=VARCHAR}
    where redeem_code_id = #{redeemCodeId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>