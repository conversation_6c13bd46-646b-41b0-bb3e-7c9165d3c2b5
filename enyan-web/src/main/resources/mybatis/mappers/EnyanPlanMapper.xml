<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aaron.spring.mapper.EnyanPlanMapper">
  <resultMap id="BaseResultMap" type="com.aaron.spring.model.EnyanPlan">
    <id column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="user_email" jdbcType="VARCHAR" property="userEmail" />
    <result column="book_id" jdbcType="BIGINT" property="bookId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="start_from" jdbcType="BIGINT" property="startFrom" />
    <result column="has_buy" jdbcType="INTEGER" property="hasBuy" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.aaron.spring.model.EnyanPlan">
    <result column="finished_bit_set" jdbcType="LONGVARBINARY" property="finishedBitSet" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    plan_id, user_email, book_id, name, start_from, has_buy, is_deleted, update_time
  </sql>
  <sql id="Blob_Column_List">
    finished_bit_set
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.aaron.spring.model.EnyanPlanExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.aaron.spring.model.EnyanPlanExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from enyan_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <include refid="MySqlPaginationSuffix" />
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from enyan_plan
    where plan_id = #{planId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enyan_plan
    where plan_id = #{planId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.aaron.spring.model.EnyanPlanExample">
    delete from enyan_plan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.aaron.spring.model.EnyanPlan">
    insert into enyan_plan (plan_id, user_email, book_id, 
      name, start_from, has_buy, 
      is_deleted, update_time, finished_bit_set
      )
    values (#{planId,jdbcType=BIGINT}, #{userEmail,jdbcType=VARCHAR}, #{bookId,jdbcType=BIGINT}, 
      #{name,jdbcType=VARCHAR}, #{startFrom,jdbcType=BIGINT}, #{hasBuy,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=INTEGER}, #{updateTime,jdbcType=BIGINT}, #{finishedBitSet,jdbcType=LONGVARBINARY}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.aaron.spring.model.EnyanPlan">
    insert into enyan_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="planId != null">
        plan_id,
      </if>
      <if test="userEmail != null">
        user_email,
      </if>
      <if test="bookId != null">
        book_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="startFrom != null">
        start_from,
      </if>
      <if test="hasBuy != null">
        has_buy,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="finishedBitSet != null">
        finished_bit_set,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="userEmail != null">
        #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        #{bookId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="startFrom != null">
        #{startFrom,jdbcType=BIGINT},
      </if>
      <if test="hasBuy != null">
        #{hasBuy,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="finishedBitSet != null">
        #{finishedBitSet,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.aaron.spring.model.EnyanPlanExample" resultType="java.lang.Long">
    SELECT COUNT(1) FROM
    <if test="distinct">
      (SELECT
          DISTINCT
          <include refid="Base_Column_List"/>
          from enyan_plan
          <if test="_parameter != null">
              <include refid="Example_Where_Clause"/>
          </if>) AS COUNT_SQL
    </if>
    <if test="!distinct">
      
          enyan_plan
          <if test="_parameter != null" >
              <include refid="Example_Where_Clause" />
          </if>

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update enyan_plan
    <set>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.userEmail != null">
        user_email = #{record.userEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.bookId != null">
        book_id = #{record.bookId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.startFrom != null">
        start_from = #{record.startFrom,jdbcType=BIGINT},
      </if>
      <if test="record.hasBuy != null">
        has_buy = #{record.hasBuy,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.finishedBitSet != null">
        finished_bit_set = #{record.finishedBitSet,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update enyan_plan
    set plan_id = #{record.planId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      start_from = #{record.startFrom,jdbcType=BIGINT},
      has_buy = #{record.hasBuy,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=BIGINT},
      finished_bit_set = #{record.finishedBitSet,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update enyan_plan
    set plan_id = #{record.planId,jdbcType=BIGINT},
      user_email = #{record.userEmail,jdbcType=VARCHAR},
      book_id = #{record.bookId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      start_from = #{record.startFrom,jdbcType=BIGINT},
      has_buy = #{record.hasBuy,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.aaron.spring.model.EnyanPlan">
    update enyan_plan
    <set>
      <if test="userEmail != null">
        user_email = #{userEmail,jdbcType=VARCHAR},
      </if>
      <if test="bookId != null">
        book_id = #{bookId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="startFrom != null">
        start_from = #{startFrom,jdbcType=BIGINT},
      </if>
      <if test="hasBuy != null">
        has_buy = #{hasBuy,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="finishedBitSet != null">
        finished_bit_set = #{finishedBitSet,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where plan_id = #{planId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.aaron.spring.model.EnyanPlan">
    update enyan_plan
    set user_email = #{userEmail,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      start_from = #{startFrom,jdbcType=BIGINT},
      has_buy = #{hasBuy,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=BIGINT},
      finished_bit_set = #{finishedBitSet,jdbcType=LONGVARBINARY}
    where plan_id = #{planId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.aaron.spring.model.EnyanPlan">
    update enyan_plan
    set user_email = #{userEmail,jdbcType=VARCHAR},
      book_id = #{bookId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      start_from = #{startFrom,jdbcType=BIGINT},
      has_buy = #{hasBuy,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=BIGINT}
    where plan_id = #{planId,jdbcType=BIGINT}
  </update>
  <sql id="MySqlPaginationSuffix">
    <if test="page != null">
      <![CDATA[ LIMIT #{page.pageSize} OFFSET #{page.recordIndex}]]>
    </if>
  </sql>
</mapper>