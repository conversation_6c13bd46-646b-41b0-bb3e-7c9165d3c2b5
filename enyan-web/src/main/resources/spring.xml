<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
    http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.2.xsd
    http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
	http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.2.xsd http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">

    <!--<context:property-placeholder location="classpath:config.properties" />-->
    <context:property-placeholder location="classpath:*.properties" ignore-unresolvable="true" />

    <!--启用spring的一些annotation -->
    <context:annotation-config/>

	<context:component-scan base-package="com.aaron.spring" >
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<context:component-scan base-package="com.aaron.security" >
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<task:annotation-driven /> <!-- 定时器开关-->

    <context:component-scan base-package="com.aaron.spring.task" >
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
    </context:component-scan>

    <context:component-scan base-package="com.aaron.data" />

	<!-- Spring Email Sender Bean Configuration -->
	<bean id="emailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="host" value="${spring.mail.host}" />
		<property name="port" value="${spring.mail.port}" />
		<property name="username" value="${spring.mail.username}" />
		<property name="password" value="${spring.mail.password}" />
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.auth">${spring.mail.properties.mail.smtp.auth}</prop>
				<prop key="mail.debug">${spring.mail.properties.mail.debug}</prop>
				<prop key="mail.transport.protocol">smtp</prop>
				<prop key="mail.smtp.socketFactory.class">javax.net.ssl.SSLSocketFactory</prop>
				<prop key="mail.smtp.socketFactory.port">465</prop>
				<prop key="mail.smtp.starttls.enable">${spring.mail.properties.mail.smtp.starttls.enable}</prop>
				<prop key="mail.smtp.ssl.trust">smtp.gmail.com</prop>
			</props>
		</property>
	</bean>
    <bean id="freemarkerConfig"
          class="org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer">
        <property name="templateLoaderPath" value="/WEB-INF/classes/templates/"/>
    </bean>

	<bean id="messageSource" class="org.springframework.context.support.ResourceBundleMessageSource">

		<!-- 其中basename用来指定properties文件的通用名
                 如实例中的messages_en.properties,messages_ja.properties通用名都是messages
         -->
		<property name="basename" value="messages" />
		<property name="defaultEncoding" value="UTF-8"/>
		<property name="useCodeAsDefaultMessage" value="true" />
	</bean>


    <!-- 配置redis池，依次为最大实例数，最大空闲实例数，(创建实例时)最大等待时间，(创建实例时)是否验证 -->
    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="500" />
        <property name="maxIdle" value="50" />
        <property name="minIdle" value="10" />
        <property name="maxWaitMillis" value="2000" />
        <property name="testOnBorrow" value="true" />
    </bean>

	<!-- 对模型视图名称的解析，即在模型视图名称添加前后缀(如果最后一个还是表示文件夹,则最后的斜杠不要漏了) 使用JSP-->
	<!-- 默认的视图解析器 在上边的解析错误时使用 (默认使用html)- -->
	<bean id="defaultViewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
		<property name="prefix" value="/WEB-INF/views/"/><!--设置JSP文件的目录位置-->
		<property name="order" value="10" />
		<property name="suffix" value=".jsp"/>
	</bean>

	<!--
        https://www.thymeleaf.org/doc/tutorials/3.0/thymeleafspring.html
        SpringResourceTemplateResolver automatically integrates with Spring's own -->
	<!-- resource resolution infrastructure, which is highly recommended.          -->
	<bean id="templateResolver"
		  class="org.thymeleaf.spring5.templateresolver.SpringResourceTemplateResolver">
		<property name="prefix" value="/WEB-INF/templates/" />
		<property name="suffix" value=".html" />
		<!-- HTML is the default value, added here for the sake of clarity.          -->
		<property name="templateMode" value="HTML" />
		<!-- Template cache is true by default. Set to false if you want             -->
		<!-- templates to be automatically updated when modified.                    -->
		<property name="cacheable" value="true" />
	</bean>
	<!-- SpringTemplateEngine automatically applies SpringStandardDialect and      -->
	<!-- enables Spring's own MessageSource message resolution mechanisms.         -->
	<bean id="springTemplateEngine"
		  class="org.thymeleaf.spring5.SpringTemplateEngine">
		<property name="templateResolver" ref="templateResolver" />
		<!-- Enabling the SpringEL compiler with Spring 4.2.4 or newer can speed up  -->
		<!-- execution in most scenarios, but might be incompatible with specific    -->
		<!-- cases when expressions in one template are reused across different data -->
		<!-- ypes, so this flag is "false" by default for safer backwards            -->
		<!-- compatibility.                                                          -->
		<property name="enableSpringELCompiler" value="true" />
	</bean>
	<bean class="org.thymeleaf.spring5.view.ThymeleafViewResolver">
		<property name="templateEngine" ref="springTemplateEngine" />
		<!-- NOTE 'order' and 'viewNames' are optional -->
		<property name="order" value="9" />
		<property name="characterEncoding" value="UTF-8"/>
		<property name="viewNames" value="*.html,*.xhtml" />
		<property name="excludedViewNames" value="*.jsp"/>
	</bean>

	<!-- jedisConnectionFactory -->
	<!--<bean id="jedisConnectionFactory"
		  class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory"
          p:host-name="${redis.host}" p:port="${redis.port}">
        <constructor-arg name="poolConfig" ref="jedisPoolConfig" />
    </bean>-->

    <!--<bean id="lettuceConnectionFactory"
          class="org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory"
          p:host-name="${redis.host}" p:port="${redis.port}"/>-->

    <!--<bean id="jredisConnectionFactory"
          class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <constructor-arg>
            <bean class="org.springframework.data.redis.connection.jredis.DefaultJredisPool">
                <constructor-arg value="localhost" />
                <constructor-arg value="6379" />
            </bean>
        </constructor-arg>
    </bean>-->

    <!-- 配置Sentinel，spring-data-redis 1.5.0开始支持此配置 -->
    <!--<bean id="sentinelConfig"
          class="org.springframework.data.redis.connection.RedisSentinelConfiguration">
        <constructor-arg name="master" value="mymaster" />
        <constructor-arg name="sentinelHostAndPorts">
            <set>
                <value>${redis.host}:${redis.port}</value>
            </set>
        </constructor-arg>
    </bean>-->

    <!-- redis连接配置，依次为数据库，是否使用池，(usePool=true时)redis的池配置 -->
    <!--<bean id="jedisConnectionFactory"
          class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory"
        >
        <property name="database" value="0"></property>
        <property name="usePool" value="true" />
        <constructor-arg name="sentinelConfig" ref="sentinelConfig" />
        <constructor-arg name="poolConfig" ref="jedisPoolConfig" />
    </bean>-->

	<!-- redisTemplate -->
	<!--序列化方式 建议key/hashKey采用StringRedisSerializer。 -->
	<!--<bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate"
          p:connection-factory-ref="jedisConnectionFactory" >
        <property name="keySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer" />
        </property>
        <property name="hashKeySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer" />
        </property>
        <property name="valueSerializer">
            <bean class="org.springframework.data.redis.serializer.JdkSerializationRedisSerializer" />
        </property>
        <property name="hashValueSerializer">
            <bean class="org.springframework.data.redis.serializer.JdkSerializationRedisSerializer" />
        </property>
    </bean>

    <bean id="cacheManager" class="org.springframework.data.redis.cache.RedisCacheManager">
        <constructor-arg name="redisOperations" ref="redisTemplate" />
        <property name="defaultExpiration" value="${redis.cache.DefaultExpiration}"/>
    </bean>
    &lt;!&ndash; 开启缓存 &ndash;&gt;
    <cache:annotation-driven/>-->
</beans>