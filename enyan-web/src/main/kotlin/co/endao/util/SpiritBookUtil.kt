package co.endao.util

import co.endao.devotion.DevotionBook
import co.endao.epub.DataSource
import co.endao.epub.decompress
import com.aaron.spring.common.SystemUtil
import java.io.File
import java.io.FileOutputStream
import java.util.zip.ZipFile

/**
 *@Author: <PERSON>
 *@Description:
 *@Date: Created in  11/18/20
 *@Modified By:
 */

class SpiritBookUtil private constructor() {
    companion object {
        private var instance: SpiritBookUtil? = null
            get() {
                if (field == null) {
                    field = SpiritBookUtil()
                }
                return field
            }
        fun get(): SpiritBookUtil{
            //细心的小伙伴肯定发现了，这里不用getInstance作为为方法名，是因为在伴生对象声明时，内部已有getInstance方法，所以只能取其他名字
            return instance!!
        }
    }

    /**
     * <p>解压缩epub文件</p>
     * @since : 11/18/20
     */
    fun decompressBookInWeb(epubName: String, bookId: Long){
        val epubPath = SystemUtil.getEpubOfSpiritBaseDir() + epubName
        val destDir = SystemUtil.getEpubOfSpiritBaseDir() + this.getEpubCompressName(epubName)
        var cssDir = SystemUtil.getEpubStyleDir() + bookId + "/"

        this.decompressBookByPath(epubPath,destDir,cssDir)
    }

    /**
     * <p>根据路径解压书籍</p>
     * @param null
     * @return: null
     * @since : 11/19/20
     */
    fun decompressBookByPath(epubPath: String, destDir: String, cssDir: String){
        val out = File(epubPath)
        if(!out.isFile){
            //context.assets.open("圣经这场戏-灵修.epub").copyTo(FileOutputStream(out))
            return
        }
        val zip = ZipFile(out)

        //val dir = File(destDir)

        zip.decompress(destDir)
        //print(destFolderName)
        zip.decompressSkip(cssDir)
    }

    /**
     * <p>网站上根据epub名称和天数获取路径</p>
     * @param null
     * @return: null
     * @since : 11/19/20
     */
    fun getPathForDayInWeb(epubName: String, day: Int): String?{
        val compressName = this.getEpubCompressName(epubName)
        val epubPath = SystemUtil.getEpubOfSpiritBaseDir() + compressName
        val book = DevotionBook(DataSource.Folder(epubPath))
        val dayPath = book.getPathForDay(day)
        //return dayPath?:null
        if (null != dayPath){
            //return epubPath + "/" + dayPath
            return "$epubPath/$dayPath"
        }
        return null
    }

    /**
     * <p>网站上根据epub解压的路径和天数获取路径</p>
     * @param null
     * @return: null
     * @since : 11/19/20
     */
    fun getPathForDayByPath(epubCompressPath: String, day: Int): String?{
        val book = DevotionBook(DataSource.Folder(epubCompressPath))
        return book.getPathForDay(day)
    }

    /**
     * <p>根据epub名称获取前缀</p>
     * @param null
     * @return: null
     * @since : 11/19/20
     */
    fun getEpubCompressName(epubName: String): String{
        return  epubName.split(".")[0]
    }

    fun ok(){
        println("ok....")
    }

    fun ZipFile.decompressSkip(p:String){
        val dir = File(p)
        dir.mkdirs()
        for(it in entries())    {
            if (it.name.lastIndexOf(".xhtml")!=-1
                    || it.name.lastIndexOf(".ncx")!=-1
                    || it.name.lastIndexOf(".opf")!=-1){
                continue
            }
            val strm = getInputStream(it) ?: continue
            val out = "$p/${it.name}"
            val parentFile = File(out).parentFile
            if (parentFile.name == "Text"){
                continue
            }
            parentFile.mkdirs()
            strm.copyTo(FileOutputStream(out))
        }
        Runtime.getRuntime().exec("chmod 777 -R $p")
    }

    //val out = File(context.getExternalFilesDir(null), "devotion.epub")
    /*
    增加了epub解压函数(ZipFile.decompress)，服务器上可以解压后供app获取资源。
    通过DevotionBook.getPathForDay可获取zip中的第x天页面相对路径（可以拼接后返回给spirit/read接口），这样的话你就不需要处理相对路径的问题。

    fun loadData(){
        val out = File(context.getExternalFilesDir(null), "devotion.epub")
        if(!out.isFile){
            context.assets.open("圣经这场戏-灵修.epub").copyTo(FileOutputStream(out))
        }

        val zip = ZipFile(out)

        val dir = File(context.getExternalFilesDir(null), "devotion")

        zip.decompress(dir.absolutePath)

        book = DevotionBook(DataSource.Folder(dir.absolutePath))
        println(book.devotionItems.size)
    }*/
}

class SingletonDemo private constructor() {
    companion object {
        private var instance: SingletonDemo? = null
            get() {
                if (field == null) {
                    field = SingletonDemo()
                }
                return field
            }
        fun get(): SingletonDemo{
            //细心的小伙伴肯定发现了，这里不用getInstance作为为方法名，是因为在伴生对象声明时，内部已有getInstance方法，所以只能取其他名字
            return instance!!
        }
    }

    fun ok(){

    }
}