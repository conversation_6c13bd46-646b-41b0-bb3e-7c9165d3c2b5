package com.aaron.spring.common;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multiset;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/7/23
 * @Modified By:
 */
public class MultimapTest {
	@Test
	public void testMap(){
		String str ="SALES:0,SALE_PRODUCTS:1,EXPENSES:2,EXPENSES_ITEMS:3";
		HashMap<String, Integer> map = (HashMap<String, Integer>) Arrays.asList(str.split(",")).stream().map(s -> s.split(":")).collect(Collectors.toMap(e -> e[0], e -> Integer.parseInt(e[1])));
		System.out.println("MapTest.testMap="+map);
	}
	@Test
	public void testMutiMap(){
		Multimap<Integer, Integer> map = HashMultimap.create();

		map.put(1, 2);
		map.put(1, 2);
		map.put(1, 3);
		map.put(1, 4);
		map.put(2, 3);
		map.put(3, 3);
		map.put(4, 3);
		map.put(5, 3);
		System.out.println(map);

		//判断集合中是否存在key-value为指定值得元素
		System.out.println(map.containsEntry(1, 2));
		System.out.println(map.containsEntry(1, 6));
		//获取key为1的value集合
		Collection<Integer> list = map.get(1);
		System.out.println(list);
		//返回集合中所有key的集合，重复的key将会用key * num的方式来表示
		Multiset<Integer> set = map.keys();
		System.out.println(set);
		//返回集合中所有不重复的key的集合
		Set<Integer> kset = map.keySet();
		System.out.println(kset);

		Collection<Integer> coll = map.replaceValues(1, Arrays.asList(1,7,8,9,10));
		System.out.println(coll);
		System.out.println(map);
	}
}
