package com.aaron.spring.common;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 *
 * @Date: Created in  2018/5/30
 * @Modified By:
 */
public class HashSetTest {
    @Test
    public void testHashSetLong(){
       Set<Long> set = new HashSet<>(Arrays.asList(1L,2L,3L));
       String jsonString = JSON.toJSONString(set);
        System.out.println(jsonString);

        HashSet<Long> hashSet = JSON.parseObject(jsonString, HashSet.class);
        if (hashSet.contains(1L)){
            System.out.println("1L is true");
        }else {
            System.out.println("1L is not true");
        }
        if (hashSet.contains(1)){
            System.out.println("1L is true");
        }else {
            System.out.println("1L is not true");
        }
        System.out.println(hashSet);
    }

    @Test
    public void testHashSetLongToArray(){
        Set<Long> set = new HashSet<>(Arrays.asList(1L,2L,3L));
        String jsonString = JSON.toJSONString(set);
        System.out.println(jsonString);

        List<Long> list = set.stream().collect(Collectors.toList());
        Long[] idsLong = list.toArray(new Long[0]);
        System.out.println("list="+list);
        System.out.println("idsLong="+idsLong.length);
        List<String> listString = set.stream().map(value->String.valueOf(value)).collect(Collectors.toList());
        String[] idsString = listString.toArray(new String[0]);
        System.out.println(idsString);
    }

    /**
     *    使用FastJSON将字符串类型的JSON对象转为泛型对象类型，需要特殊处理下才能正常转换。
     *
     * json使用的是fastjson，json转换对象的时候，如果对象中存在泛型对象，则需要特殊处理下才能正常转换。
     *
     *      使用的是fastjson中的TypeReference来进行转换：
     *
     * A<B<C>> resultObj =JSON.parseObject("转换json",new TypeReference<A<B<C>>>(){});
     *
     * 其中A为接收类型,B为A的泛型类,C为B的泛型类
     * */
    @Test
    public void testJSONHashSetLongToArray(){
        Set<Long> set = new HashSet<>(Arrays.asList(1L,2L,3L));
        String jsonString = JSON.toJSONString(set);
        System.out.println(jsonString);

        HashSet<Long> hashSet = JSON.parseObject(jsonString, new TypeReference<HashSet<Long>>(){});

        List<Long> list = hashSet.stream().collect(Collectors.toList());
        Long[] idsLong = list.toArray(new Long[0]);
        System.out.println("list="+list);
        System.out.println("idsLong="+idsLong.length);
        List<String> listString = set.stream().map(String::valueOf).collect(Collectors.toList());
        String[] idsString = listString.toArray(new String[0]);
        System.out.println(idsString);
    }

    @Test
    public void testHashSetString(){
        Set<String> set = new HashSet<>(Arrays.asList("1","2","3"));
        String jsonString = JSON.toJSONString(set);
        System.out.println(jsonString);

        HashSet<String> hashSet = JSON.parseObject(jsonString, HashSet.class);
        if (hashSet.contains("1")){
            System.out.println("1L is true");
        }else {
            System.out.println("1L is not true");
        }
        System.out.println(hashSet);

        String s = String.join(",", set);
        System.out.println("s="+s);
    }
}
