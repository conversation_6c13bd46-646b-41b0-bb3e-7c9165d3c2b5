package com.aaron.spring.common;

import com.aaron.a4j.util.AaronDateUtils;
import com.aaron.api.constant.InterfaceContant;
import com.aaron.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.junit.Test;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 *
 *
 * @Date: Created in  2018/5/30
 * @Modified By:
 */
public class RegexpTest {
    @Test
    public void test1(){
        String text = "*aaaa";
        System.out.println(StringUtils.indexOf(text, "b" ));
        if (StringUtils.indexOf(text, "*" ) != -1 || StringUtils.indexOf(text, "$" ) != -1){
            System.out.println("1 = has");
        }else{
            System.out.println("1 = no");
        }

        if (StringUtils.indexOf(text, "$" ) != -1){
            System.out.println("2 = has");
        }else{
            System.out.println("2 = no");
        }
    }

    public static String stringFilter(String str) throws PatternSyntaxException {
        // 只允许字母和数字 // String regEx ="[^a-zA-Z0-9]";
        // 清除掉所有特殊字符
        String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    public static void main(String[] args) {
        String srcText = "*adCVs*34_a _09_b5*[/435^*&城池()^$$&*).{}+.|.)%%*(*.中国}34{45[]12.fd'*&999下面是中文的字符￥……{}【】。，；’“‘”？";
        String text = RegexpTest.stringFilter(srcText);
        System.out.println(text);
    }
}
