package com.aaron.spring.common;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.Collection;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/6/28
 * @Modified By:
 */
public class FileScanThread implements Runnable{
	private Thread t;
	private String name = "";

	public FileScanThread(String name) {
		this.name = name;
	}

	@Override
	public void run() {
		File file = new File(name);
		Collection<File> list =  FileUtils.listFiles(file, null, true);
	}

	public void start () {
		System.out.println("Starting " +  name );
		if (t == null) {
			t = new Thread (this, name);
			t.start ();
		}
	}

	public static void main(String[] args) {
		String basePath = "/Volumes/Expansion/2T/文字";
		Collection<File> list =  FileUtils.listFiles(new File(basePath), null, false);
		for (File file:list){
			FileScanThread thread = new FileScanThread(file.getPath());
			thread.start();
		}
	}
}
