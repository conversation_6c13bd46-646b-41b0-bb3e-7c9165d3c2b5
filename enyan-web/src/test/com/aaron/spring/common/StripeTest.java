package com.aaron.spring.common;

import com.aaron.editionguard.dto.EDTransactionDTO;
import com.aaron.spring.model.OrderPayInfo;
import com.alibaba.fastjson2.JSONObject;
//import com.mashape.unirest.http.HttpResponse;
//import com.mashape.unirest.http.Unirest;
import com.stripe.Stripe;
import com.stripe.config.StripeConfig;
import com.stripe.exception.*;
import com.stripe.model.Charge;
import com.stripe.model.Token;
import com.stripe.net.RequestOptions;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static com.aaron.spring.common.Constant.EDITION_GUARD_TOKEN;

/**
 *
 *
 * @Date: Created in  2020-03-25
 * @Modified By:
 */
public class StripeTest {
    /*
    @Test
    public void testTransaction()throws Exception{
        String url = "https://app.editionguard.com/api/v2/transaction/4Oft55QVQV";
        HttpResponse<String> response = Unirest.get(url)
                .header("authorization", EDITION_GUARD_TOKEN)
                .asString();

        //{"id":"4Oft55QVQV","created":"2018-02-02T01:59:14Z","modified":"2018-02-02T01:59:14Z","resource_id":"urn:uuid:8b9fc9c0-304f-4471-ad99-6393a17b69df","is_fulfilled":false,"download_link":"https://app.editionguard.com/fulfillment/4Oft55QVQV","show_instructions":false,"external_id":"","watermark_place_begin":true,"watermark_place_end":false,"watermark_place_random":false,"watermark_place_random_count":null}
        //Download_link: https://app.editionguard.com/fulfillment/4Oft55QVQV
        String json = response.getBody();

        System.out.println(json);

        EDTransactionDTO edTransactionDTO = JSONObject.parseObject(json,EDTransactionDTO.class);
        System.out.println("Download_link:"+edTransactionDTO.getDownload_link());

        //System.out.println(response.getBody());
    }*/

    @Test
    public void testToken() throws Exception{
        Stripe.apiKey = "sk_test_4eC39HqLyjWDarjtT1zdp7dc";

        Map<String, Object> card = new HashMap<>();
        card.put("number", "****************");
        card.put("exp_month", 3);
        card.put("exp_year", 2021);
        card.put("cvc", "314");
        Map<String, Object> params = new HashMap<>();
        params.put("card", card);
        params.put("email","myEmail");

        try {
            Token token = Token.create(params);
            System.out.println(token);
        } catch (StripeException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testOrderInfo() throws Exception{
        /*
        RequestOptions requestOptions = (new RequestOptions.RequestOptionsBuilder()).setApiKey(StripeConfig.SECRET_KEY).build();
        Map<String, Object> chargeMap = new HashMap<>();
        //付款金额，必填

        BigDecimal fee = order.getOrderTotal().multiply(new BigDecimal("100"));
        int total_fee = fee.intValue();

        //chargeMap.put("amount", 400);//100分
        chargeMap.put("amount", total_fee);//100分
        chargeMap.put("currency", "hkd");//cny usd
        chargeMap.put("source", token); // obtained via Stripe.js
        chargeMap.put("description",order.getOrderNum());
        try {
            Charge charge = Charge.create(chargeMap, requestOptions);
            //Card card = (Card) charge.getSource();
            //System.out.println("country:"+card.getAccount()+card.getCustomer()+card.getCountry());
            result.setResult("/shop/scr");
            result.setSuccessMessage("支付成功");
            OrderPayInfo payInfo = new OrderPayInfo();
            payInfo.addStripe(charge,true);
            this.paySuccess(order,payInfo,request);
            return result;
        } catch (StripeException e) {
            e.printStackTrace();
            //result.addErrorMessage("支付失败");
            //return result;
        }*/
    }

    @Test
    public void testNum() throws Exception{
        for (int i = 0; i < 200; i++) {
            if (i == 0){
                continue;
            }

            if (i%2==1){
                System.out.print(",");
                System.out.print(i);
            }

        }
    }
}
