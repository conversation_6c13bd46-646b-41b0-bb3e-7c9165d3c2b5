package com.aaron.spring.common;

import org.apache.commons.io.FileUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  11/20/20
 * @Modified By:
 */
public class HtmlUtils {
    /*
    <div id="post_list" class="post-list">
              <article class="post-item" data-post-id="14011154">
                <section class="post-item-body">
                  <div class="post-item-text">
                    <a class="post-item-title" href="https://www.cnblogs.com/xionglaichuangyichuang/p/14011154.html"
                      target="_blank">C# 简易的串口监视上位机实现</a>
                    <p class="post-item-summary">
                      <img src="https://pic.cnblogs.com/face/2139213/20200827231802.png" class="avatar">
                      实现上位机和下位机之间的通信，通常使用的是串口通信，接下来实现一个通过上位机和串口调试助手来完成串口通信测试。
                      首先创建一个WInfrom窗体应用工程文件，创建过程可参考https://www.cnblogs.com/xionglaichuangyichuang/p/13734179.html； 在 ...
                    </p>
                  </div>
                  <footer class="post-item-foot">
                    <a href="https://www.cnblogs.com/xionglaichuangyichuang/"
                      class="post-item-author"><span>熊来闯一闯</span></a>
                    <span class="post-meta-item">
                      <span>2020-11-20 15:31</span>
                    </span>
                    <a class="post-meta-item btn " href="javascript:void(0)"
                      onclick="DiggPost('xionglaichuangyichuang', 14011154, 626788, 1);return false;">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_digg"></use>
                      </svg>
                      <span id="digg_count_14011154">0</span>
                    </a>
                    <a class="post-meta-item btn"
                      href="https://www.cnblogs.com/xionglaichuangyichuang/p/14011154.html#commentform">
                      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_comment"></use>
                      </svg>
                      <span>0</span>
                    </a>
                    <a class="post-meta-item btn" href="https://www.cnblogs.com/xionglaichuangyichuang/p/14011154.html">
                      <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                        <use xlink:href="#icon_views"></use>
                      </svg>
                      <span>99</span>
                    </a>
                    <span id="digg_tip_14011154" class="digg-tip" style="color: red"></span>
                  </footer>
                </section>
                <figure>
                </figure>
              </article>
        </div>
    * */
    @Test
    public void testBlog() throws IOException {
        String htmlPath = "/Users/<USER>/Documents/Test/epub/blog.html";
        File file = new File(htmlPath);
        String html = FileUtils.readFileToString(file, Charset.defaultCharset());

        //System.out.println(html);

        Document document = Jsoup.parse(html);
        //像js一样，通过标签获取title
        System.out.println(document.getElementsByTag("title").first());
        //像js一样，通过id 获取文章列表元素对象
        Element postList = document.getElementById("post_list");
        //像js一样，通过class 获取列表下的所有博客
        Elements postItems = postList.getElementsByClass("post-item");
        //循环处理每篇博客
        for (Element postItem : postItems) {
            //像jquery选择器一样，获取文章标题元素
            Elements titleEle = postItem.select(".post-item-body a[class='post-item-title']");
            System.out.println("class:" + titleEle.attr("class"));
            System.out.println("target:" + titleEle.attr("target"));
            System.out.println("文章标题:" + titleEle.text());
            System.out.println("文章地址:" + titleEle.attr("href"));
            //像jquery选择器一样，获取文章作者元素
            Elements footEle = postItem.select(".post-item-foot a[class='post-item-author']");
            System.out.println("文章作者:" + footEle.text());;
            System.out.println("作者主页:" + footEle.attr("href"));
            System.out.println("*********************************");
        }
    }

    @Test
    public void testGetLink() throws IOException {
        String htmlPath = "/Users/<USER>/Documents/Test/epub/blog.html";
        File file = new File(htmlPath);
        String html = FileUtils.readFileToString(file, Charset.defaultCharset());

        //System.out.println(html);

        Document document = Jsoup.parse(html);
        //像js一样，通过标签获取title
        System.out.println(document.getElementsByTag("title").first());

        Elements links = document.getElementsByTag("link");
        for (Element element : links){
            System.out.println("链接:"+element.attr("href"));
            String href = element.attr("href");
            int index = href.lastIndexOf("/");
            System.out.println(href.substring(index+1));

            element.attr("href",href.substring(index+1));
        }

        System.out.println(document.outerHtml());
    }

    @Test
    public void testHtml() throws IOException {
        String htmlPath = "/Users/<USER>/Documents/Test/epub/信心加油站-简-加引用-201117/OEBPS/Text/74.xhtml";
        File file = new File(htmlPath);
        String html = FileUtils.readFileToString(file, Charset.defaultCharset());

        System.out.println(html);

        Document document = Jsoup.parse(html);
        //像js一样，通过标签获取title
        System.out.println(document.getElementsByTag("title").first());
        //像js一样，通过id 获取文章列表元素对象
        Element postList = document.getElementById("post_list");
        //像js一样，通过class 获取列表下的所有博客
        Elements postItems = postList.getElementsByClass("post_item");
        //循环处理每篇博客
        for (Element postItem : postItems) {
            //像jquery选择器一样，获取文章标题元素
            Elements titleEle = postItem.select(".post_item_body a[class='titlelnk']");
            System.out.println("文章标题:" + titleEle.text());;
            System.out.println("文章地址:" + titleEle.attr("href"));
            //像jquery选择器一样，获取文章作者元素
            Elements footEle = postItem.select(".post_item_foot a[class='lightblue']");
            System.out.println("文章作者:" + footEle.text());;
            System.out.println("作者主页:" + footEle.attr("href"));
            System.out.println("*********************************");
        }
    }

    @Test
    public void testHeader() throws IOException {
        String htmlPath = "/Users/<USER>/Documents/Test/epub/信心加油站-简-加引用-201117/OEBPS/Text/74.xhtml";
        File file = new File(htmlPath);
        String html = FileUtils.readFileToString(file, Charset.defaultCharset());

        //System.out.println(html);

        Document document = Jsoup.parse(html);
        //document.head().attr("")
        //像js一样，通过标签获取title
        System.out.println(document.getElementsByTag("title").first());

        Element metaTag = new Element("meta");
        metaTag.attributes().add("name","viewport");
        metaTag.attributes().add("content","width=device-width");

        document.head().appendChild(metaTag);

        System.out.println(document.head());

        System.out.println(document.outerHtml());
    }

    @Test
    public void testReplaceHtml() throws IOException {
        String htmlPath = "/Users/<USER>/Documents/Test/epub/信心加油站-简-加引用-201117/OEBPS/Text/74.xhtml";
        File file = new File(htmlPath);
        String html = FileUtils.readFileToString(file, Charset.defaultCharset());

        //System.out.println(html);

        Document document = Jsoup.parse(html);
        //document.head().attr("")
        //像js一样，通过标签获取title
        System.out.println(document.getElementsByTag("title").first());

        Element metaTag = new Element("meta");
        metaTag.attributes().add("name","viewport");
        metaTag.attributes().add("content","width=device-width");

        document.head().appendChild(metaTag);

        System.out.println(document.head());

        System.out.println(document.outerHtml());

        System.out.println("text="+document.body().text());
    }

    @Test
    public void testReplaceHtmlPart() throws IOException {
        String html = "<p>【本書簡介】</p>\n\\n<p>聖經這本書里蘊藏了怎樣的珍寶？該從何讀起，又應如何踐行？且聽格思里博士偕友向你娓娓道來。</p>\\n\\n<p>本書作者採訪了十多位愛主、愛教會的一流福音派學者，他們畢".replaceAll("\n","");
        Document document = Jsoup.parse(html);

        System.out.println("text="+document.body().text());
    }
}
