package com.aaron.spring.common;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;

import org.apache.commons.io.FileUtils;
import org.imgscalr.Scalr;
/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2021/7/23
 * @Modified By:
 */
public class ImgscalrExample {
	public static BufferedImage simpleResizeImage(BufferedImage originalImage, int targetWidth) {
		return Scalr.resize(originalImage, targetWidth);
	}

	/**
	 * <p>获取目标宽度与高度的图片</p>
	 * @param originalImage
	 * @param targetWidth
	 * @param targetHeight
	 * @return java.awt.image.BufferedImage
	 * @since : 2021/7/23
	 **/
	public static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
		return Scalr.resize(originalImage, Scalr.Method.AUTOMATIC, Scalr.Mode.AUTOMATIC, targetWidth, targetHeight, Scalr.OP_ANTIALIAS);
	}

	/**
	 * <p>根据宽度自动生成</p>
	 * @param originalImage
	 * @param targetWidth
	 * @return java.awt.image.BufferedImage
	 * @since : 2021/7/23
	 **/
	public static BufferedImage resizeImageFitWidth(BufferedImage originalImage, int targetWidth) {
		return Scalr.resize(originalImage, Scalr.Method.AUTOMATIC, Scalr.Mode.FIT_TO_WIDTH, targetWidth, 0, Scalr.OP_ANTIALIAS);
	}

	public static void main(String[] args) throws Exception {
//		BufferedImage bufferedImage = ImageIO.read()
		BufferedImage originalImage = ImageIO.read(new File("/Users/<USER>/Movies/dev/pic/cover.jpeg"));

		BufferedImage outputImage = resizeImage(originalImage, 300, 445);
		ImageIO.write(outputImage, "jpg", new File("/Users/<USER>/Movies/dev/pic/cover1.jpeg"));

		BufferedImage outputImage2 = simpleResizeImage(originalImage, 145);
		ImageIO.write(outputImage2, "jpg", new File("/Users/<USER>/Movies/dev/pic/cover2.jpeg"));

		BufferedImage outputImage3 = resizeImageFitWidth(originalImage, 300);
		ImageIO.write(outputImage3, "jpg", new File("/Users/<USER>/Movies/dev/pic/cover3.jpeg"));
//		FileUtils.writeByteArrayToFile(new File("～/Movies/dev/pic/cover1.jpeg"), outputImage.to);
	}
}
