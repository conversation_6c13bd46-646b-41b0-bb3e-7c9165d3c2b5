package com.aaron.spring.common;

import com.aaron.util.AESUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.request.AlipayUserAgreementPageSignRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.alipay.api.response.AlipayUserAgreementPageSignResponse;
import com.alipay.config.AlipayConfig;
import org.junit.Test;
import org.springframework.context.annotation.ScopeMetadata;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/10/18
 * @Modified By:
 */
public class AlipayTest {

	/**
	 * <p>https://github.com/alipay/ams-java-sdk#maven-users</p>
	 * <p>基础支付：https://opendocs.alipay.com/open/270/105899</p>
	 * @param
	 * @return void
	 * @since : 2022/10/18
	 **/
	@Test
	public void testWeek() throws Exception {
		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi-sea-global.alipayplus.com/gateway.do", AlipayConfig.partner,AlipayConfig.KEY_RSA_PRIVATE,"json","GBK",AlipayConfig.KEY_RSA_ALIPAY_PUBLIC,"RSA2");
		AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
		request.setNotifyUrl(AlipayConfig.notify_url);
		request.setReturnUrl(AlipayConfig.return_url);

		JSONObject bizContent = new JSONObject();
		bizContent.put("out_trade_no", "20210817010101004");
		bizContent.put("total_amount", 0.01);
		bizContent.put("subject", "测试商品");
		bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
//bizContent.put("time_expire", "2022-08-01 22:00:00");

//// 商品明细信息，按需传入
JSONArray goodsDetail = new JSONArray();
JSONObject goods1 = new JSONObject();
goods1.put("goods_id", "goodsNo1");
goods1.put("goods_name", "子商品1");
goods1.put("quantity", 1);
goods1.put("price", 0.01);
goodsDetail.add(goods1);
bizContent.put("goods_detail", goodsDetail);

//// 扩展信息，按需传入
JSONObject extendParams = new JSONObject();
extendParams.put("sys_service_provider_id", "2088511833207846");
bizContent.put("extend_params", extendParams);

		request.setBizContent(bizContent.toString());
		AlipayTradePagePayResponse response = alipayClient.pageExecute(request);
		if(response.isSuccess()){
			System.out.println("调用成功");
			System.out.print(response.getBody());
		} else {
			System.out.println("调用失败");
		}
	}

	/**
	 * <p>沙盒测试</p>
	 * <p>https://opendocs.alipay.com/common/02kkv7</p>
	 * @param
	 * @return void
	 * @since : 2022/10/18
	 **/
	@Test
	public void testSandbox() throws Exception {
		//openapi-sea-global.alipayplus.com
		AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipaydev.com/gateway.do","2016072200101XXXX","请复制第1步中生成的密钥中的商家应用私钥","json","utf-8","沙箱环境RSA2支付宝公钥","RSA2");
		AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
		AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
		request.setBizModel(model);
		model.setOutTradeNo(System.currentTimeMillis()+"");
		model.setTotalAmount("88.88");
		model.setSubject("Iphone6 16G");
		AlipayTradePrecreateResponse response = alipayClient.execute(request);
		System.out.print(response.getBody());
		System.out.print(response.getQrCode());
	}

	/**
	 * <p>周期扣款测试</p>
	 * <p>周期扣款 https://opendocs.alipay.com/open/20190319114403226822</p>
	 * <p>https://opendocs.alipay.com/open/00a05b</p>
	 * <p>预授权 https://opendocs.alipay.com/open/20180417160701241302</p>
	 * @param
	 * @return void
	 * @since : 2022/10/18
	 **/
	@Test
	public void testAutoDebt() throws Exception {
		com.alipay.api.AlipayConfig alipayConfig = new com.alipay.api.AlipayConfig();
		alipayConfig.setServerUrl("https://openapi-sea-global.alipayplus.com/gateway.do");
		alipayConfig.setAppId(AlipayConfig.partner);
		alipayConfig.setPrivateKey(AlipayConfig.KEY_RSA_PRIVATE);
		alipayConfig.setFormat("json");
		alipayConfig.setCharset("GBK");
		alipayConfig.setAlipayPublicKey(AlipayConfig.KEY_RSA_ALIPAY_PUBLIC);
		alipayConfig.setSignType("RSA2");
		//构造client
		AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);


		//DefaultAlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
		// 提交数据至支付宝时请使用
		//alipayClient.certificateExecute(request);

		/*
		bizContent={
    "product_code":"CYCLE_PAY_AUTH",//周期扣款场景固定为 CYCLE_PAY_AUTH
    "personal_product_code":"CYCLE_PAY_AUTH_P",//周期扣款产品时必传签约其它产品时无需传入
    "sign_scene":"INDUSTRY|CARRENTAL",//签约场景
    "external_agreement_no":"dk20220712102811111",//商户签约号
    "sign_validity_period":"2m",//当前用户签约请求的协议有效周期
    "access_params":{//接入渠道
        "channel":"ALIPAYAPP"
    },
    "period_rule_params":{//周期管控规则
        "period_type":"DAY",//周期类型枚举值为DAY和MONTH
        "period":"9",//周期数
        "execute_time":"2022-07-13",//商户发起首次扣款的时间
        "single_amount":"0.01",//每次发起扣款时限制的最大金额单位为元
        "total_amount":"0.02",//总金额限制，单位为元
        "total_payments":"2"//总扣款次数
    },
    "identity_params":{// 非必填，用户实名信息参数
        "user_name":"\u5f20\u4e09",
        "cert_no":"61102619921108888",
        "identity_hash":"ac8c238bc68fca1c35933db1efa1d79accc014db5dc42fb3a43c421d47c2dbfa",
        "sign_user_id":"2088202888530893"
    }
}
		* */
		JSONObject bizContent = new JSONObject();
		bizContent.put("out_trade_no", "20210817010101004");
		bizContent.put("total_amount", 0.01);
		bizContent.put("subject", "测试商品");
		bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");

		AlipayUserAgreementPageSignRequest request = new AlipayUserAgreementPageSignRequest();
		request.setBizContent(bizContent.toString());
		// 若想获取跳转链接使用pageExecute GET方式转换二维码可使用 alipayClient.pageExecute(request,"get")
		// 周期扣款场景使用小程序/h5 接口跳转至签约页面时请使用 alipayClient.sdkExecute
		AlipayUserAgreementPageSignResponse response = alipayClient.sdkExecute(request);
	}

	//说明：允许商家在约定日期及其之前5天开始扣款，如：约定扣款日为 20 号，支持商家从 15 至 20 号发起扣款。
}
