package com.aaron.spring.common;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 *
 * @Date: Created in  2018/5/30
 * @Modified By:
 */
public class StringTest {
    @Test
    public void testLength(){
        String str ="2022101722001305221414220631";//9f2279b775994154b22cd3af46785f8d
        System.out.println("length="+str.length());
    }

    @Test
    public void testIndex(){
        String url = "http://localhost:8080/emailSuccess";
        int index = StringUtils.indexOf(url, "email");
        System.out.println("index:"+index);
    }

    @Test
    public void testFormat(){
        String str=String.format("%04d", 12);
        System.out.println(str);

        str = String.format("%.1f", 11.01f);
        System.out.println(str);
    }

    @Test
    public void testSubstring(){
        String str="12345678901";
        System.out.println(str.substring(7));
    }

    @Test
    public void testEqual(){
        String str = null;
        if ("0".equals(str)){
            System.out.println("equal");
        }else {
            System.out.println("not equal");
        }
    }

    @Test
    public void testSplit(){
        String str = "E12345_444";
        String[] strings = str.split("_");
        System.out.println(Arrays.stream(strings).toList());


        str = "E12345";
        strings = str.split("_");
        System.out.println(Arrays.stream(strings).toList());
        System.out.println(strings[0]);
    }

    @Test
    public void testReadFile() throws IOException {
        ///usr/local/lcp/lcpfiles/master
        // ~/Downloads/epubDel.txt
        String filePath = "/Users/<USER>/Downloads/epubDel.txt";
        //String  readText = FileUtils.readFileToString(new File(filePath), "UTF-8");
        //System.out.println("readText:"+readText);
        List<String> list = FileUtils.readLines(new File(filePath), "UTF-8");
        StringBuffer buffer =new StringBuffer();
        int j = 0;
        for (int i = 0; i < list.size(); i++) {
            if (i % 5 == 0){
                buffer.append("rm -rf ");
            }
            buffer.append("\""+list.get(i) + "\" ");
            if (i % 5 == 4 || i == list.size() - 1){
                System.out.println(buffer.toString());
                buffer.delete(0, buffer.length());
                j++;
                if (j % 7 == 0){
                    System.out.println("--------------------------------------------------------");
                }
            }
        }
    }
}
