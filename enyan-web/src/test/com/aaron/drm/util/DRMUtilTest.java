package com.aaron.drm.util;

import com.aaron.drm.model.Encryption;
import com.aaron.drm.model.Licenses;
import com.aaron.drm.model.User;
import com.aaron.drm.model.UserKey;
import com.aaron.http.HttpCredentials;
import com.aaron.http.HttpMethod;
import com.aaron.http.HttpProtocolHandler;
import com.aaron.http.HttpResult;
import com.alibaba.fastjson2.JSONObject;
import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;

import static org.junit.Assert.*;

/**
 *
 *
 * @Date: Created in  2019-06-05
 * @Modified By:
 */
public class DRMUtilTest {

    @Test
    public void getJsonForPublicationByLicenseID() {
        User user = new User();
        Encryption encryption = new Encryption();
        UserKey userKey = new UserKey();

        user.setEmail("<EMAIL>");
        user.setId("111");

        userKey.setTextHint("123");
        userKey.setHexValue("a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3");

        encryption.setUserKey(userKey);

        String jsonString = DRMUtil.getJsonForPublicationByLicenseID(user,encryption);
        System.out.println("json:"+jsonString);
    }
    @Test
    public void testGetLicense(){
        String url = DRMUtil.getApiLicenseByPurchaseId(18);
        url = "http://127.0.0.1:8991/api/v1/purchases/26/license";
        System.out.println("url:"+url);
        //下载模板
        //File file = FileUtils.toFile(new URL(url));

        HttpResult httpResult = HttpProtocolHandler.execute(new HashMap<>(),url, HttpMethod.GET);
        //logger.debug(httpResult.getStringResult());

        if (!httpResult.isSuccess()){
            return ;
        }
        try {
            //System.out.println("result:"+httpResult.getStringResult());
            String result = httpResult.getStringResult();
            Licenses licenses = JSONObject.parseObject(result,Licenses.class);

            return ;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return ;
    }
    @Test
    public void testGetLicenseById(){
        HttpCredentials httpCredentials = new HttpCredentials();
        httpCredentials.setAuthName(DRMUtil.AUTH_LCP_NAME);
        httpCredentials.setAuthPasswd(DRMUtil.AUTH_LCP_PASSWD);

        String url = DRMUtil.getApiLicenseByPurchaseId(18);
        url = "http://127.0.0.1:8989/licenses/b6007c66-94f0-4363-b886-108194c49c4a";
        System.out.println("url:"+url);
        //下载模板
        //File file = FileUtils.toFile(new URL(url));

        com.aaron.drm.model.User user = new com.aaron.drm.model.User();
        Encryption encryption = new Encryption();
        UserKey userKey = new UserKey();

        user.setEmail("<EMAIL>");
        //user.setId("111");

        userKey.setTextHint("您的email？");
        userKey.setHexValue("873cc7daab2a29e0547be4f4b24fbff349a0177c659e53709663d4fd26639915");

        encryption.setUserKey(userKey);

        String jsonString = DRMUtil.getJsonForPublicationByLicenseID(user,encryption);

        //String jsonString = "{\"user\":{\"email\":\"<EMAIL>\",\"encrypted\":[\"email\"]},\"encryption\":{\"user_key\":{\"text_hint\":\"123\",\"hex_value\":\"a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3\"}}}";
        HttpResult httpResult = HttpProtocolHandler.execute(null,url, HttpMethod.POST,httpCredentials,jsonString);
        //logger.debug(httpResult.getStringResult());

        if (!httpResult.isSuccess()){
            return ;
        }
        try {
            System.out.println("result:"+httpResult.getStringResult());
            String result = httpResult.getStringResult();
            Licenses licenses = JSONObject.parseObject(result,Licenses.class);
            System.out.println(licenses);
            return ;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return ;
    }
}