package com.aaron.bible;


import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Description:
 * @Date: Created in  2022/12/8
 * @Modified By:
 */
public class Bible implements Serializable {
	private static final long serialVersionUID = 6166947515482438302L;
	private List<BibleBook> bookList;

	public List<BibleBook> getBookList() {
		return bookList;
	}

	public void setBookList(List<BibleBook> bookList) {
		this.bookList = bookList;
	}
}
